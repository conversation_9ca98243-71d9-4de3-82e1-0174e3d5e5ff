var Rd=Object.defineProperty;var Pd=(e,t,n)=>t in e?Rd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Gi=(e,t,n)=>(Pd(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var Hu={exports:{}},ji={},Qu={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wr=Symbol.for("react.element"),jd=Symbol.for("react.portal"),Ld=Symbol.for("react.fragment"),Od=Symbol.for("react.strict_mode"),zd=Symbol.for("react.profiler"),Ad=Symbol.for("react.provider"),Id=Symbol.for("react.context"),Dd=Symbol.for("react.forward_ref"),Md=Symbol.for("react.suspense"),Bd=Symbol.for("react.memo"),Fd=Symbol.for("react.lazy"),Nl=Symbol.iterator;function Ud(e){return e===null||typeof e!="object"?null:(e=Nl&&e[Nl]||e["@@iterator"],typeof e=="function"?e:null)}var qu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ku=Object.assign,Yu={};function Nn(e,t,n){this.props=e,this.context=t,this.refs=Yu,this.updater=n||qu}Nn.prototype.isReactComponent={};Nn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Xu(){}Xu.prototype=Nn.prototype;function Cs(e,t,n){this.props=e,this.context=t,this.refs=Yu,this.updater=n||qu}var Ns=Cs.prototype=new Xu;Ns.constructor=Cs;Ku(Ns,Nn.prototype);Ns.isPureReactComponent=!0;var Tl=Array.isArray,Gu=Object.prototype.hasOwnProperty,Ts={current:null},Ju={key:!0,ref:!0,__self:!0,__source:!0};function Zu(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Gu.call(t,r)&&!Ju.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:wr,type:e,key:o,ref:s,props:i,_owner:Ts.current}}function $d(e,t){return{$$typeof:wr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Rs(e){return typeof e=="object"&&e!==null&&e.$$typeof===wr}function Wd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Rl=/\/+/g;function Ji(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Wd(""+e.key):t.toString(36)}function Hr(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case wr:case jd:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Ji(s,0):r,Tl(i)?(n="",e!=null&&(n=e.replace(Rl,"$&/")+"/"),Hr(i,t,n,"",function(c){return c})):i!=null&&(Rs(i)&&(i=$d(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Rl,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Tl(e))for(var l=0;l<e.length;l++){o=e[l];var u=r+Ji(o,l);s+=Hr(o,t,n,u,i)}else if(u=Ud(e),typeof u=="function")for(e=u.call(e),l=0;!(o=e.next()).done;)o=o.value,u=r+Ji(o,l++),s+=Hr(o,t,n,u,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Cr(e,t,n){if(e==null)return e;var r=[],i=0;return Hr(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Vd(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ye={current:null},Qr={transition:null},Hd={ReactCurrentDispatcher:ye,ReactCurrentBatchConfig:Qr,ReactCurrentOwner:Ts};function bu(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:Cr,forEach:function(e,t,n){Cr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Cr(e,function(){t++}),t},toArray:function(e){return Cr(e,function(t){return t})||[]},only:function(e){if(!Rs(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=Nn;D.Fragment=Ld;D.Profiler=zd;D.PureComponent=Cs;D.StrictMode=Od;D.Suspense=Md;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hd;D.act=bu;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ku({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Ts.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)Gu.call(t,u)&&!Ju.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&l!==void 0?l[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:wr,type:e.type,key:i,ref:o,props:r,_owner:s}};D.createContext=function(e){return e={$$typeof:Id,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Ad,_context:e},e.Consumer=e};D.createElement=Zu;D.createFactory=function(e){var t=Zu.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:Dd,render:e}};D.isValidElement=Rs;D.lazy=function(e){return{$$typeof:Fd,_payload:{_status:-1,_result:e},_init:Vd}};D.memo=function(e,t){return{$$typeof:Bd,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=Qr.transition;Qr.transition={};try{e()}finally{Qr.transition=t}};D.unstable_act=bu;D.useCallback=function(e,t){return ye.current.useCallback(e,t)};D.useContext=function(e){return ye.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return ye.current.useDeferredValue(e)};D.useEffect=function(e,t){return ye.current.useEffect(e,t)};D.useId=function(){return ye.current.useId()};D.useImperativeHandle=function(e,t,n){return ye.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return ye.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return ye.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return ye.current.useMemo(e,t)};D.useReducer=function(e,t,n){return ye.current.useReducer(e,t,n)};D.useRef=function(e){return ye.current.useRef(e)};D.useState=function(e){return ye.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return ye.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return ye.current.useTransition()};D.version="18.3.1";Qu.exports=D;var I=Qu.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qd=I,qd=Symbol.for("react.element"),Kd=Symbol.for("react.fragment"),Yd=Object.prototype.hasOwnProperty,Xd=Qd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Gd={key:!0,ref:!0,__self:!0,__source:!0};function ea(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Yd.call(t,r)&&!Gd.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:qd,type:e,key:o,ref:s,props:i,_owner:Xd.current}}ji.Fragment=Kd;ji.jsx=ea;ji.jsxs=ea;Hu.exports=ji;var p=Hu.exports,ta={exports:{}},Re={},na={exports:{}},ra={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,z){var A=_.length;_.push(z);e:for(;0<A;){var Q=A-1>>>1,Z=_[Q];if(0<i(Z,z))_[Q]=z,_[A]=Z,A=Q;else break e}}function n(_){return _.length===0?null:_[0]}function r(_){if(_.length===0)return null;var z=_[0],A=_.pop();if(A!==z){_[0]=A;e:for(var Q=0,Z=_.length,Jt=Z>>>1;Q<Jt;){var nt=2*(Q+1)-1,On=_[nt],Be=nt+1,Zt=_[Be];if(0>i(On,A))Be<Z&&0>i(Zt,On)?(_[Q]=Zt,_[Be]=A,Q=Be):(_[Q]=On,_[nt]=A,Q=nt);else if(Be<Z&&0>i(Zt,A))_[Q]=Zt,_[Be]=A,Q=Be;else break e}}return z}function i(_,z){var A=_.sortIndex-z.sortIndex;return A!==0?A:_.id-z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var u=[],c=[],y=1,m=null,h=3,w=!1,x=!1,k=!1,U=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function f(_){for(var z=n(c);z!==null;){if(z.callback===null)r(c);else if(z.startTime<=_)r(c),z.sortIndex=z.expirationTime,t(u,z);else break;z=n(c)}}function g(_){if(k=!1,f(_),!x)if(n(u)!==null)x=!0,Dt(E);else{var z=n(c);z!==null&&Ln(g,z.startTime-_)}}function E(_,z){x=!1,k&&(k=!1,d(T),T=-1),w=!0;var A=h;try{for(f(z),m=n(u);m!==null&&(!(m.expirationTime>z)||_&&!te());){var Q=m.callback;if(typeof Q=="function"){m.callback=null,h=m.priorityLevel;var Z=Q(m.expirationTime<=z);z=e.unstable_now(),typeof Z=="function"?m.callback=Z:m===n(u)&&r(u),f(z)}else r(u);m=n(u)}if(m!==null)var Jt=!0;else{var nt=n(c);nt!==null&&Ln(g,nt.startTime-z),Jt=!1}return Jt}finally{m=null,h=A,w=!1}}var P=!1,R=null,T=-1,O=5,L=-1;function te(){return!(e.unstable_now()-L<O)}function Ke(){if(R!==null){var _=e.unstable_now();L=_;var z=!0;try{z=R(!0,_)}finally{z?Ye():(P=!1,R=null)}}else P=!1}var Ye;if(typeof a=="function")Ye=function(){a(Ke)};else if(typeof MessageChannel<"u"){var It=new MessageChannel,jn=It.port2;It.port1.onmessage=Ke,Ye=function(){jn.postMessage(null)}}else Ye=function(){U(Ke,0)};function Dt(_){R=_,P||(P=!0,Ye())}function Ln(_,z){T=U(function(){_(e.unstable_now())},z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){x||w||(x=!0,Dt(E))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(_){switch(h){case 1:case 2:case 3:var z=3;break;default:z=h}var A=h;h=z;try{return _()}finally{h=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,z){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var A=h;h=_;try{return z()}finally{h=A}},e.unstable_scheduleCallback=function(_,z,A){var Q=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?Q+A:Q):A=Q,_){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=A+Z,_={id:y++,callback:z,priorityLevel:_,startTime:A,expirationTime:Z,sortIndex:-1},A>Q?(_.sortIndex=A,t(c,_),n(u)===null&&_===n(c)&&(k?(d(T),T=-1):k=!0,Ln(g,A-Q))):(_.sortIndex=Z,t(u,_),x||w||(x=!0,Dt(E))),_},e.unstable_shouldYield=te,e.unstable_wrapCallback=function(_){var z=h;return function(){var A=h;h=z;try{return _.apply(this,arguments)}finally{h=A}}}})(ra);na.exports=ra;var Jd=na.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zd=I,Te=Jd;function v(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ia=new Set,nr={};function Xt(e,t){wn(e,t),wn(e+"Capture",t)}function wn(e,t){for(nr[e]=t,e=0;e<t.length;e++)ia.add(t[e])}var ut=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),To=Object.prototype.hasOwnProperty,bd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Pl={},jl={};function ef(e){return To.call(jl,e)?!0:To.call(Pl,e)?!1:bd.test(e)?jl[e]=!0:(Pl[e]=!0,!1)}function tf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function nf(e,t,n,r){if(t===null||typeof t>"u"||tf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ge(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ae={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ae[e]=new ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ae[t]=new ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ae[e]=new ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ae[e]=new ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ae[e]=new ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ae[e]=new ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ae[e]=new ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ae[e]=new ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ae[e]=new ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ps=/[\-:]([a-z])/g;function js(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ps,js);ae[t]=new ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ps,js);ae[t]=new ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ps,js);ae[t]=new ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ae[e]=new ge(e,1,!1,e.toLowerCase(),null,!1,!1)});ae.xlinkHref=new ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ae[e]=new ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ls(e,t,n,r){var i=ae.hasOwnProperty(t)?ae[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(nf(t,n,i,r)&&(n=null),r||i===null?ef(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ft=Zd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Nr=Symbol.for("react.element"),en=Symbol.for("react.portal"),tn=Symbol.for("react.fragment"),Os=Symbol.for("react.strict_mode"),Ro=Symbol.for("react.profiler"),oa=Symbol.for("react.provider"),sa=Symbol.for("react.context"),zs=Symbol.for("react.forward_ref"),Po=Symbol.for("react.suspense"),jo=Symbol.for("react.suspense_list"),As=Symbol.for("react.memo"),mt=Symbol.for("react.lazy"),la=Symbol.for("react.offscreen"),Ll=Symbol.iterator;function zn(e){return e===null||typeof e!="object"?null:(e=Ll&&e[Ll]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,Zi;function Wn(e){if(Zi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zi=t&&t[1]||""}return`
`+Zi+e}var bi=!1;function eo(e,t){if(!e||bi)return"";bi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var u=`
`+i[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=l);break}}}finally{bi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Wn(e):""}function rf(e){switch(e.tag){case 5:return Wn(e.type);case 16:return Wn("Lazy");case 13:return Wn("Suspense");case 19:return Wn("SuspenseList");case 0:case 2:case 15:return e=eo(e.type,!1),e;case 11:return e=eo(e.type.render,!1),e;case 1:return e=eo(e.type,!0),e;default:return""}}function Lo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case tn:return"Fragment";case en:return"Portal";case Ro:return"Profiler";case Os:return"StrictMode";case Po:return"Suspense";case jo:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case sa:return(e.displayName||"Context")+".Consumer";case oa:return(e._context.displayName||"Context")+".Provider";case zs:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case As:return t=e.displayName||null,t!==null?t:Lo(e.type)||"Memo";case mt:t=e._payload,e=e._init;try{return Lo(e(t))}catch{}}return null}function of(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Lo(t);case 8:return t===Os?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function jt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ua(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function sf(e){var t=ua(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Tr(e){e._valueTracker||(e._valueTracker=sf(e))}function aa(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ua(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function si(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Oo(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ol(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=jt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ca(e,t){t=t.checked,t!=null&&Ls(e,"checked",t,!1)}function zo(e,t){ca(e,t);var n=jt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ao(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ao(e,t.type,jt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function zl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ao(e,t,n){(t!=="number"||si(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Vn=Array.isArray;function pn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+jt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Io(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(v(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Al(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(v(92));if(Vn(n)){if(1<n.length)throw Error(v(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:jt(n)}}function da(e,t){var n=jt(t.value),r=jt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Il(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function fa(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Do(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?fa(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Rr,pa=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Rr=Rr||document.createElement("div"),Rr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Rr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function rr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Kn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},lf=["Webkit","ms","Moz","O"];Object.keys(Kn).forEach(function(e){lf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Kn[t]=Kn[e]})});function ha(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Kn.hasOwnProperty(e)&&Kn[e]?(""+t).trim():t+"px"}function ma(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=ha(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var uf=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Mo(e,t){if(t){if(uf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(v(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(v(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(v(61))}if(t.style!=null&&typeof t.style!="object")throw Error(v(62))}}function Bo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Fo=null;function Is(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Uo=null,hn=null,mn=null;function Dl(e){if(e=_r(e)){if(typeof Uo!="function")throw Error(v(280));var t=e.stateNode;t&&(t=Ii(t),Uo(e.stateNode,e.type,t))}}function ya(e){hn?mn?mn.push(e):mn=[e]:hn=e}function ga(){if(hn){var e=hn,t=mn;if(mn=hn=null,Dl(e),t)for(e=0;e<t.length;e++)Dl(t[e])}}function va(e,t){return e(t)}function wa(){}var to=!1;function xa(e,t,n){if(to)return e(t,n);to=!0;try{return va(e,t,n)}finally{to=!1,(hn!==null||mn!==null)&&(wa(),ga())}}function ir(e,t){var n=e.stateNode;if(n===null)return null;var r=Ii(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(v(231,t,typeof n));return n}var $o=!1;if(ut)try{var An={};Object.defineProperty(An,"passive",{get:function(){$o=!0}}),window.addEventListener("test",An,An),window.removeEventListener("test",An,An)}catch{$o=!1}function af(e,t,n,r,i,o,s,l,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(y){this.onError(y)}}var Yn=!1,li=null,ui=!1,Wo=null,cf={onError:function(e){Yn=!0,li=e}};function df(e,t,n,r,i,o,s,l,u){Yn=!1,li=null,af.apply(cf,arguments)}function ff(e,t,n,r,i,o,s,l,u){if(df.apply(this,arguments),Yn){if(Yn){var c=li;Yn=!1,li=null}else throw Error(v(198));ui||(ui=!0,Wo=c)}}function Gt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ka(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ml(e){if(Gt(e)!==e)throw Error(v(188))}function pf(e){var t=e.alternate;if(!t){if(t=Gt(e),t===null)throw Error(v(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Ml(i),e;if(o===r)return Ml(i),t;o=o.sibling}throw Error(v(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(v(189))}}if(n.alternate!==r)throw Error(v(190))}if(n.tag!==3)throw Error(v(188));return n.stateNode.current===n?e:t}function _a(e){return e=pf(e),e!==null?Sa(e):null}function Sa(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Sa(e);if(t!==null)return t;e=e.sibling}return null}var Ea=Te.unstable_scheduleCallback,Bl=Te.unstable_cancelCallback,hf=Te.unstable_shouldYield,mf=Te.unstable_requestPaint,J=Te.unstable_now,yf=Te.unstable_getCurrentPriorityLevel,Ds=Te.unstable_ImmediatePriority,Ca=Te.unstable_UserBlockingPriority,ai=Te.unstable_NormalPriority,gf=Te.unstable_LowPriority,Na=Te.unstable_IdlePriority,Li=null,Ze=null;function vf(e){if(Ze&&typeof Ze.onCommitFiberRoot=="function")try{Ze.onCommitFiberRoot(Li,e,void 0,(e.current.flags&128)===128)}catch{}}var He=Math.clz32?Math.clz32:kf,wf=Math.log,xf=Math.LN2;function kf(e){return e>>>=0,e===0?32:31-(wf(e)/xf|0)|0}var Pr=64,jr=4194304;function Hn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ci(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=Hn(l):(o&=s,o!==0&&(r=Hn(o)))}else s=n&~i,s!==0?r=Hn(s):o!==0&&(r=Hn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-He(t),i=1<<n,r|=e[n],t&=~i;return r}function _f(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Sf(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-He(o),l=1<<s,u=i[s];u===-1?(!(l&n)||l&r)&&(i[s]=_f(l,t)):u<=t&&(e.expiredLanes|=l),o&=~l}}function Vo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ta(){var e=Pr;return Pr<<=1,!(Pr&4194240)&&(Pr=64),e}function no(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function xr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-He(t),e[t]=n}function Ef(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-He(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Ms(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-He(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var F=0;function Ra(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Pa,Bs,ja,La,Oa,Ho=!1,Lr=[],kt=null,_t=null,St=null,or=new Map,sr=new Map,gt=[],Cf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Fl(e,t){switch(e){case"focusin":case"focusout":kt=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":St=null;break;case"pointerover":case"pointerout":or.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":sr.delete(t.pointerId)}}function In(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=_r(t),t!==null&&Bs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Nf(e,t,n,r,i){switch(t){case"focusin":return kt=In(kt,e,t,n,r,i),!0;case"dragenter":return _t=In(_t,e,t,n,r,i),!0;case"mouseover":return St=In(St,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return or.set(o,In(or.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,sr.set(o,In(sr.get(o)||null,e,t,n,r,i)),!0}return!1}function za(e){var t=Ft(e.target);if(t!==null){var n=Gt(t);if(n!==null){if(t=n.tag,t===13){if(t=ka(n),t!==null){e.blockedOn=t,Oa(e.priority,function(){ja(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Fo=r,n.target.dispatchEvent(r),Fo=null}else return t=_r(n),t!==null&&Bs(t),e.blockedOn=n,!1;t.shift()}return!0}function Ul(e,t,n){qr(e)&&n.delete(t)}function Tf(){Ho=!1,kt!==null&&qr(kt)&&(kt=null),_t!==null&&qr(_t)&&(_t=null),St!==null&&qr(St)&&(St=null),or.forEach(Ul),sr.forEach(Ul)}function Dn(e,t){e.blockedOn===t&&(e.blockedOn=null,Ho||(Ho=!0,Te.unstable_scheduleCallback(Te.unstable_NormalPriority,Tf)))}function lr(e){function t(i){return Dn(i,e)}if(0<Lr.length){Dn(Lr[0],e);for(var n=1;n<Lr.length;n++){var r=Lr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(kt!==null&&Dn(kt,e),_t!==null&&Dn(_t,e),St!==null&&Dn(St,e),or.forEach(t),sr.forEach(t),n=0;n<gt.length;n++)r=gt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<gt.length&&(n=gt[0],n.blockedOn===null);)za(n),n.blockedOn===null&&gt.shift()}var yn=ft.ReactCurrentBatchConfig,di=!0;function Rf(e,t,n,r){var i=F,o=yn.transition;yn.transition=null;try{F=1,Fs(e,t,n,r)}finally{F=i,yn.transition=o}}function Pf(e,t,n,r){var i=F,o=yn.transition;yn.transition=null;try{F=4,Fs(e,t,n,r)}finally{F=i,yn.transition=o}}function Fs(e,t,n,r){if(di){var i=Qo(e,t,n,r);if(i===null)po(e,t,r,fi,n),Fl(e,r);else if(Nf(i,e,t,n,r))r.stopPropagation();else if(Fl(e,r),t&4&&-1<Cf.indexOf(e)){for(;i!==null;){var o=_r(i);if(o!==null&&Pa(o),o=Qo(e,t,n,r),o===null&&po(e,t,r,fi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else po(e,t,r,null,n)}}var fi=null;function Qo(e,t,n,r){if(fi=null,e=Is(r),e=Ft(e),e!==null)if(t=Gt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ka(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return fi=e,null}function Aa(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(yf()){case Ds:return 1;case Ca:return 4;case ai:case gf:return 16;case Na:return 536870912;default:return 16}default:return 16}}var wt=null,Us=null,Kr=null;function Ia(){if(Kr)return Kr;var e,t=Us,n=t.length,r,i="value"in wt?wt.value:wt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Kr=i.slice(e,1<r?1-r:void 0)}function Yr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Or(){return!0}function $l(){return!1}function Pe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Or:$l,this.isPropagationStopped=$l,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Or)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Or)},persist:function(){},isPersistent:Or}),t}var Tn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$s=Pe(Tn),kr=X({},Tn,{view:0,detail:0}),jf=Pe(kr),ro,io,Mn,Oi=X({},kr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ws,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Mn&&(Mn&&e.type==="mousemove"?(ro=e.screenX-Mn.screenX,io=e.screenY-Mn.screenY):io=ro=0,Mn=e),ro)},movementY:function(e){return"movementY"in e?e.movementY:io}}),Wl=Pe(Oi),Lf=X({},Oi,{dataTransfer:0}),Of=Pe(Lf),zf=X({},kr,{relatedTarget:0}),oo=Pe(zf),Af=X({},Tn,{animationName:0,elapsedTime:0,pseudoElement:0}),If=Pe(Af),Df=X({},Tn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Mf=Pe(Df),Bf=X({},Tn,{data:0}),Vl=Pe(Bf),Ff={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Uf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},$f={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Wf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=$f[e])?!!t[e]:!1}function Ws(){return Wf}var Vf=X({},kr,{key:function(e){if(e.key){var t=Ff[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Yr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Uf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ws,charCode:function(e){return e.type==="keypress"?Yr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Yr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Hf=Pe(Vf),Qf=X({},Oi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hl=Pe(Qf),qf=X({},kr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ws}),Kf=Pe(qf),Yf=X({},Tn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Xf=Pe(Yf),Gf=X({},Oi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Jf=Pe(Gf),Zf=[9,13,27,32],Vs=ut&&"CompositionEvent"in window,Xn=null;ut&&"documentMode"in document&&(Xn=document.documentMode);var bf=ut&&"TextEvent"in window&&!Xn,Da=ut&&(!Vs||Xn&&8<Xn&&11>=Xn),Ql=String.fromCharCode(32),ql=!1;function Ma(e,t){switch(e){case"keyup":return Zf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ba(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var nn=!1;function ep(e,t){switch(e){case"compositionend":return Ba(t);case"keypress":return t.which!==32?null:(ql=!0,Ql);case"textInput":return e=t.data,e===Ql&&ql?null:e;default:return null}}function tp(e,t){if(nn)return e==="compositionend"||!Vs&&Ma(e,t)?(e=Ia(),Kr=Us=wt=null,nn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Da&&t.locale!=="ko"?null:t.data;default:return null}}var np={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Kl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!np[e.type]:t==="textarea"}function Fa(e,t,n,r){ya(r),t=pi(t,"onChange"),0<t.length&&(n=new $s("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Gn=null,ur=null;function rp(e){Ga(e,0)}function zi(e){var t=sn(e);if(aa(t))return e}function ip(e,t){if(e==="change")return t}var Ua=!1;if(ut){var so;if(ut){var lo="oninput"in document;if(!lo){var Yl=document.createElement("div");Yl.setAttribute("oninput","return;"),lo=typeof Yl.oninput=="function"}so=lo}else so=!1;Ua=so&&(!document.documentMode||9<document.documentMode)}function Xl(){Gn&&(Gn.detachEvent("onpropertychange",$a),ur=Gn=null)}function $a(e){if(e.propertyName==="value"&&zi(ur)){var t=[];Fa(t,ur,e,Is(e)),xa(rp,t)}}function op(e,t,n){e==="focusin"?(Xl(),Gn=t,ur=n,Gn.attachEvent("onpropertychange",$a)):e==="focusout"&&Xl()}function sp(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return zi(ur)}function lp(e,t){if(e==="click")return zi(t)}function up(e,t){if(e==="input"||e==="change")return zi(t)}function ap(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qe=typeof Object.is=="function"?Object.is:ap;function ar(e,t){if(qe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!To.call(t,i)||!qe(e[i],t[i]))return!1}return!0}function Gl(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jl(e,t){var n=Gl(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Gl(n)}}function Wa(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Wa(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Va(){for(var e=window,t=si();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=si(e.document)}return t}function Hs(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function cp(e){var t=Va(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Wa(n.ownerDocument.documentElement,n)){if(r!==null&&Hs(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Jl(n,o);var s=Jl(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var dp=ut&&"documentMode"in document&&11>=document.documentMode,rn=null,qo=null,Jn=null,Ko=!1;function Zl(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ko||rn==null||rn!==si(r)||(r=rn,"selectionStart"in r&&Hs(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Jn&&ar(Jn,r)||(Jn=r,r=pi(qo,"onSelect"),0<r.length&&(t=new $s("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rn)))}function zr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var on={animationend:zr("Animation","AnimationEnd"),animationiteration:zr("Animation","AnimationIteration"),animationstart:zr("Animation","AnimationStart"),transitionend:zr("Transition","TransitionEnd")},uo={},Ha={};ut&&(Ha=document.createElement("div").style,"AnimationEvent"in window||(delete on.animationend.animation,delete on.animationiteration.animation,delete on.animationstart.animation),"TransitionEvent"in window||delete on.transitionend.transition);function Ai(e){if(uo[e])return uo[e];if(!on[e])return e;var t=on[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ha)return uo[e]=t[n];return e}var Qa=Ai("animationend"),qa=Ai("animationiteration"),Ka=Ai("animationstart"),Ya=Ai("transitionend"),Xa=new Map,bl="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ot(e,t){Xa.set(e,t),Xt(t,[e])}for(var ao=0;ao<bl.length;ao++){var co=bl[ao],fp=co.toLowerCase(),pp=co[0].toUpperCase()+co.slice(1);Ot(fp,"on"+pp)}Ot(Qa,"onAnimationEnd");Ot(qa,"onAnimationIteration");Ot(Ka,"onAnimationStart");Ot("dblclick","onDoubleClick");Ot("focusin","onFocus");Ot("focusout","onBlur");Ot(Ya,"onTransitionEnd");wn("onMouseEnter",["mouseout","mouseover"]);wn("onMouseLeave",["mouseout","mouseover"]);wn("onPointerEnter",["pointerout","pointerover"]);wn("onPointerLeave",["pointerout","pointerover"]);Xt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),hp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Qn));function eu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ff(r,t,void 0,e),e.currentTarget=null}function Ga(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],u=l.instance,c=l.currentTarget;if(l=l.listener,u!==o&&i.isPropagationStopped())break e;eu(i,l,c),o=u}else for(s=0;s<r.length;s++){if(l=r[s],u=l.instance,c=l.currentTarget,l=l.listener,u!==o&&i.isPropagationStopped())break e;eu(i,l,c),o=u}}}if(ui)throw e=Wo,ui=!1,Wo=null,e}function V(e,t){var n=t[Zo];n===void 0&&(n=t[Zo]=new Set);var r=e+"__bubble";n.has(r)||(Ja(t,e,2,!1),n.add(r))}function fo(e,t,n){var r=0;t&&(r|=4),Ja(n,e,r,t)}var Ar="_reactListening"+Math.random().toString(36).slice(2);function cr(e){if(!e[Ar]){e[Ar]=!0,ia.forEach(function(n){n!=="selectionchange"&&(hp.has(n)||fo(n,!1,e),fo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ar]||(t[Ar]=!0,fo("selectionchange",!1,t))}}function Ja(e,t,n,r){switch(Aa(t)){case 1:var i=Rf;break;case 4:i=Pf;break;default:i=Fs}n=i.bind(null,t,n,e),i=void 0,!$o||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function po(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Ft(l),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}l=l.parentNode}}r=r.return}xa(function(){var c=o,y=Is(n),m=[];e:{var h=Xa.get(e);if(h!==void 0){var w=$s,x=e;switch(e){case"keypress":if(Yr(n)===0)break e;case"keydown":case"keyup":w=Hf;break;case"focusin":x="focus",w=oo;break;case"focusout":x="blur",w=oo;break;case"beforeblur":case"afterblur":w=oo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Wl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Of;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Kf;break;case Qa:case qa:case Ka:w=If;break;case Ya:w=Xf;break;case"scroll":w=jf;break;case"wheel":w=Jf;break;case"copy":case"cut":case"paste":w=Mf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Hl}var k=(t&4)!==0,U=!k&&e==="scroll",d=k?h!==null?h+"Capture":null:h;k=[];for(var a=c,f;a!==null;){f=a;var g=f.stateNode;if(f.tag===5&&g!==null&&(f=g,d!==null&&(g=ir(a,d),g!=null&&k.push(dr(a,g,f)))),U)break;a=a.return}0<k.length&&(h=new w(h,x,null,n,y),m.push({event:h,listeners:k}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&n!==Fo&&(x=n.relatedTarget||n.fromElement)&&(Ft(x)||x[at]))break e;if((w||h)&&(h=y.window===y?y:(h=y.ownerDocument)?h.defaultView||h.parentWindow:window,w?(x=n.relatedTarget||n.toElement,w=c,x=x?Ft(x):null,x!==null&&(U=Gt(x),x!==U||x.tag!==5&&x.tag!==6)&&(x=null)):(w=null,x=c),w!==x)){if(k=Wl,g="onMouseLeave",d="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(k=Hl,g="onPointerLeave",d="onPointerEnter",a="pointer"),U=w==null?h:sn(w),f=x==null?h:sn(x),h=new k(g,a+"leave",w,n,y),h.target=U,h.relatedTarget=f,g=null,Ft(y)===c&&(k=new k(d,a+"enter",x,n,y),k.target=f,k.relatedTarget=U,g=k),U=g,w&&x)t:{for(k=w,d=x,a=0,f=k;f;f=bt(f))a++;for(f=0,g=d;g;g=bt(g))f++;for(;0<a-f;)k=bt(k),a--;for(;0<f-a;)d=bt(d),f--;for(;a--;){if(k===d||d!==null&&k===d.alternate)break t;k=bt(k),d=bt(d)}k=null}else k=null;w!==null&&tu(m,h,w,k,!1),x!==null&&U!==null&&tu(m,U,x,k,!0)}}e:{if(h=c?sn(c):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var E=ip;else if(Kl(h))if(Ua)E=up;else{E=sp;var P=op}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(E=lp);if(E&&(E=E(e,c))){Fa(m,E,n,y);break e}P&&P(e,h,c),e==="focusout"&&(P=h._wrapperState)&&P.controlled&&h.type==="number"&&Ao(h,"number",h.value)}switch(P=c?sn(c):window,e){case"focusin":(Kl(P)||P.contentEditable==="true")&&(rn=P,qo=c,Jn=null);break;case"focusout":Jn=qo=rn=null;break;case"mousedown":Ko=!0;break;case"contextmenu":case"mouseup":case"dragend":Ko=!1,Zl(m,n,y);break;case"selectionchange":if(dp)break;case"keydown":case"keyup":Zl(m,n,y)}var R;if(Vs)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else nn?Ma(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Da&&n.locale!=="ko"&&(nn||T!=="onCompositionStart"?T==="onCompositionEnd"&&nn&&(R=Ia()):(wt=y,Us="value"in wt?wt.value:wt.textContent,nn=!0)),P=pi(c,T),0<P.length&&(T=new Vl(T,e,null,n,y),m.push({event:T,listeners:P}),R?T.data=R:(R=Ba(n),R!==null&&(T.data=R)))),(R=bf?ep(e,n):tp(e,n))&&(c=pi(c,"onBeforeInput"),0<c.length&&(y=new Vl("onBeforeInput","beforeinput",null,n,y),m.push({event:y,listeners:c}),y.data=R))}Ga(m,t)})}function dr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function pi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=ir(e,n),o!=null&&r.unshift(dr(e,o,i)),o=ir(e,t),o!=null&&r.push(dr(e,o,i))),e=e.return}return r}function bt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,u=l.alternate,c=l.stateNode;if(u!==null&&u===r)break;l.tag===5&&c!==null&&(l=c,i?(u=ir(n,o),u!=null&&s.unshift(dr(n,u,l))):i||(u=ir(n,o),u!=null&&s.push(dr(n,u,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var mp=/\r\n?/g,yp=/\u0000|\uFFFD/g;function nu(e){return(typeof e=="string"?e:""+e).replace(mp,`
`).replace(yp,"")}function Ir(e,t,n){if(t=nu(t),nu(e)!==t&&n)throw Error(v(425))}function hi(){}var Yo=null,Xo=null;function Go(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Jo=typeof setTimeout=="function"?setTimeout:void 0,gp=typeof clearTimeout=="function"?clearTimeout:void 0,ru=typeof Promise=="function"?Promise:void 0,vp=typeof queueMicrotask=="function"?queueMicrotask:typeof ru<"u"?function(e){return ru.resolve(null).then(e).catch(wp)}:Jo;function wp(e){setTimeout(function(){throw e})}function ho(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),lr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);lr(t)}function Et(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function iu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Rn=Math.random().toString(36).slice(2),Je="__reactFiber$"+Rn,fr="__reactProps$"+Rn,at="__reactContainer$"+Rn,Zo="__reactEvents$"+Rn,xp="__reactListeners$"+Rn,kp="__reactHandles$"+Rn;function Ft(e){var t=e[Je];if(t)return t;for(var n=e.parentNode;n;){if(t=n[at]||n[Je]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=iu(e);e!==null;){if(n=e[Je])return n;e=iu(e)}return t}e=n,n=e.parentNode}return null}function _r(e){return e=e[Je]||e[at],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function sn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(v(33))}function Ii(e){return e[fr]||null}var bo=[],ln=-1;function zt(e){return{current:e}}function H(e){0>ln||(e.current=bo[ln],bo[ln]=null,ln--)}function W(e,t){ln++,bo[ln]=e.current,e.current=t}var Lt={},pe=zt(Lt),xe=zt(!1),Ht=Lt;function xn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function ke(e){return e=e.childContextTypes,e!=null}function mi(){H(xe),H(pe)}function ou(e,t,n){if(pe.current!==Lt)throw Error(v(168));W(pe,t),W(xe,n)}function Za(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(v(108,of(e)||"Unknown",i));return X({},n,r)}function yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,Ht=pe.current,W(pe,e),W(xe,xe.current),!0}function su(e,t,n){var r=e.stateNode;if(!r)throw Error(v(169));n?(e=Za(e,t,Ht),r.__reactInternalMemoizedMergedChildContext=e,H(xe),H(pe),W(pe,e)):H(xe),W(xe,n)}var it=null,Di=!1,mo=!1;function ba(e){it===null?it=[e]:it.push(e)}function _p(e){Di=!0,ba(e)}function At(){if(!mo&&it!==null){mo=!0;var e=0,t=F;try{var n=it;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}it=null,Di=!1}catch(i){throw it!==null&&(it=it.slice(e+1)),Ea(Ds,At),i}finally{F=t,mo=!1}}return null}var un=[],an=0,gi=null,vi=0,Le=[],Oe=0,Qt=null,ot=1,st="";function Mt(e,t){un[an++]=vi,un[an++]=gi,gi=e,vi=t}function ec(e,t,n){Le[Oe++]=ot,Le[Oe++]=st,Le[Oe++]=Qt,Qt=e;var r=ot;e=st;var i=32-He(r)-1;r&=~(1<<i),n+=1;var o=32-He(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,ot=1<<32-He(t)+i|n<<i|r,st=o+e}else ot=1<<o|n<<i|r,st=e}function Qs(e){e.return!==null&&(Mt(e,1),ec(e,1,0))}function qs(e){for(;e===gi;)gi=un[--an],un[an]=null,vi=un[--an],un[an]=null;for(;e===Qt;)Qt=Le[--Oe],Le[Oe]=null,st=Le[--Oe],Le[Oe]=null,ot=Le[--Oe],Le[Oe]=null}var Ne=null,Ce=null,q=!1,Ve=null;function tc(e,t){var n=Ae(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function lu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ne=e,Ce=Et(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ne=e,Ce=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Qt!==null?{id:ot,overflow:st}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ae(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ne=e,Ce=null,!0):!1;default:return!1}}function es(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ts(e){if(q){var t=Ce;if(t){var n=t;if(!lu(e,t)){if(es(e))throw Error(v(418));t=Et(n.nextSibling);var r=Ne;t&&lu(e,t)?tc(r,n):(e.flags=e.flags&-4097|2,q=!1,Ne=e)}}else{if(es(e))throw Error(v(418));e.flags=e.flags&-4097|2,q=!1,Ne=e}}}function uu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ne=e}function Dr(e){if(e!==Ne)return!1;if(!q)return uu(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Go(e.type,e.memoizedProps)),t&&(t=Ce)){if(es(e))throw nc(),Error(v(418));for(;t;)tc(e,t),t=Et(t.nextSibling)}if(uu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(v(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ce=Et(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ce=null}}else Ce=Ne?Et(e.stateNode.nextSibling):null;return!0}function nc(){for(var e=Ce;e;)e=Et(e.nextSibling)}function kn(){Ce=Ne=null,q=!1}function Ks(e){Ve===null?Ve=[e]:Ve.push(e)}var Sp=ft.ReactCurrentBatchConfig;function Bn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(v(309));var r=n.stateNode}if(!r)throw Error(v(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(v(284));if(!n._owner)throw Error(v(290,e))}return e}function Mr(e,t){throw e=Object.prototype.toString.call(t),Error(v(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function au(e){var t=e._init;return t(e._payload)}function rc(e){function t(d,a){if(e){var f=d.deletions;f===null?(d.deletions=[a],d.flags|=16):f.push(a)}}function n(d,a){if(!e)return null;for(;a!==null;)t(d,a),a=a.sibling;return null}function r(d,a){for(d=new Map;a!==null;)a.key!==null?d.set(a.key,a):d.set(a.index,a),a=a.sibling;return d}function i(d,a){return d=Rt(d,a),d.index=0,d.sibling=null,d}function o(d,a,f){return d.index=f,e?(f=d.alternate,f!==null?(f=f.index,f<a?(d.flags|=2,a):f):(d.flags|=2,a)):(d.flags|=1048576,a)}function s(d){return e&&d.alternate===null&&(d.flags|=2),d}function l(d,a,f,g){return a===null||a.tag!==6?(a=_o(f,d.mode,g),a.return=d,a):(a=i(a,f),a.return=d,a)}function u(d,a,f,g){var E=f.type;return E===tn?y(d,a,f.props.children,g,f.key):a!==null&&(a.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===mt&&au(E)===a.type)?(g=i(a,f.props),g.ref=Bn(d,a,f),g.return=d,g):(g=ti(f.type,f.key,f.props,null,d.mode,g),g.ref=Bn(d,a,f),g.return=d,g)}function c(d,a,f,g){return a===null||a.tag!==4||a.stateNode.containerInfo!==f.containerInfo||a.stateNode.implementation!==f.implementation?(a=So(f,d.mode,g),a.return=d,a):(a=i(a,f.children||[]),a.return=d,a)}function y(d,a,f,g,E){return a===null||a.tag!==7?(a=Vt(f,d.mode,g,E),a.return=d,a):(a=i(a,f),a.return=d,a)}function m(d,a,f){if(typeof a=="string"&&a!==""||typeof a=="number")return a=_o(""+a,d.mode,f),a.return=d,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case Nr:return f=ti(a.type,a.key,a.props,null,d.mode,f),f.ref=Bn(d,null,a),f.return=d,f;case en:return a=So(a,d.mode,f),a.return=d,a;case mt:var g=a._init;return m(d,g(a._payload),f)}if(Vn(a)||zn(a))return a=Vt(a,d.mode,f,null),a.return=d,a;Mr(d,a)}return null}function h(d,a,f,g){var E=a!==null?a.key:null;if(typeof f=="string"&&f!==""||typeof f=="number")return E!==null?null:l(d,a,""+f,g);if(typeof f=="object"&&f!==null){switch(f.$$typeof){case Nr:return f.key===E?u(d,a,f,g):null;case en:return f.key===E?c(d,a,f,g):null;case mt:return E=f._init,h(d,a,E(f._payload),g)}if(Vn(f)||zn(f))return E!==null?null:y(d,a,f,g,null);Mr(d,f)}return null}function w(d,a,f,g,E){if(typeof g=="string"&&g!==""||typeof g=="number")return d=d.get(f)||null,l(a,d,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Nr:return d=d.get(g.key===null?f:g.key)||null,u(a,d,g,E);case en:return d=d.get(g.key===null?f:g.key)||null,c(a,d,g,E);case mt:var P=g._init;return w(d,a,f,P(g._payload),E)}if(Vn(g)||zn(g))return d=d.get(f)||null,y(a,d,g,E,null);Mr(a,g)}return null}function x(d,a,f,g){for(var E=null,P=null,R=a,T=a=0,O=null;R!==null&&T<f.length;T++){R.index>T?(O=R,R=null):O=R.sibling;var L=h(d,R,f[T],g);if(L===null){R===null&&(R=O);break}e&&R&&L.alternate===null&&t(d,R),a=o(L,a,T),P===null?E=L:P.sibling=L,P=L,R=O}if(T===f.length)return n(d,R),q&&Mt(d,T),E;if(R===null){for(;T<f.length;T++)R=m(d,f[T],g),R!==null&&(a=o(R,a,T),P===null?E=R:P.sibling=R,P=R);return q&&Mt(d,T),E}for(R=r(d,R);T<f.length;T++)O=w(R,d,T,f[T],g),O!==null&&(e&&O.alternate!==null&&R.delete(O.key===null?T:O.key),a=o(O,a,T),P===null?E=O:P.sibling=O,P=O);return e&&R.forEach(function(te){return t(d,te)}),q&&Mt(d,T),E}function k(d,a,f,g){var E=zn(f);if(typeof E!="function")throw Error(v(150));if(f=E.call(f),f==null)throw Error(v(151));for(var P=E=null,R=a,T=a=0,O=null,L=f.next();R!==null&&!L.done;T++,L=f.next()){R.index>T?(O=R,R=null):O=R.sibling;var te=h(d,R,L.value,g);if(te===null){R===null&&(R=O);break}e&&R&&te.alternate===null&&t(d,R),a=o(te,a,T),P===null?E=te:P.sibling=te,P=te,R=O}if(L.done)return n(d,R),q&&Mt(d,T),E;if(R===null){for(;!L.done;T++,L=f.next())L=m(d,L.value,g),L!==null&&(a=o(L,a,T),P===null?E=L:P.sibling=L,P=L);return q&&Mt(d,T),E}for(R=r(d,R);!L.done;T++,L=f.next())L=w(R,d,T,L.value,g),L!==null&&(e&&L.alternate!==null&&R.delete(L.key===null?T:L.key),a=o(L,a,T),P===null?E=L:P.sibling=L,P=L);return e&&R.forEach(function(Ke){return t(d,Ke)}),q&&Mt(d,T),E}function U(d,a,f,g){if(typeof f=="object"&&f!==null&&f.type===tn&&f.key===null&&(f=f.props.children),typeof f=="object"&&f!==null){switch(f.$$typeof){case Nr:e:{for(var E=f.key,P=a;P!==null;){if(P.key===E){if(E=f.type,E===tn){if(P.tag===7){n(d,P.sibling),a=i(P,f.props.children),a.return=d,d=a;break e}}else if(P.elementType===E||typeof E=="object"&&E!==null&&E.$$typeof===mt&&au(E)===P.type){n(d,P.sibling),a=i(P,f.props),a.ref=Bn(d,P,f),a.return=d,d=a;break e}n(d,P);break}else t(d,P);P=P.sibling}f.type===tn?(a=Vt(f.props.children,d.mode,g,f.key),a.return=d,d=a):(g=ti(f.type,f.key,f.props,null,d.mode,g),g.ref=Bn(d,a,f),g.return=d,d=g)}return s(d);case en:e:{for(P=f.key;a!==null;){if(a.key===P)if(a.tag===4&&a.stateNode.containerInfo===f.containerInfo&&a.stateNode.implementation===f.implementation){n(d,a.sibling),a=i(a,f.children||[]),a.return=d,d=a;break e}else{n(d,a);break}else t(d,a);a=a.sibling}a=So(f,d.mode,g),a.return=d,d=a}return s(d);case mt:return P=f._init,U(d,a,P(f._payload),g)}if(Vn(f))return x(d,a,f,g);if(zn(f))return k(d,a,f,g);Mr(d,f)}return typeof f=="string"&&f!==""||typeof f=="number"?(f=""+f,a!==null&&a.tag===6?(n(d,a.sibling),a=i(a,f),a.return=d,d=a):(n(d,a),a=_o(f,d.mode,g),a.return=d,d=a),s(d)):n(d,a)}return U}var _n=rc(!0),ic=rc(!1),wi=zt(null),xi=null,cn=null,Ys=null;function Xs(){Ys=cn=xi=null}function Gs(e){var t=wi.current;H(wi),e._currentValue=t}function ns(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function gn(e,t){xi=e,Ys=cn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function De(e){var t=e._currentValue;if(Ys!==e)if(e={context:e,memoizedValue:t,next:null},cn===null){if(xi===null)throw Error(v(308));cn=e,xi.dependencies={lanes:0,firstContext:e}}else cn=cn.next=e;return t}var Ut=null;function Js(e){Ut===null?Ut=[e]:Ut.push(e)}function oc(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Js(t)):(n.next=i.next,i.next=n),t.interleaved=n,ct(e,r)}function ct(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var yt=!1;function Zs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function sc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function lt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ct(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ct(e,n)}return i=r.interleaved,i===null?(t.next=t,Js(r)):(t.next=i.next,i.next=t),r.interleaved=t,ct(e,n)}function Xr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}function cu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ki(e,t,n,r){var i=e.updateQueue;yt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var u=l,c=u.next;u.next=null,s===null?o=c:s.next=c,s=u;var y=e.alternate;y!==null&&(y=y.updateQueue,l=y.lastBaseUpdate,l!==s&&(l===null?y.firstBaseUpdate=c:l.next=c,y.lastBaseUpdate=u))}if(o!==null){var m=i.baseState;s=0,y=c=u=null,l=o;do{var h=l.lane,w=l.eventTime;if((r&h)===h){y!==null&&(y=y.next={eventTime:w,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var x=e,k=l;switch(h=t,w=n,k.tag){case 1:if(x=k.payload,typeof x=="function"){m=x.call(w,m,h);break e}m=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=k.payload,h=typeof x=="function"?x.call(w,m,h):x,h==null)break e;m=X({},m,h);break e;case 2:yt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else w={eventTime:w,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},y===null?(c=y=w,u=m):y=y.next=w,s|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(y===null&&(u=m),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=y,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Kt|=s,e.lanes=s,e.memoizedState=m}}function du(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(v(191,i));i.call(r)}}}var Sr={},be=zt(Sr),pr=zt(Sr),hr=zt(Sr);function $t(e){if(e===Sr)throw Error(v(174));return e}function bs(e,t){switch(W(hr,t),W(pr,e),W(be,Sr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Do(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Do(t,e)}H(be),W(be,t)}function Sn(){H(be),H(pr),H(hr)}function lc(e){$t(hr.current);var t=$t(be.current),n=Do(t,e.type);t!==n&&(W(pr,e),W(be,n))}function el(e){pr.current===e&&(H(be),H(pr))}var K=zt(0);function _i(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var yo=[];function tl(){for(var e=0;e<yo.length;e++)yo[e]._workInProgressVersionPrimary=null;yo.length=0}var Gr=ft.ReactCurrentDispatcher,go=ft.ReactCurrentBatchConfig,qt=0,Y=null,ne=null,ie=null,Si=!1,Zn=!1,mr=0,Ep=0;function ce(){throw Error(v(321))}function nl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qe(e[n],t[n]))return!1;return!0}function rl(e,t,n,r,i,o){if(qt=o,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Gr.current=e===null||e.memoizedState===null?Rp:Pp,e=n(r,i),Zn){o=0;do{if(Zn=!1,mr=0,25<=o)throw Error(v(301));o+=1,ie=ne=null,t.updateQueue=null,Gr.current=jp,e=n(r,i)}while(Zn)}if(Gr.current=Ei,t=ne!==null&&ne.next!==null,qt=0,ie=ne=Y=null,Si=!1,t)throw Error(v(300));return e}function il(){var e=mr!==0;return mr=0,e}function Ge(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ie===null?Y.memoizedState=ie=e:ie=ie.next=e,ie}function Me(){if(ne===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=ie===null?Y.memoizedState:ie.next;if(t!==null)ie=t,ne=e;else{if(e===null)throw Error(v(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},ie===null?Y.memoizedState=ie=e:ie=ie.next=e}return ie}function yr(e,t){return typeof t=="function"?t(e):t}function vo(e){var t=Me(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=ne,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,u=null,c=o;do{var y=c.lane;if((qt&y)===y)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:y,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(l=u=m,s=r):u=u.next=m,Y.lanes|=y,Kt|=y}c=c.next}while(c!==null&&c!==o);u===null?s=r:u.next=l,qe(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,Y.lanes|=o,Kt|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wo(e){var t=Me(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);qe(o,t.memoizedState)||(we=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function uc(){}function ac(e,t){var n=Y,r=Me(),i=t(),o=!qe(r.memoizedState,i);if(o&&(r.memoizedState=i,we=!0),r=r.queue,ol(fc.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||ie!==null&&ie.memoizedState.tag&1){if(n.flags|=2048,gr(9,dc.bind(null,n,r,i,t),void 0,null),oe===null)throw Error(v(349));qt&30||cc(n,t,i)}return i}function cc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function dc(e,t,n,r){t.value=n,t.getSnapshot=r,pc(t)&&hc(e)}function fc(e,t,n){return n(function(){pc(t)&&hc(e)})}function pc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qe(e,n)}catch{return!0}}function hc(e){var t=ct(e,1);t!==null&&Qe(t,e,1,-1)}function fu(e){var t=Ge();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yr,lastRenderedState:e},t.queue=e,e=e.dispatch=Tp.bind(null,Y,e),[t.memoizedState,e]}function gr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function mc(){return Me().memoizedState}function Jr(e,t,n,r){var i=Ge();Y.flags|=e,i.memoizedState=gr(1|t,n,void 0,r===void 0?null:r)}function Mi(e,t,n,r){var i=Me();r=r===void 0?null:r;var o=void 0;if(ne!==null){var s=ne.memoizedState;if(o=s.destroy,r!==null&&nl(r,s.deps)){i.memoizedState=gr(t,n,o,r);return}}Y.flags|=e,i.memoizedState=gr(1|t,n,o,r)}function pu(e,t){return Jr(8390656,8,e,t)}function ol(e,t){return Mi(2048,8,e,t)}function yc(e,t){return Mi(4,2,e,t)}function gc(e,t){return Mi(4,4,e,t)}function vc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function wc(e,t,n){return n=n!=null?n.concat([e]):null,Mi(4,4,vc.bind(null,t,e),n)}function sl(){}function xc(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&nl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function kc(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&nl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function _c(e,t,n){return qt&21?(qe(n,t)||(n=Ta(),Y.lanes|=n,Kt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function Cp(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=go.transition;go.transition={};try{e(!1),t()}finally{F=n,go.transition=r}}function Sc(){return Me().memoizedState}function Np(e,t,n){var r=Tt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ec(e))Cc(t,n);else if(n=oc(e,t,n,r),n!==null){var i=me();Qe(n,e,r,i),Nc(n,t,r)}}function Tp(e,t,n){var r=Tt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ec(e))Cc(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,qe(l,s)){var u=t.interleaved;u===null?(i.next=i,Js(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=oc(e,t,i,r),n!==null&&(i=me(),Qe(n,e,r,i),Nc(n,t,r))}}function Ec(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Cc(e,t){Zn=Si=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Nc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Ms(e,n)}}var Ei={readContext:De,useCallback:ce,useContext:ce,useEffect:ce,useImperativeHandle:ce,useInsertionEffect:ce,useLayoutEffect:ce,useMemo:ce,useReducer:ce,useRef:ce,useState:ce,useDebugValue:ce,useDeferredValue:ce,useTransition:ce,useMutableSource:ce,useSyncExternalStore:ce,useId:ce,unstable_isNewReconciler:!1},Rp={readContext:De,useCallback:function(e,t){return Ge().memoizedState=[e,t===void 0?null:t],e},useContext:De,useEffect:pu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Jr(4194308,4,vc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Jr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Jr(4,2,e,t)},useMemo:function(e,t){var n=Ge();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ge();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Np.bind(null,Y,e),[r.memoizedState,e]},useRef:function(e){var t=Ge();return e={current:e},t.memoizedState=e},useState:fu,useDebugValue:sl,useDeferredValue:function(e){return Ge().memoizedState=e},useTransition:function(){var e=fu(!1),t=e[0];return e=Cp.bind(null,e[1]),Ge().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Y,i=Ge();if(q){if(n===void 0)throw Error(v(407));n=n()}else{if(n=t(),oe===null)throw Error(v(349));qt&30||cc(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,pu(fc.bind(null,r,o,e),[e]),r.flags|=2048,gr(9,dc.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ge(),t=oe.identifierPrefix;if(q){var n=st,r=ot;n=(r&~(1<<32-He(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ep++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Pp={readContext:De,useCallback:xc,useContext:De,useEffect:ol,useImperativeHandle:wc,useInsertionEffect:yc,useLayoutEffect:gc,useMemo:kc,useReducer:vo,useRef:mc,useState:function(){return vo(yr)},useDebugValue:sl,useDeferredValue:function(e){var t=Me();return _c(t,ne.memoizedState,e)},useTransition:function(){var e=vo(yr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:uc,useSyncExternalStore:ac,useId:Sc,unstable_isNewReconciler:!1},jp={readContext:De,useCallback:xc,useContext:De,useEffect:ol,useImperativeHandle:wc,useInsertionEffect:yc,useLayoutEffect:gc,useMemo:kc,useReducer:wo,useRef:mc,useState:function(){return wo(yr)},useDebugValue:sl,useDeferredValue:function(e){var t=Me();return ne===null?t.memoizedState=e:_c(t,ne.memoizedState,e)},useTransition:function(){var e=wo(yr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:uc,useSyncExternalStore:ac,useId:Sc,unstable_isNewReconciler:!1};function Ue(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Bi={isMounted:function(e){return(e=e._reactInternals)?Gt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=me(),i=Tt(e),o=lt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Ct(e,o,i),t!==null&&(Qe(t,e,i,r),Xr(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=me(),i=Tt(e),o=lt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Ct(e,o,i),t!==null&&(Qe(t,e,i,r),Xr(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=me(),r=Tt(e),i=lt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ct(e,i,r),t!==null&&(Qe(t,e,r,n),Xr(t,e,r))}};function hu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!ar(n,r)||!ar(i,o):!0}function Tc(e,t,n){var r=!1,i=Lt,o=t.contextType;return typeof o=="object"&&o!==null?o=De(o):(i=ke(t)?Ht:pe.current,r=t.contextTypes,o=(r=r!=null)?xn(e,i):Lt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Bi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function mu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Bi.enqueueReplaceState(t,t.state,null)}function is(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Zs(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=De(o):(o=ke(t)?Ht:pe.current,i.context=xn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(rs(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Bi.enqueueReplaceState(i,i.state,null),ki(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function En(e,t){try{var n="",r=t;do n+=rf(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function xo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function os(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Lp=typeof WeakMap=="function"?WeakMap:Map;function Rc(e,t,n){n=lt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ni||(Ni=!0,ms=r),os(e,t)},n}function Pc(e,t,n){n=lt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){os(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){os(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function yu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Lp;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Qp.bind(null,e,t,n),t.then(e,e))}function gu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=lt(-1,1),t.tag=2,Ct(n,t,1))),n.lanes|=1),e)}var Op=ft.ReactCurrentOwner,we=!1;function he(e,t,n,r){t.child=e===null?ic(t,null,n,r):_n(t,e.child,n,r)}function wu(e,t,n,r,i){n=n.render;var o=t.ref;return gn(t,i),r=rl(e,t,n,r,o,i),n=il(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,dt(e,t,i)):(q&&n&&Qs(t),t.flags|=1,he(e,t,r,i),t.child)}function xu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!hl(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,jc(e,t,o,r,i)):(e=ti(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:ar,n(s,r)&&e.ref===t.ref)return dt(e,t,i)}return t.flags|=1,e=Rt(o,r),e.ref=t.ref,e.return=t,t.child=e}function jc(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(ar(o,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,dt(e,t,i)}return ss(e,t,n,r,i)}function Lc(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},W(fn,Ee),Ee|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,W(fn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,W(fn,Ee),Ee|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,W(fn,Ee),Ee|=r;return he(e,t,i,n),t.child}function Oc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ss(e,t,n,r,i){var o=ke(n)?Ht:pe.current;return o=xn(t,o),gn(t,i),n=rl(e,t,n,r,o,i),r=il(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,dt(e,t,i)):(q&&r&&Qs(t),t.flags|=1,he(e,t,n,i),t.child)}function ku(e,t,n,r,i){if(ke(n)){var o=!0;yi(t)}else o=!1;if(gn(t,i),t.stateNode===null)Zr(e,t),Tc(t,n,r),is(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=De(c):(c=ke(n)?Ht:pe.current,c=xn(t,c));var y=n.getDerivedStateFromProps,m=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function";m||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||u!==c)&&mu(t,s,r,c),yt=!1;var h=t.memoizedState;s.state=h,ki(t,r,s,i),u=t.memoizedState,l!==r||h!==u||xe.current||yt?(typeof y=="function"&&(rs(t,n,y,r),u=t.memoizedState),(l=yt||hu(t,n,l,r,h,u,c))?(m||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,sc(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Ue(t.type,l),s.props=c,m=t.pendingProps,h=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=De(u):(u=ke(n)?Ht:pe.current,u=xn(t,u));var w=n.getDerivedStateFromProps;(y=typeof w=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==m||h!==u)&&mu(t,s,r,u),yt=!1,h=t.memoizedState,s.state=h,ki(t,r,s,i);var x=t.memoizedState;l!==m||h!==x||xe.current||yt?(typeof w=="function"&&(rs(t,n,w,r),x=t.memoizedState),(c=yt||hu(t,n,c,r,h,x,u)||!1)?(y||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return ls(e,t,n,r,o,i)}function ls(e,t,n,r,i,o){Oc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&su(t,n,!1),dt(e,t,o);r=t.stateNode,Op.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=_n(t,e.child,null,o),t.child=_n(t,null,l,o)):he(e,t,l,o),t.memoizedState=r.state,i&&su(t,n,!0),t.child}function zc(e){var t=e.stateNode;t.pendingContext?ou(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ou(e,t.context,!1),bs(e,t.containerInfo)}function _u(e,t,n,r,i){return kn(),Ks(i),t.flags|=256,he(e,t,n,r),t.child}var us={dehydrated:null,treeContext:null,retryLane:0};function as(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ac(e,t,n){var r=t.pendingProps,i=K.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),W(K,i&1),e===null)return ts(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=$i(s,r,0,null),e=Vt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=as(n),t.memoizedState=us,e):ll(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return zp(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Rt(i,u),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Rt(l,o):(o=Vt(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?as(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=us,r}return o=e.child,e=o.sibling,r=Rt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ll(e,t){return t=$i({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Br(e,t,n,r){return r!==null&&Ks(r),_n(t,e.child,null,n),e=ll(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function zp(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=xo(Error(v(422))),Br(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=$i({mode:"visible",children:r.children},i,0,null),o=Vt(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&_n(t,e.child,null,s),t.child.memoizedState=as(s),t.memoizedState=us,o);if(!(t.mode&1))return Br(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(v(419)),r=xo(o,r,void 0),Br(e,t,s,r)}if(l=(s&e.childLanes)!==0,we||l){if(r=oe,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,ct(e,i),Qe(r,e,i,-1))}return pl(),r=xo(Error(v(421))),Br(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=qp.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ce=Et(i.nextSibling),Ne=t,q=!0,Ve=null,e!==null&&(Le[Oe++]=ot,Le[Oe++]=st,Le[Oe++]=Qt,ot=e.id,st=e.overflow,Qt=t),t=ll(t,r.children),t.flags|=4096,t)}function Su(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ns(e.return,t,n)}function ko(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Ic(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(he(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Su(e,n,t);else if(e.tag===19)Su(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(W(K,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&_i(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ko(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&_i(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ko(t,!0,n,null,o);break;case"together":ko(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Zr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function dt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(v(153));if(t.child!==null){for(e=t.child,n=Rt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Rt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ap(e,t,n){switch(t.tag){case 3:zc(t),kn();break;case 5:lc(t);break;case 1:ke(t.type)&&yi(t);break;case 4:bs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;W(wi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(W(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?Ac(e,t,n):(W(K,K.current&1),e=dt(e,t,n),e!==null?e.sibling:null);W(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ic(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),W(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,Lc(e,t,n)}return dt(e,t,n)}var Dc,cs,Mc,Bc;Dc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};cs=function(){};Mc=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,$t(be.current);var o=null;switch(n){case"input":i=Oo(e,i),r=Oo(e,r),o=[];break;case"select":i=X({},i,{value:void 0}),r=X({},r,{value:void 0}),o=[];break;case"textarea":i=Io(e,i),r=Io(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=hi)}Mo(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var l=i[c];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(nr.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(u!=null||l!=null))if(c==="style")if(l){for(s in l)!l.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&l[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,l=l?l.__html:void 0,u!=null&&l!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(nr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&V("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Bc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Fn(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ip(e,t,n){var r=t.pendingProps;switch(qs(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return ke(t.type)&&mi(),de(t),null;case 3:return r=t.stateNode,Sn(),H(xe),H(pe),tl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Dr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ve!==null&&(vs(Ve),Ve=null))),cs(e,t),de(t),null;case 5:el(t);var i=$t(hr.current);if(n=t.type,e!==null&&t.stateNode!=null)Mc(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(v(166));return de(t),null}if(e=$t(be.current),Dr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Je]=t,r[fr]=o,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(i=0;i<Qn.length;i++)V(Qn[i],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":Ol(r,o),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},V("invalid",r);break;case"textarea":Al(r,o),V("invalid",r)}Mo(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&Ir(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&Ir(r.textContent,l,e),i=["children",""+l]):nr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&V("scroll",r)}switch(n){case"input":Tr(r),zl(r,o,!0);break;case"textarea":Tr(r),Il(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=hi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=fa(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Je]=t,e[fr]=r,Dc(e,t,!1,!1),t.stateNode=e;e:{switch(s=Bo(n,r),n){case"dialog":V("cancel",e),V("close",e),i=r;break;case"iframe":case"object":case"embed":V("load",e),i=r;break;case"video":case"audio":for(i=0;i<Qn.length;i++)V(Qn[i],e);i=r;break;case"source":V("error",e),i=r;break;case"img":case"image":case"link":V("error",e),V("load",e),i=r;break;case"details":V("toggle",e),i=r;break;case"input":Ol(e,r),i=Oo(e,r),V("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=X({},r,{value:void 0}),V("invalid",e);break;case"textarea":Al(e,r),i=Io(e,r),V("invalid",e);break;default:i=r}Mo(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var u=l[o];o==="style"?ma(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&pa(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&rr(e,u):typeof u=="number"&&rr(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(nr.hasOwnProperty(o)?u!=null&&o==="onScroll"&&V("scroll",e):u!=null&&Ls(e,o,u,s))}switch(n){case"input":Tr(e),zl(e,r,!1);break;case"textarea":Tr(e),Il(e);break;case"option":r.value!=null&&e.setAttribute("value",""+jt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?pn(e,!!r.multiple,o,!1):r.defaultValue!=null&&pn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=hi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)Bc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(v(166));if(n=$t(hr.current),$t(be.current),Dr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Je]=t,(o=r.nodeValue!==n)&&(e=Ne,e!==null))switch(e.tag){case 3:Ir(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ir(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Je]=t,t.stateNode=r}return de(t),null;case 13:if(H(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Ce!==null&&t.mode&1&&!(t.flags&128))nc(),kn(),t.flags|=98560,o=!1;else if(o=Dr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(v(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(v(317));o[Je]=t}else kn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),o=!1}else Ve!==null&&(vs(Ve),Ve=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?re===0&&(re=3):pl())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return Sn(),cs(e,t),e===null&&cr(t.stateNode.containerInfo),de(t),null;case 10:return Gs(t.type._context),de(t),null;case 17:return ke(t.type)&&mi(),de(t),null;case 19:if(H(K),o=t.memoizedState,o===null)return de(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Fn(o,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=_i(e),s!==null){for(t.flags|=128,Fn(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return W(K,K.current&1|2),t.child}e=e.sibling}o.tail!==null&&J()>Cn&&(t.flags|=128,r=!0,Fn(o,!1),t.lanes=4194304)}else{if(!r)if(e=_i(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Fn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!q)return de(t),null}else 2*J()-o.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,Fn(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=J(),t.sibling=null,n=K.current,W(K,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return fl(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(v(156,t.tag))}function Dp(e,t){switch(qs(t),t.tag){case 1:return ke(t.type)&&mi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Sn(),H(xe),H(pe),tl(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return el(t),null;case 13:if(H(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(v(340));kn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return H(K),null;case 4:return Sn(),null;case 10:return Gs(t.type._context),null;case 22:case 23:return fl(),null;case 24:return null;default:return null}}var Fr=!1,fe=!1,Mp=typeof WeakSet=="function"?WeakSet:Set,N=null;function dn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){G(e,t,r)}else n.current=null}function ds(e,t,n){try{n()}catch(r){G(e,t,r)}}var Eu=!1;function Bp(e,t){if(Yo=di,e=Va(),Hs(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,u=-1,c=0,y=0,m=e,h=null;t:for(;;){for(var w;m!==n||i!==0&&m.nodeType!==3||(l=s+i),m!==o||r!==0&&m.nodeType!==3||(u=s+r),m.nodeType===3&&(s+=m.nodeValue.length),(w=m.firstChild)!==null;)h=m,m=w;for(;;){if(m===e)break t;if(h===n&&++c===i&&(l=s),h===o&&++y===r&&(u=s),(w=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=w}n=l===-1||u===-1?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xo={focusedElem:e,selectionRange:n},di=!1,N=t;N!==null;)if(t=N,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,N=e;else for(;N!==null;){t=N;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var k=x.memoizedProps,U=x.memoizedState,d=t.stateNode,a=d.getSnapshotBeforeUpdate(t.elementType===t.type?k:Ue(t.type,k),U);d.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var f=t.stateNode.containerInfo;f.nodeType===1?f.textContent="":f.nodeType===9&&f.documentElement&&f.removeChild(f.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(v(163))}}catch(g){G(t,t.return,g)}if(e=t.sibling,e!==null){e.return=t.return,N=e;break}N=t.return}return x=Eu,Eu=!1,x}function bn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&ds(t,n,o)}i=i.next}while(i!==r)}}function Fi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function fs(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Fc(e){var t=e.alternate;t!==null&&(e.alternate=null,Fc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Je],delete t[fr],delete t[Zo],delete t[xp],delete t[kp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Uc(e){return e.tag===5||e.tag===3||e.tag===4}function Cu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Uc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ps(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=hi));else if(r!==4&&(e=e.child,e!==null))for(ps(e,t,n),e=e.sibling;e!==null;)ps(e,t,n),e=e.sibling}function hs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(hs(e,t,n),e=e.sibling;e!==null;)hs(e,t,n),e=e.sibling}var le=null,$e=!1;function ht(e,t,n){for(n=n.child;n!==null;)$c(e,t,n),n=n.sibling}function $c(e,t,n){if(Ze&&typeof Ze.onCommitFiberUnmount=="function")try{Ze.onCommitFiberUnmount(Li,n)}catch{}switch(n.tag){case 5:fe||dn(n,t);case 6:var r=le,i=$e;le=null,ht(e,t,n),le=r,$e=i,le!==null&&($e?(e=le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):le.removeChild(n.stateNode));break;case 18:le!==null&&($e?(e=le,n=n.stateNode,e.nodeType===8?ho(e.parentNode,n):e.nodeType===1&&ho(e,n),lr(e)):ho(le,n.stateNode));break;case 4:r=le,i=$e,le=n.stateNode.containerInfo,$e=!0,ht(e,t,n),le=r,$e=i;break;case 0:case 11:case 14:case 15:if(!fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&ds(n,t,s),i=i.next}while(i!==r)}ht(e,t,n);break;case 1:if(!fe&&(dn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){G(n,t,l)}ht(e,t,n);break;case 21:ht(e,t,n);break;case 22:n.mode&1?(fe=(r=fe)||n.memoizedState!==null,ht(e,t,n),fe=r):ht(e,t,n);break;default:ht(e,t,n)}}function Nu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Mp),t.forEach(function(r){var i=Kp.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Fe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:le=l.stateNode,$e=!1;break e;case 3:le=l.stateNode.containerInfo,$e=!0;break e;case 4:le=l.stateNode.containerInfo,$e=!0;break e}l=l.return}if(le===null)throw Error(v(160));$c(o,s,i),le=null,$e=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(c){G(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wc(t,e),t=t.sibling}function Wc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Fe(t,e),Xe(e),r&4){try{bn(3,e,e.return),Fi(3,e)}catch(k){G(e,e.return,k)}try{bn(5,e,e.return)}catch(k){G(e,e.return,k)}}break;case 1:Fe(t,e),Xe(e),r&512&&n!==null&&dn(n,n.return);break;case 5:if(Fe(t,e),Xe(e),r&512&&n!==null&&dn(n,n.return),e.flags&32){var i=e.stateNode;try{rr(i,"")}catch(k){G(e,e.return,k)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&ca(i,o),Bo(l,s);var c=Bo(l,o);for(s=0;s<u.length;s+=2){var y=u[s],m=u[s+1];y==="style"?ma(i,m):y==="dangerouslySetInnerHTML"?pa(i,m):y==="children"?rr(i,m):Ls(i,y,m,c)}switch(l){case"input":zo(i,o);break;case"textarea":da(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var w=o.value;w!=null?pn(i,!!o.multiple,w,!1):h!==!!o.multiple&&(o.defaultValue!=null?pn(i,!!o.multiple,o.defaultValue,!0):pn(i,!!o.multiple,o.multiple?[]:"",!1))}i[fr]=o}catch(k){G(e,e.return,k)}}break;case 6:if(Fe(t,e),Xe(e),r&4){if(e.stateNode===null)throw Error(v(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(k){G(e,e.return,k)}}break;case 3:if(Fe(t,e),Xe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{lr(t.containerInfo)}catch(k){G(e,e.return,k)}break;case 4:Fe(t,e),Xe(e);break;case 13:Fe(t,e),Xe(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(cl=J())),r&4&&Nu(e);break;case 22:if(y=n!==null&&n.memoizedState!==null,e.mode&1?(fe=(c=fe)||y,Fe(t,e),fe=c):Fe(t,e),Xe(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!y&&e.mode&1)for(N=e,y=e.child;y!==null;){for(m=N=y;N!==null;){switch(h=N,w=h.child,h.tag){case 0:case 11:case 14:case 15:bn(4,h,h.return);break;case 1:dn(h,h.return);var x=h.stateNode;if(typeof x.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(k){G(r,n,k)}}break;case 5:dn(h,h.return);break;case 22:if(h.memoizedState!==null){Ru(m);continue}}w!==null?(w.return=h,N=w):Ru(m)}y=y.sibling}e:for(y=null,m=e;;){if(m.tag===5){if(y===null){y=m;try{i=m.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=m.stateNode,u=m.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,l.style.display=ha("display",s))}catch(k){G(e,e.return,k)}}}else if(m.tag===6){if(y===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(k){G(e,e.return,k)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;y===m&&(y=null),m=m.return}y===m&&(y=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Fe(t,e),Xe(e),r&4&&Nu(e);break;case 21:break;default:Fe(t,e),Xe(e)}}function Xe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Uc(n)){var r=n;break e}n=n.return}throw Error(v(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(rr(i,""),r.flags&=-33);var o=Cu(e);hs(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Cu(e);ps(e,l,s);break;default:throw Error(v(161))}}catch(u){G(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Fp(e,t,n){N=e,Vc(e)}function Vc(e,t,n){for(var r=(e.mode&1)!==0;N!==null;){var i=N,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Fr;if(!s){var l=i.alternate,u=l!==null&&l.memoizedState!==null||fe;l=Fr;var c=fe;if(Fr=s,(fe=u)&&!c)for(N=i;N!==null;)s=N,u=s.child,s.tag===22&&s.memoizedState!==null?Pu(i):u!==null?(u.return=s,N=u):Pu(i);for(;o!==null;)N=o,Vc(o),o=o.sibling;N=i,Fr=l,fe=c}Tu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,N=o):Tu(e)}}function Tu(e){for(;N!==null;){var t=N;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:fe||Fi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!fe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ue(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&du(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}du(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var y=c.memoizedState;if(y!==null){var m=y.dehydrated;m!==null&&lr(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(v(163))}fe||t.flags&512&&fs(t)}catch(h){G(t,t.return,h)}}if(t===e){N=null;break}if(n=t.sibling,n!==null){n.return=t.return,N=n;break}N=t.return}}function Ru(e){for(;N!==null;){var t=N;if(t===e){N=null;break}var n=t.sibling;if(n!==null){n.return=t.return,N=n;break}N=t.return}}function Pu(e){for(;N!==null;){var t=N;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Fi(4,t)}catch(u){G(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){G(t,i,u)}}var o=t.return;try{fs(t)}catch(u){G(t,o,u)}break;case 5:var s=t.return;try{fs(t)}catch(u){G(t,s,u)}}}catch(u){G(t,t.return,u)}if(t===e){N=null;break}var l=t.sibling;if(l!==null){l.return=t.return,N=l;break}N=t.return}}var Up=Math.ceil,Ci=ft.ReactCurrentDispatcher,ul=ft.ReactCurrentOwner,Ie=ft.ReactCurrentBatchConfig,B=0,oe=null,b=null,ue=0,Ee=0,fn=zt(0),re=0,vr=null,Kt=0,Ui=0,al=0,er=null,ve=null,cl=0,Cn=1/0,rt=null,Ni=!1,ms=null,Nt=null,Ur=!1,xt=null,Ti=0,tr=0,ys=null,br=-1,ei=0;function me(){return B&6?J():br!==-1?br:br=J()}function Tt(e){return e.mode&1?B&2&&ue!==0?ue&-ue:Sp.transition!==null?(ei===0&&(ei=Ta()),ei):(e=F,e!==0||(e=window.event,e=e===void 0?16:Aa(e.type)),e):1}function Qe(e,t,n,r){if(50<tr)throw tr=0,ys=null,Error(v(185));xr(e,n,r),(!(B&2)||e!==oe)&&(e===oe&&(!(B&2)&&(Ui|=n),re===4&&vt(e,ue)),_e(e,r),n===1&&B===0&&!(t.mode&1)&&(Cn=J()+500,Di&&At()))}function _e(e,t){var n=e.callbackNode;Sf(e,t);var r=ci(e,e===oe?ue:0);if(r===0)n!==null&&Bl(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Bl(n),t===1)e.tag===0?_p(ju.bind(null,e)):ba(ju.bind(null,e)),vp(function(){!(B&6)&&At()}),n=null;else{switch(Ra(r)){case 1:n=Ds;break;case 4:n=Ca;break;case 16:n=ai;break;case 536870912:n=Na;break;default:n=ai}n=Jc(n,Hc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Hc(e,t){if(br=-1,ei=0,B&6)throw Error(v(327));var n=e.callbackNode;if(vn()&&e.callbackNode!==n)return null;var r=ci(e,e===oe?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ri(e,r);else{t=r;var i=B;B|=2;var o=qc();(oe!==e||ue!==t)&&(rt=null,Cn=J()+500,Wt(e,t));do try{Vp();break}catch(l){Qc(e,l)}while(1);Xs(),Ci.current=o,B=i,b!==null?t=0:(oe=null,ue=0,t=re)}if(t!==0){if(t===2&&(i=Vo(e),i!==0&&(r=i,t=gs(e,i))),t===1)throw n=vr,Wt(e,0),vt(e,r),_e(e,J()),n;if(t===6)vt(e,r);else{if(i=e.current.alternate,!(r&30)&&!$p(i)&&(t=Ri(e,r),t===2&&(o=Vo(e),o!==0&&(r=o,t=gs(e,o))),t===1))throw n=vr,Wt(e,0),vt(e,r),_e(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(v(345));case 2:Bt(e,ve,rt);break;case 3:if(vt(e,r),(r&130023424)===r&&(t=cl+500-J(),10<t)){if(ci(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){me(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Jo(Bt.bind(null,e,ve,rt),t);break}Bt(e,ve,rt);break;case 4:if(vt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-He(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Up(r/1960))-r,10<r){e.timeoutHandle=Jo(Bt.bind(null,e,ve,rt),r);break}Bt(e,ve,rt);break;case 5:Bt(e,ve,rt);break;default:throw Error(v(329))}}}return _e(e,J()),e.callbackNode===n?Hc.bind(null,e):null}function gs(e,t){var n=er;return e.current.memoizedState.isDehydrated&&(Wt(e,t).flags|=256),e=Ri(e,t),e!==2&&(t=ve,ve=n,t!==null&&vs(t)),e}function vs(e){ve===null?ve=e:ve.push.apply(ve,e)}function $p(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!qe(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function vt(e,t){for(t&=~al,t&=~Ui,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-He(t),r=1<<n;e[n]=-1,t&=~r}}function ju(e){if(B&6)throw Error(v(327));vn();var t=ci(e,0);if(!(t&1))return _e(e,J()),null;var n=Ri(e,t);if(e.tag!==0&&n===2){var r=Vo(e);r!==0&&(t=r,n=gs(e,r))}if(n===1)throw n=vr,Wt(e,0),vt(e,t),_e(e,J()),n;if(n===6)throw Error(v(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bt(e,ve,rt),_e(e,J()),null}function dl(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(Cn=J()+500,Di&&At())}}function Yt(e){xt!==null&&xt.tag===0&&!(B&6)&&vn();var t=B;B|=1;var n=Ie.transition,r=F;try{if(Ie.transition=null,F=1,e)return e()}finally{F=r,Ie.transition=n,B=t,!(B&6)&&At()}}function fl(){Ee=fn.current,H(fn)}function Wt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,gp(n)),b!==null)for(n=b.return;n!==null;){var r=n;switch(qs(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&mi();break;case 3:Sn(),H(xe),H(pe),tl();break;case 5:el(r);break;case 4:Sn();break;case 13:H(K);break;case 19:H(K);break;case 10:Gs(r.type._context);break;case 22:case 23:fl()}n=n.return}if(oe=e,b=e=Rt(e.current,null),ue=Ee=t,re=0,vr=null,al=Ui=Kt=0,ve=er=null,Ut!==null){for(t=0;t<Ut.length;t++)if(n=Ut[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}Ut=null}return e}function Qc(e,t){do{var n=b;try{if(Xs(),Gr.current=Ei,Si){for(var r=Y.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Si=!1}if(qt=0,ie=ne=Y=null,Zn=!1,mr=0,ul.current=null,n===null||n.return===null){re=1,vr=t,b=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=ue,l.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,y=l,m=y.tag;if(!(y.mode&1)&&(m===0||m===11||m===15)){var h=y.alternate;h?(y.updateQueue=h.updateQueue,y.memoizedState=h.memoizedState,y.lanes=h.lanes):(y.updateQueue=null,y.memoizedState=null)}var w=gu(s);if(w!==null){w.flags&=-257,vu(w,s,l,o,t),w.mode&1&&yu(o,c,t),t=w,u=c;var x=t.updateQueue;if(x===null){var k=new Set;k.add(u),t.updateQueue=k}else x.add(u);break e}else{if(!(t&1)){yu(o,c,t),pl();break e}u=Error(v(426))}}else if(q&&l.mode&1){var U=gu(s);if(U!==null){!(U.flags&65536)&&(U.flags|=256),vu(U,s,l,o,t),Ks(En(u,l));break e}}o=u=En(u,l),re!==4&&(re=2),er===null?er=[o]:er.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var d=Rc(o,u,t);cu(o,d);break e;case 1:l=u;var a=o.type,f=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||f!==null&&typeof f.componentDidCatch=="function"&&(Nt===null||!Nt.has(f)))){o.flags|=65536,t&=-t,o.lanes|=t;var g=Pc(o,l,t);cu(o,g);break e}}o=o.return}while(o!==null)}Yc(n)}catch(E){t=E,b===n&&n!==null&&(b=n=n.return);continue}break}while(1)}function qc(){var e=Ci.current;return Ci.current=Ei,e===null?Ei:e}function pl(){(re===0||re===3||re===2)&&(re=4),oe===null||!(Kt&268435455)&&!(Ui&268435455)||vt(oe,ue)}function Ri(e,t){var n=B;B|=2;var r=qc();(oe!==e||ue!==t)&&(rt=null,Wt(e,t));do try{Wp();break}catch(i){Qc(e,i)}while(1);if(Xs(),B=n,Ci.current=r,b!==null)throw Error(v(261));return oe=null,ue=0,re}function Wp(){for(;b!==null;)Kc(b)}function Vp(){for(;b!==null&&!hf();)Kc(b)}function Kc(e){var t=Gc(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?Yc(e):b=t,ul.current=null}function Yc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Dp(n,t),n!==null){n.flags&=32767,b=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,b=null;return}}else if(n=Ip(n,t,Ee),n!==null){b=n;return}if(t=t.sibling,t!==null){b=t;return}b=t=e}while(t!==null);re===0&&(re=5)}function Bt(e,t,n){var r=F,i=Ie.transition;try{Ie.transition=null,F=1,Hp(e,t,n,r)}finally{Ie.transition=i,F=r}return null}function Hp(e,t,n,r){do vn();while(xt!==null);if(B&6)throw Error(v(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(v(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Ef(e,o),e===oe&&(b=oe=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ur||(Ur=!0,Jc(ai,function(){return vn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ie.transition,Ie.transition=null;var s=F;F=1;var l=B;B|=4,ul.current=null,Bp(e,n),Wc(n,e),cp(Xo),di=!!Yo,Xo=Yo=null,e.current=n,Fp(n),mf(),B=l,F=s,Ie.transition=o}else e.current=n;if(Ur&&(Ur=!1,xt=e,Ti=i),o=e.pendingLanes,o===0&&(Nt=null),vf(n.stateNode),_e(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ni)throw Ni=!1,e=ms,ms=null,e;return Ti&1&&e.tag!==0&&vn(),o=e.pendingLanes,o&1?e===ys?tr++:(tr=0,ys=e):tr=0,At(),null}function vn(){if(xt!==null){var e=Ra(Ti),t=Ie.transition,n=F;try{if(Ie.transition=null,F=16>e?16:e,xt===null)var r=!1;else{if(e=xt,xt=null,Ti=0,B&6)throw Error(v(331));var i=B;for(B|=4,N=e.current;N!==null;){var o=N,s=o.child;if(N.flags&16){var l=o.deletions;if(l!==null){for(var u=0;u<l.length;u++){var c=l[u];for(N=c;N!==null;){var y=N;switch(y.tag){case 0:case 11:case 15:bn(8,y,o)}var m=y.child;if(m!==null)m.return=y,N=m;else for(;N!==null;){y=N;var h=y.sibling,w=y.return;if(Fc(y),y===c){N=null;break}if(h!==null){h.return=w,N=h;break}N=w}}}var x=o.alternate;if(x!==null){var k=x.child;if(k!==null){x.child=null;do{var U=k.sibling;k.sibling=null,k=U}while(k!==null)}}N=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,N=s;else e:for(;N!==null;){if(o=N,o.flags&2048)switch(o.tag){case 0:case 11:case 15:bn(9,o,o.return)}var d=o.sibling;if(d!==null){d.return=o.return,N=d;break e}N=o.return}}var a=e.current;for(N=a;N!==null;){s=N;var f=s.child;if(s.subtreeFlags&2064&&f!==null)f.return=s,N=f;else e:for(s=a;N!==null;){if(l=N,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Fi(9,l)}}catch(E){G(l,l.return,E)}if(l===s){N=null;break e}var g=l.sibling;if(g!==null){g.return=l.return,N=g;break e}N=l.return}}if(B=i,At(),Ze&&typeof Ze.onPostCommitFiberRoot=="function")try{Ze.onPostCommitFiberRoot(Li,e)}catch{}r=!0}return r}finally{F=n,Ie.transition=t}}return!1}function Lu(e,t,n){t=En(n,t),t=Rc(e,t,1),e=Ct(e,t,1),t=me(),e!==null&&(xr(e,1,t),_e(e,t))}function G(e,t,n){if(e.tag===3)Lu(e,e,n);else for(;t!==null;){if(t.tag===3){Lu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=En(n,e),e=Pc(t,e,1),t=Ct(t,e,1),e=me(),t!==null&&(xr(t,1,e),_e(t,e));break}}t=t.return}}function Qp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=me(),e.pingedLanes|=e.suspendedLanes&n,oe===e&&(ue&n)===n&&(re===4||re===3&&(ue&130023424)===ue&&500>J()-cl?Wt(e,0):al|=n),_e(e,t)}function Xc(e,t){t===0&&(e.mode&1?(t=jr,jr<<=1,!(jr&130023424)&&(jr=4194304)):t=1);var n=me();e=ct(e,t),e!==null&&(xr(e,t,n),_e(e,n))}function qp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Xc(e,n)}function Kp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(v(314))}r!==null&&r.delete(t),Xc(e,n)}var Gc;Gc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||xe.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,Ap(e,t,n);we=!!(e.flags&131072)}else we=!1,q&&t.flags&1048576&&ec(t,vi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Zr(e,t),e=t.pendingProps;var i=xn(t,pe.current);gn(t,n),i=rl(null,t,r,e,i,n);var o=il();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ke(r)?(o=!0,yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Zs(t),i.updater=Bi,t.stateNode=i,i._reactInternals=t,is(t,r,e,n),t=ls(null,t,r,!0,o,n)):(t.tag=0,q&&o&&Qs(t),he(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Zr(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Xp(r),e=Ue(r,e),i){case 0:t=ss(null,t,r,e,n);break e;case 1:t=ku(null,t,r,e,n);break e;case 11:t=wu(null,t,r,e,n);break e;case 14:t=xu(null,t,r,Ue(r.type,e),n);break e}throw Error(v(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),ss(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),ku(e,t,r,i,n);case 3:e:{if(zc(t),e===null)throw Error(v(387));r=t.pendingProps,o=t.memoizedState,i=o.element,sc(e,t),ki(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=En(Error(v(423)),t),t=_u(e,t,r,n,i);break e}else if(r!==i){i=En(Error(v(424)),t),t=_u(e,t,r,n,i);break e}else for(Ce=Et(t.stateNode.containerInfo.firstChild),Ne=t,q=!0,Ve=null,n=ic(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(kn(),r===i){t=dt(e,t,n);break e}he(e,t,r,n)}t=t.child}return t;case 5:return lc(t),e===null&&ts(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Go(r,i)?s=null:o!==null&&Go(r,o)&&(t.flags|=32),Oc(e,t),he(e,t,s,n),t.child;case 6:return e===null&&ts(t),null;case 13:return Ac(e,t,n);case 4:return bs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_n(t,null,r,n):he(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),wu(e,t,r,i,n);case 7:return he(e,t,t.pendingProps,n),t.child;case 8:return he(e,t,t.pendingProps.children,n),t.child;case 12:return he(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,W(wi,r._currentValue),r._currentValue=s,o!==null)if(qe(o.value,s)){if(o.children===i.children&&!xe.current){t=dt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var u=l.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=lt(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var y=c.pending;y===null?u.next=u:(u.next=y.next,y.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),ns(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(v(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),ns(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}he(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,gn(t,n),i=De(i),r=r(i),t.flags|=1,he(e,t,r,n),t.child;case 14:return r=t.type,i=Ue(r,t.pendingProps),i=Ue(r.type,i),xu(e,t,r,i,n);case 15:return jc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ue(r,i),Zr(e,t),t.tag=1,ke(r)?(e=!0,yi(t)):e=!1,gn(t,n),Tc(t,r,i),is(t,r,i,n),ls(null,t,r,!0,e,n);case 19:return Ic(e,t,n);case 22:return Lc(e,t,n)}throw Error(v(156,t.tag))};function Jc(e,t){return Ea(e,t)}function Yp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ae(e,t,n,r){return new Yp(e,t,n,r)}function hl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Xp(e){if(typeof e=="function")return hl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===zs)return 11;if(e===As)return 14}return 2}function Rt(e,t){var n=e.alternate;return n===null?(n=Ae(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ti(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")hl(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case tn:return Vt(n.children,i,o,t);case Os:s=8,i|=8;break;case Ro:return e=Ae(12,n,t,i|2),e.elementType=Ro,e.lanes=o,e;case Po:return e=Ae(13,n,t,i),e.elementType=Po,e.lanes=o,e;case jo:return e=Ae(19,n,t,i),e.elementType=jo,e.lanes=o,e;case la:return $i(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case oa:s=10;break e;case sa:s=9;break e;case zs:s=11;break e;case As:s=14;break e;case mt:s=16,r=null;break e}throw Error(v(130,e==null?e:typeof e,""))}return t=Ae(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Vt(e,t,n,r){return e=Ae(7,e,r,t),e.lanes=n,e}function $i(e,t,n,r){return e=Ae(22,e,r,t),e.elementType=la,e.lanes=n,e.stateNode={isHidden:!1},e}function _o(e,t,n){return e=Ae(6,e,null,t),e.lanes=n,e}function So(e,t,n){return t=Ae(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Gp(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=no(0),this.expirationTimes=no(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=no(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ml(e,t,n,r,i,o,s,l,u){return e=new Gp(e,t,n,l,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ae(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zs(o),e}function Jp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:en,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Zc(e){if(!e)return Lt;e=e._reactInternals;e:{if(Gt(e)!==e||e.tag!==1)throw Error(v(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ke(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(v(171))}if(e.tag===1){var n=e.type;if(ke(n))return Za(e,n,t)}return t}function bc(e,t,n,r,i,o,s,l,u){return e=ml(n,r,!0,e,i,o,s,l,u),e.context=Zc(null),n=e.current,r=me(),i=Tt(n),o=lt(r,i),o.callback=t??null,Ct(n,o,i),e.current.lanes=i,xr(e,i,r),_e(e,r),e}function Wi(e,t,n,r){var i=t.current,o=me(),s=Tt(i);return n=Zc(n),t.context===null?t.context=n:t.pendingContext=n,t=lt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ct(i,t,s),e!==null&&(Qe(e,i,s,o),Xr(e,i,s)),s}function Pi(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ou(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function yl(e,t){Ou(e,t),(e=e.alternate)&&Ou(e,t)}function Zp(){return null}var ed=typeof reportError=="function"?reportError:function(e){console.error(e)};function gl(e){this._internalRoot=e}Vi.prototype.render=gl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(v(409));Wi(e,t,null,null)};Vi.prototype.unmount=gl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yt(function(){Wi(null,e,null,null)}),t[at]=null}};function Vi(e){this._internalRoot=e}Vi.prototype.unstable_scheduleHydration=function(e){if(e){var t=La();e={blockedOn:null,target:e,priority:t};for(var n=0;n<gt.length&&t!==0&&t<gt[n].priority;n++);gt.splice(n,0,e),n===0&&za(e)}};function vl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Hi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function zu(){}function bp(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=Pi(s);o.call(c)}}var s=bc(t,r,e,0,null,!1,!1,"",zu);return e._reactRootContainer=s,e[at]=s.current,cr(e.nodeType===8?e.parentNode:e),Yt(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var c=Pi(u);l.call(c)}}var u=ml(e,0,!1,null,null,!1,!1,"",zu);return e._reactRootContainer=u,e[at]=u.current,cr(e.nodeType===8?e.parentNode:e),Yt(function(){Wi(t,u,n,r)}),u}function Qi(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var u=Pi(s);l.call(u)}}Wi(t,s,e,i)}else s=bp(n,t,e,i,r);return Pi(s)}Pa=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Hn(t.pendingLanes);n!==0&&(Ms(t,n|1),_e(t,J()),!(B&6)&&(Cn=J()+500,At()))}break;case 13:Yt(function(){var r=ct(e,1);if(r!==null){var i=me();Qe(r,e,1,i)}}),yl(e,1)}};Bs=function(e){if(e.tag===13){var t=ct(e,134217728);if(t!==null){var n=me();Qe(t,e,134217728,n)}yl(e,134217728)}};ja=function(e){if(e.tag===13){var t=Tt(e),n=ct(e,t);if(n!==null){var r=me();Qe(n,e,t,r)}yl(e,t)}};La=function(){return F};Oa=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Uo=function(e,t,n){switch(t){case"input":if(zo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ii(r);if(!i)throw Error(v(90));aa(r),zo(r,i)}}}break;case"textarea":da(e,n);break;case"select":t=n.value,t!=null&&pn(e,!!n.multiple,t,!1)}};va=dl;wa=Yt;var eh={usingClientEntryPoint:!1,Events:[_r,sn,Ii,ya,ga,dl]},Un={findFiberByHostInstance:Ft,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},th={bundleType:Un.bundleType,version:Un.version,rendererPackageName:Un.rendererPackageName,rendererConfig:Un.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ft.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=_a(e),e===null?null:e.stateNode},findFiberByHostInstance:Un.findFiberByHostInstance||Zp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $r=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$r.isDisabled&&$r.supportsFiber)try{Li=$r.inject(th),Ze=$r}catch{}}Re.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eh;Re.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!vl(t))throw Error(v(200));return Jp(e,t,null,n)};Re.createRoot=function(e,t){if(!vl(e))throw Error(v(299));var n=!1,r="",i=ed;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ml(e,1,!1,null,null,n,!1,r,i),e[at]=t.current,cr(e.nodeType===8?e.parentNode:e),new gl(t)};Re.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(v(188)):(e=Object.keys(e).join(","),Error(v(268,e)));return e=_a(t),e=e===null?null:e.stateNode,e};Re.flushSync=function(e){return Yt(e)};Re.hydrate=function(e,t,n){if(!Hi(t))throw Error(v(200));return Qi(null,e,t,!0,n)};Re.hydrateRoot=function(e,t,n){if(!vl(e))throw Error(v(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=ed;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=bc(t,null,e,1,n??null,i,!1,o,s),e[at]=t.current,cr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Vi(t)};Re.render=function(e,t,n){if(!Hi(t))throw Error(v(200));return Qi(null,e,t,!1,n)};Re.unmountComponentAtNode=function(e){if(!Hi(e))throw Error(v(40));return e._reactRootContainer?(Yt(function(){Qi(null,null,e,!1,function(){e._reactRootContainer=null,e[at]=null})}),!0):!1};Re.unstable_batchedUpdates=dl;Re.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Hi(n))throw Error(v(200));if(e==null||e._reactInternals===void 0)throw Error(v(38));return Qi(e,t,n,!1,r)};Re.version="18.3.1-next-f1338f8080-20240426";function td(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(td)}catch(e){console.error(e)}}td(),ta.exports=Re;var nh=ta.exports,nd,Au=nh;nd=Au.createRoot,Au.hydrateRoot;const tt=Object.create(null);tt.open="0";tt.close="1";tt.ping="2";tt.pong="3";tt.message="4";tt.upgrade="5";tt.noop="6";const ni=Object.create(null);Object.keys(tt).forEach(e=>{ni[tt[e]]=e});const ws={type:"error",data:"parser error"},rd=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",id=typeof ArrayBuffer=="function",od=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,wl=({type:e,data:t},n,r)=>rd&&t instanceof Blob?n?r(t):Iu(t,r):id&&(t instanceof ArrayBuffer||od(t))?n?r(t):Iu(new Blob([t]),r):r(tt[e]+(t||"")),Iu=(e,t)=>{const n=new FileReader;return n.onload=function(){const r=n.result.split(",")[1];t("b"+(r||""))},n.readAsDataURL(e)};function Du(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Eo;function rh(e,t){if(rd&&e.data instanceof Blob)return e.data.arrayBuffer().then(Du).then(t);if(id&&(e.data instanceof ArrayBuffer||od(e.data)))return t(Du(e.data));wl(e,!1,n=>{Eo||(Eo=new TextEncoder),t(Eo.encode(n))})}const Mu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",qn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Mu.length;e++)qn[Mu.charCodeAt(e)]=e;const ih=e=>{let t=e.length*.75,n=e.length,r,i=0,o,s,l,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const c=new ArrayBuffer(t),y=new Uint8Array(c);for(r=0;r<n;r+=4)o=qn[e.charCodeAt(r)],s=qn[e.charCodeAt(r+1)],l=qn[e.charCodeAt(r+2)],u=qn[e.charCodeAt(r+3)],y[i++]=o<<2|s>>4,y[i++]=(s&15)<<4|l>>2,y[i++]=(l&3)<<6|u&63;return c},oh=typeof ArrayBuffer=="function",xl=(e,t)=>{if(typeof e!="string")return{type:"message",data:sd(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:sh(e.substring(1),t)}:ni[n]?e.length>1?{type:ni[n],data:e.substring(1)}:{type:ni[n]}:ws},sh=(e,t)=>{if(oh){const n=ih(e);return sd(n,t)}else return{base64:!0,data:e}},sd=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},ld=String.fromCharCode(30),lh=(e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach((o,s)=>{wl(o,!1,l=>{r[s]=l,++i===n&&t(r.join(ld))})})},uh=(e,t)=>{const n=e.split(ld),r=[];for(let i=0;i<n.length;i++){const o=xl(n[i],t);if(r.push(o),o.type==="error")break}return r};function ah(){return new TransformStream({transform(e,t){rh(e,n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const o=new DataView(i.buffer);o.setUint8(0,126),o.setUint16(1,r)}else{i=new Uint8Array(9);const o=new DataView(i.buffer);o.setUint8(0,127),o.setBigUint64(1,BigInt(r))}e.data&&typeof e.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(n)})}})}let Co;function Wr(e){return e.reduce((t,n)=>t+n.length,0)}function Vr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function ch(e,t){Co||(Co=new TextDecoder);const n=[];let r=0,i=-1,o=!1;return new TransformStream({transform(s,l){for(n.push(s);;){if(r===0){if(Wr(n)<1)break;const u=Vr(n,1);o=(u[0]&128)===128,i=u[0]&127,i<126?r=3:i===126?r=1:r=2}else if(r===1){if(Wr(n)<2)break;const u=Vr(n,2);i=new DataView(u.buffer,u.byteOffset,u.length).getUint16(0),r=3}else if(r===2){if(Wr(n)<8)break;const u=Vr(n,8),c=new DataView(u.buffer,u.byteOffset,u.length),y=c.getUint32(0);if(y>Math.pow(2,53-32)-1){l.enqueue(ws);break}i=y*Math.pow(2,32)+c.getUint32(4),r=3}else{if(Wr(n)<i)break;const u=Vr(n,i);l.enqueue(xl(o?u:Co.decode(u),t)),r=0}if(i===0||i>e){l.enqueue(ws);break}}}})}const ud=4;function ee(e){if(e)return dh(e)}function dh(e){for(var t in ee.prototype)e[t]=ee.prototype[t];return e}ee.prototype.on=ee.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};ee.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};ee.prototype.off=ee.prototype.removeListener=ee.prototype.removeAllListeners=ee.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var r,i=0;i<n.length;i++)if(r=n[i],r===t||r.fn===t){n.splice(i,1);break}return n.length===0&&delete this._callbacks["$"+e],this};ee.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,i=n.length;r<i;++r)n[r].apply(this,t)}return this};ee.prototype.emitReserved=ee.prototype.emit;ee.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};ee.prototype.hasListeners=function(e){return!!this.listeners(e).length};const qi=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0))(),ze=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),fh="arraybuffer";function ad(e,...t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const ph=ze.setTimeout,hh=ze.clearTimeout;function Ki(e,t){t.useNativeTimers?(e.setTimeoutFn=ph.bind(ze),e.clearTimeoutFn=hh.bind(ze)):(e.setTimeoutFn=ze.setTimeout.bind(ze),e.clearTimeoutFn=ze.clearTimeout.bind(ze))}const mh=1.33;function yh(e){return typeof e=="string"?gh(e):Math.ceil((e.byteLength||e.size)*mh)}function gh(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}function cd(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function vh(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function wh(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let o=n[r].split("=");t[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return t}class xh extends Error{constructor(t,n,r){super(t),this.description=n,this.context=r,this.type="TransportError"}}class kl extends ee{constructor(t){super(),this.writable=!1,Ki(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,r){return super.emitReserved("error",new xh(t,n,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=xl(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=vh(t);return n.length?"?"+n:""}}class kh extends kl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||n()})),this.writable||(r++,this.once("drain",function(){--r||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};uh(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,lh(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=cd()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let dd=!1;try{dd=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const _h=dd;function Sh(){}class Eh extends kh{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let r=location.port;r||(r=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,n){const r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(i,o)=>{this.onError("xhr post error",i,o)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,r)=>{this.onError("xhr poll error",n,r)}),this.pollXhr=t}}class et extends ee{constructor(t,n,r){super(),this.createRequest=t,Ki(this,r),this._opts=r,this._method=r.method||"GET",this._uri=n,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const n=ad(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&r.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var i;r.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=et.requestsCount++,et.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Sh,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete et.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}et.requestsCount=0;et.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Bu);else if(typeof addEventListener=="function"){const e="onpagehide"in ze?"pagehide":"unload";addEventListener(e,Bu,!1)}}function Bu(){for(let e in et.requests)et.requests.hasOwnProperty(e)&&et.requests[e].abort()}const Ch=function(){const e=fd({xdomain:!1});return e&&e.responseType!==null}();class Nh extends Eh{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=Ch&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new et(fd,this.uri(),t)}}function fd(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||_h))return new XMLHttpRequest}catch{}if(!t)try{return new ze[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const pd=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Th extends kl{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,r=pd?{}:ad(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,r)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;wl(r,this.supportsBinary,o=>{try{this.doWrite(r,o)}catch{}i&&qi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=cd()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const No=ze.WebSocket||ze.MozWebSocket;class Rh extends Th{createSocket(t,n,r){return pd?new No(t,n,r):n?new No(t,n):new No(t)}doWrite(t,n){this.ws.send(n)}}class Ph extends kl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=ch(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(n).getReader(),i=ah();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const o=()=>{r.read().then(({done:l,value:u})=>{l||(this.onPacket(u),o())}).catch(l=>{})};o();const s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;this._writer.write(r).then(()=>{i&&qi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const jh={websocket:Rh,webtransport:Ph,polling:Nh},Lh=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Oh=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function xs(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");n!=-1&&r!=-1&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=Lh.exec(e||""),o={},s=14;for(;s--;)o[Oh[s]]=i[s]||"";return n!=-1&&r!=-1&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=zh(o,o.path),o.queryKey=Ah(o,o.query),o}function zh(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Ah(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,i,o){i&&(n[i]=o)}),n}const ks=typeof addEventListener=="function"&&typeof removeEventListener=="function",ri=[];ks&&addEventListener("offline",()=>{ri.forEach(e=>e())},!1);class Pt extends ee{constructor(t,n){if(super(),this.binaryType=fh,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const r=xs(t);n.hostname=r.host,n.secure=r.protocol==="https"||r.protocol==="wss",n.port=r.port,r.query&&(n.query=r.query)}else n.host&&(n.hostname=xs(n.host).host);Ki(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(r=>{const i=r.prototype.name;this.transports.push(i),this._transportsByName[i]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=wh(this.opts.query)),ks&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ri.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=ud,n.transport=t,this.id&&(n.sid=this.id);const r=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Pt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",Pt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let r=0;r<this.writeBuffer.length;r++){const i=this.writeBuffer[r].data;if(i&&(n+=yh(i)),r>0&&n>this._maxPayload)return this.writeBuffer.slice(0,r);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,qi(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,r){return this._sendPacket("message",t,n,r),this}send(t,n,r){return this._sendPacket("message",t,n,r),this}_sendPacket(t,n,r,i){if(typeof n=="function"&&(i=n,n=void 0),typeof r=="function"&&(i=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const o={type:t,data:n,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},r=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(Pt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),ks&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=ri.indexOf(this._offlineEventListener);r!==-1&&ri.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}Pt.protocol=ud;class Ih extends Pt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),r=!1;Pt.priorWebsocketSuccess=!1;const i=()=>{r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",m=>{if(!r)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;Pt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(y(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const h=new Error("probe error");h.transport=n.name,this.emitReserved("upgradeError",h)}}))};function o(){r||(r=!0,y(),n.close(),n=null)}const s=m=>{const h=new Error("probe error: "+m);h.transport=n.name,o(),this.emitReserved("upgradeError",h)};function l(){s("transport closed")}function u(){s("socket closed")}function c(m){n&&m.name!==n.name&&o()}const y=()=>{n.removeListener("open",i),n.removeListener("error",s),n.removeListener("close",l),this.off("close",u),this.off("upgrading",c)};n.once("open",i),n.once("error",s),n.once("close",l),this.once("close",u),this.once("upgrading",c),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&n.push(t[r]);return n}}let Dh=class extends Ih{constructor(t,n={}){const r=typeof t=="object"?t:n;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(i=>jh[i]).filter(i=>!!i)),super(t,r)}};function Mh(e,t="",n){let r=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),r=xs(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(n&&n.port===r.port?"":":"+r.port),r}const Bh=typeof ArrayBuffer=="function",Fh=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,hd=Object.prototype.toString,Uh=typeof Blob=="function"||typeof Blob<"u"&&hd.call(Blob)==="[object BlobConstructor]",$h=typeof File=="function"||typeof File<"u"&&hd.call(File)==="[object FileConstructor]";function _l(e){return Bh&&(e instanceof ArrayBuffer||Fh(e))||Uh&&e instanceof Blob||$h&&e instanceof File}function ii(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,r=e.length;n<r;n++)if(ii(e[n]))return!0;return!1}if(_l(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return ii(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&ii(e[n]))return!0;return!1}function Wh(e){const t=[],n=e.data,r=e;return r.data=_s(n,t),r.attachments=t.length,{packet:r,buffers:t}}function _s(e,t){if(!e)return e;if(_l(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=_s(e[r],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=_s(e[r],t));return n}return e}function Vh(e,t){return e.data=Ss(e.data,t),delete e.attachments,e}function Ss(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=Ss(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=Ss(e[n],t));return e}const Hh=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Qh=5;var M;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(M||(M={}));class qh{constructor(t){this.replacer=t}encode(t){return(t.type===M.EVENT||t.type===M.ACK)&&ii(t)?this.encodeAsBinary({type:t.type===M.EVENT?M.BINARY_EVENT:M.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===M.BINARY_EVENT||t.type===M.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=Wh(t),r=this.encodeAsString(n.packet),i=n.buffers;return i.unshift(r),i}}function Fu(e){return Object.prototype.toString.call(e)==="[object Object]"}class Sl extends ee{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const r=n.type===M.BINARY_EVENT;r||n.type===M.BINARY_ACK?(n.type=r?M.EVENT:M.ACK,this.reconstructor=new Kh(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(_l(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const r={type:Number(t.charAt(0))};if(M[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===M.BINARY_EVENT||r.type===M.BINARY_ACK){const o=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const s=t.substring(o,n);if(s!=Number(s)||t.charAt(n)!=="-")throw new Error("Illegal attachments");r.attachments=Number(s)}if(t.charAt(n+1)==="/"){const o=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););r.nsp=t.substring(o,n)}else r.nsp="/";const i=t.charAt(n+1);if(i!==""&&Number(i)==i){const o=n+1;for(;++n;){const s=t.charAt(n);if(s==null||Number(s)!=s){--n;break}if(n===t.length)break}r.id=Number(t.substring(o,n+1))}if(t.charAt(++n)){const o=this.tryParse(t.substr(n));if(Sl.isPayloadValid(r.type,o))r.data=o;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case M.CONNECT:return Fu(n);case M.DISCONNECT:return n===void 0;case M.CONNECT_ERROR:return typeof n=="string"||Fu(n);case M.EVENT:case M.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&Hh.indexOf(n[0])===-1);case M.ACK:case M.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Kh{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=Vh(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Yh=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Sl,Encoder:qh,get PacketType(){return M},protocol:Qh},Symbol.toStringTag,{value:"Module"}));function We(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Xh=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class md extends ee{constructor(t,n,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[We(t,"open",this.onopen.bind(this)),We(t,"packet",this.onpacket.bind(this)),We(t,"error",this.onerror.bind(this)),We(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var r,i,o;if(Xh.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const s={type:M.EVENT,data:n};if(s.options={},s.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const y=this.ids++,m=n.pop();this._registerAckCallback(y,m),s.id=y}const l=(i=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||i===void 0?void 0:i.writable,u=this.connected&&!(!((o=this.io.engine)===null||o===void 0)&&o._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(t,n){var r;const i=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(i===void 0){this.acks[t]=n;return}const o=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===t&&this.sendBuffer.splice(l,1);n.call(this,new Error("operation has timed out"))},i),s=(...l)=>{this.io.clearTimeoutFn(o),n.apply(this,l)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...n){return new Promise((r,i)=>{const o=(s,l)=>s?i(s):r(l);o.withError=!0,n.push(o),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...o)=>r!==this._queue[0]?void 0:(i!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(i)):(this._queue.shift(),n&&n(null,...o)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:M.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case M.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case M.EVENT:case M.BINARY_EVENT:this.onevent(t);break;case M.ACK:case M.BINARY_ACK:this.onack(t);break;case M.DISCONNECT:this.ondisconnect();break;case M.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const r of n)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let r=!1;return function(...i){r||(r=!0,n.packet({type:M.ACK,id:t,data:i}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:M.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const r of n)r.apply(this,t.data)}}}function Pn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Pn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};Pn.prototype.reset=function(){this.attempts=0};Pn.prototype.setMin=function(e){this.ms=e};Pn.prototype.setMax=function(e){this.max=e};Pn.prototype.setJitter=function(e){this.jitter=e};class Es extends ee{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,Ki(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((r=n.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new Pn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const i=n.parser||Yh;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Dh(this.uri,this.opts);const n=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const i=We(n,"open",function(){r.onopen(),t&&t()}),o=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),t?t(l):this.maybeReconnectOnOpen()},s=We(n,"error",o);if(this._timeout!==!1){const l=this._timeout,u=this.setTimeoutFn(()=>{i(),o(new Error("timeout")),n.close()},l);this.opts.autoUnref&&u.unref(),this.subs.push(()=>{this.clearTimeoutFn(u)})}return this.subs.push(i),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(We(t,"ping",this.onping.bind(this)),We(t,"data",this.ondata.bind(this)),We(t,"error",this.onerror.bind(this)),We(t,"close",this.onclose.bind(this)),We(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){qi(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new md(this,t,n),this.nsps[t]=r),r}_destroy(t){const n=Object.keys(this.nsps);for(const r of n)if(this.nsps[r].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let r=0;r<n.length;r++)this.engine.write(n[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},n);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const $n={};function oi(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=Mh(e,t.path||"/socket.io"),r=n.source,i=n.id,o=n.path,s=$n[i]&&o in $n[i].nsps,l=t.forceNew||t["force new connection"]||t.multiplex===!1||s;let u;return l?u=new Es(r,t):($n[i]||($n[i]=new Es(r,t)),u=$n[i]),n.query&&!t.query&&(t.query=n.queryKey),u.socket(n.path,t)}Object.assign(oi,{Manager:Es,Socket:md,io:oi,connect:oi});const Gh=({state:e,isConnected:t})=>{const n=()=>{if(!t)return"avatar-disconnected";switch(e){case"listening":return"avatar-listening";case"speaking":return"avatar-speaking";case"thinking":return"avatar-thinking";default:return"avatar-idle"}};return p.jsxs("div",{className:`avatar-container ${n()}`,children:[p.jsx("div",{className:"avatar-circle",children:p.jsxs("div",{className:"avatar-inner",children:[p.jsx("div",{className:"avatar-ring ring-1"}),p.jsx("div",{className:"avatar-ring ring-2"}),p.jsx("div",{className:"avatar-ring ring-3"}),p.jsxs("div",{className:"avatar-icon",children:[e==="listening"&&p.jsx("svg",{className:"w-12 h-12 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z",clipRule:"evenodd"})}),e==="speaking"&&p.jsx("svg",{className:"w-12 h-12 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.814L4.846 13.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h1.846l3.537-3.314a1 1 0 011.617.814zM15 8a2 2 0 012 2v0a2 2 0 01-2 2 1 1 0 01-1-1V9a1 1 0 011-1z",clipRule:"evenodd"})}),e==="thinking"&&p.jsx("svg",{className:"w-12 h-12 text-yellow-400 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})}),e==="idle"&&p.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z",clipRule:"evenodd"})})]})]})}),p.jsx("div",{className:"avatar-status",children:p.jsx("span",{className:"text-sm font-medium text-white/80",children:t?e==="listening"?"Listening...":e==="speaking"?"Speaking...":e==="thinking"?"Thinking...":"Ready":"Disconnected"})})]})},Jh=({messages:e,isLoading:t})=>{const n=I.useRef(null),r=()=>{var o;(o=n.current)==null||o.scrollIntoView({behavior:"smooth"})};I.useEffect(()=>{r()},[e]);const i=o=>new Date(o).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return p.jsx("div",{className:"flex flex-col h-full",children:p.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 min-h-0",children:[e.length===0?p.jsx("div",{className:"flex items-center justify-center h-full",children:p.jsxs("div",{className:"text-center text-white/60",children:[p.jsx("svg",{className:"w-12 h-12 mx-auto mb-4 opacity-50",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z",clipRule:"evenodd"})}),p.jsx("p",{className:"text-sm",children:"Start a conversation"}),p.jsx("p",{className:"text-xs mt-1",children:"Type a message or use voice input"})]})}):e.map(o=>p.jsx("div",{className:`flex ${o.message_type==="user"?"justify-end":"justify-start"}`,children:p.jsxs("div",{className:`max-w-[80%] rounded-2xl px-4 py-3 ${o.message_type==="user"?"bg-blue-600 text-white":o.message_type==="assistant"?"bg-white/10 text-white border border-white/20":"bg-yellow-600/20 text-yellow-200 border border-yellow-600/30"}`,children:[p.jsx("div",{className:"text-sm leading-relaxed",children:o.content}),p.jsxs("div",{className:`text-xs mt-2 opacity-70 ${o.message_type==="user"?"text-blue-100":"text-white/60"}`,children:[i(o.created_at),o.audio_url&&p.jsx("span",{className:"ml-2",children:"🔊"})]})]})},o.id)),t&&p.jsx("div",{className:"flex justify-start",children:p.jsx("div",{className:"bg-white/10 text-white border border-white/20 rounded-2xl px-4 py-3",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsxs("div",{className:"flex space-x-1",children:[p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce"}),p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),p.jsx("span",{className:"text-sm text-white/80",children:"AI is thinking..."})]})})}),p.jsx("div",{ref:n})]})})};var Zh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const bh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),em=(e,t)=>{const n=I.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,children:l,...u},c)=>I.createElement("svg",{ref:c,...Zh,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:`lucide lucide-${bh(e)}`,...u},[...t.map(([y,m])=>I.createElement(y,m)),...(Array.isArray(l)?l:[l])||[]]));return n.displayName=`${e}`,n};var pt=em;const Uu=pt("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),tm=pt("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),nm=pt("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),rm=pt("Move",[["polyline",{points:"5 9 2 12 5 15",key:"1r5uj5"}],["polyline",{points:"9 5 12 2 15 5",key:"5v383o"}],["polyline",{points:"15 19 12 22 9 19",key:"g7qi8m"}],["polyline",{points:"19 9 22 12 19 15",key:"tpp73q"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}]]),im=pt("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),$u=pt("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),yd=pt("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),gd=pt("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),Wu=pt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),om=({isRecording:e,isPlaying:t,isMuted:n,isProcessing:r=!1,systemActive:i=!0,onToggleMute:o})=>p.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[p.jsxs("div",{className:"flex items-center justify-center space-x-6",children:[p.jsxs("div",{className:"flex flex-col items-center",children:[p.jsx("div",{className:`w-8 h-8 rounded-full mb-2 flex items-center justify-center ${r?"bg-blue-400":e?"bg-green-400 animate-pulse":t?"bg-purple-400 animate-pulse":"bg-gray-400"}`,children:r&&p.jsx(Uu,{className:"w-4 h-4 text-white animate-spin"})}),p.jsx("span",{className:"text-sm text-white/90 font-medium text-center",children:r?"Thinking...":e?"Listening...":t?"Speaking...":"Ready"})]}),p.jsxs("div",{className:"flex flex-col items-center",children:[p.jsx("button",{onClick:o,className:`p-4 rounded-full transition-all duration-300 ${n?"bg-gray-600 hover:bg-gray-700":"bg-purple-500 hover:bg-purple-600 shadow-lg shadow-purple-500/30"}`,children:n?p.jsx(gd,{className:"w-6 h-6 text-white"}):p.jsx(yd,{className:"w-6 h-6 text-white"})}),p.jsx("span",{className:"text-xs text-white/70 font-medium mt-2",children:n?"Unmute":"Mute"})]})]}),i?r?p.jsx("div",{className:"bg-black/20 rounded-lg px-4 py-2 backdrop-blur-sm",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx(Uu,{className:"w-4 h-4 text-blue-400 animate-spin"}),p.jsx("span",{className:"text-sm text-white/90",children:"Thinking..."})]})}):null:p.jsx("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg px-4 py-2 backdrop-blur-sm",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),p.jsx("span",{className:"text-sm text-red-400 font-medium",children:"System Paused"})]})}),p.jsx("div",{className:"text-center",children:p.jsx("p",{className:"text-sm text-white/70 max-w-md",children:i?"Voice-to-voice conversation active. Say your wake word to start talking!":"System is paused - use Control Centre to resume"})})]}),sm=({isExpanded:e,onToggleExpanded:t,isMuted:n,onToggleMute:r,onRefreshChat:i,chatExpanded:o,onToggleChatExpanded:s,userName:l,onUserNameChange:u,systemActive:c,onToggleSystemActive:y,wakeWords:m=[],onAddWakeWord:h,onDeleteWakeWord:w,onToggleWakeWord:x})=>{const[k,U]=I.useState({x:20,y:20}),[d,a]=I.useState(!1),[f,g]=I.useState({x:0,y:0}),[E,P]=I.useState(""),R=I.useRef(null),T=O=>{if(!R.current)return;const L=R.current.getBoundingClientRect();g({x:O.clientX-L.left,y:O.clientY-L.top}),a(!0)};return I.useEffect(()=>{const O=te=>{if(!d)return;const Ke=te.clientX-f.x,Ye=te.clientY-f.y,It=window.innerWidth-400,jn=window.innerHeight-600;U({x:Math.max(0,Math.min(Ke,It)),y:Math.max(0,Math.min(Ye,jn))})},L=()=>{a(!1)};return d&&(document.addEventListener("mousemove",O),document.addEventListener("mouseup",L)),()=>{document.removeEventListener("mousemove",O),document.removeEventListener("mouseup",L)}},[d,f]),e?p.jsxs("div",{ref:R,className:"fixed z-50 bg-black/80 backdrop-blur-md border border-white/20 rounded-xl w-96 max-h-[80vh] overflow-y-auto",style:{left:k.x,top:k.y},children:[p.jsxs("div",{className:"flex items-center justify-between p-6 pb-4 cursor-move",onMouseDown:T,children:[p.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center space-x-2",children:[p.jsx(rm,{className:"w-4 h-4 text-white/60"}),p.jsx($u,{className:"w-5 h-5"}),p.jsx("span",{children:"Control Centre"})]}),p.jsx("button",{onClick:t,className:"text-white/60 hover:text-white transition-colors",children:p.jsx(Wu,{className:"w-5 h-5"})})]}),p.jsxs("div",{className:"px-6 pb-6",children:[p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"User Preferences"}),p.jsxs("div",{className:"space-y-3",children:[p.jsxs("div",{children:[p.jsx("label",{className:"text-xs text-white/60 mb-1 block",children:"What should I call you?"}),p.jsx("input",{type:"text",value:l,onChange:O=>u(O.target.value),placeholder:"Enter your preferred name...",className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400"})]}),p.jsxs("div",{children:[p.jsx("label",{className:"text-xs text-white/60 mb-2 block",children:"Wake Word Management"}),p.jsxs("div",{className:"flex space-x-2 mb-3",children:[p.jsx("input",{type:"text",value:E,onChange:O=>P(O.target.value),placeholder:"Enter new wake word...",className:"flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400",onKeyPress:O=>{O.key==="Enter"&&E.trim()&&h&&(h(E.trim()),P(""))}}),p.jsx("button",{onClick:()=>{E.trim()&&h&&(h(E.trim()),P(""))},disabled:!E.trim(),className:"px-3 py-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-sm",children:"Add"})]}),p.jsxs("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:[m.map(O=>p.jsxs("div",{className:"flex items-center justify-between p-2 bg-white/5 rounded-lg",children:[p.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[p.jsx("button",{onClick:()=>x==null?void 0:x(O.id),className:`w-4 h-4 rounded border-2 flex items-center justify-center ${O.enabled?"bg-green-500 border-green-500":"border-white/30"}`,children:O.enabled&&p.jsx("span",{className:"text-white text-xs",children:"✓"})}),p.jsx("span",{className:`text-sm ${O.enabled?"text-white":"text-white/60"}`,children:O.keyword})]}),p.jsx("button",{onClick:()=>w==null?void 0:w(O.id),className:"text-red-400 hover:text-red-300 p-1",children:p.jsx(Wu,{className:"w-3 h-3"})})]},O.id)),m.length===0&&p.jsx("div",{className:"text-center text-white/60 text-sm py-2",children:"No wake words configured"})]})]})]})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"System Control"}),p.jsxs("button",{onClick:y,className:`w-full flex items-center justify-center space-x-2 p-4 rounded-lg transition-all font-medium ${c?"bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30":"bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30"}`,children:[p.jsx("div",{className:`w-3 h-3 rounded-full ${c?"bg-green-400 animate-pulse":"bg-red-400"}`}),p.jsx("span",{children:c?"System Active - Click to Pause":"System Paused - Click to Resume"})]}),p.jsx("p",{className:"text-xs text-white/60 mt-2 text-center",children:c?"AI is listening and ready to respond":"AI is paused - no processing or responses"})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"Chat Controls"}),p.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[p.jsxs("button",{onClick:r,className:`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all ${n?"bg-red-500/20 border border-red-500/30 text-red-400":"bg-purple-500/20 border border-purple-500/30 text-purple-400"}`,children:[n?p.jsx(gd,{className:"w-4 h-4"}):p.jsx(yd,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:n?"Unmute":"Mute"})]}),p.jsxs("button",{onClick:i,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-blue-500/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 transition-all",children:[p.jsx(im,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:"Refresh"})]}),p.jsxs("button",{onClick:s,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 transition-all col-span-2",children:[o?p.jsx(nm,{className:"w-4 h-4"}):p.jsx(tm,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:o?"Normal Size":"Expand Chat"})]})]})]}),p.jsxs("div",{className:"text-xs text-white/50 leading-relaxed",children:[p.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3",children:[p.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[p.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),p.jsx("span",{className:"text-blue-300 font-medium text-xs",children:"OpenWakeWord System"})]}),p.jsx("p",{className:"text-xs text-white/70",children:"Now using advanced AI-powered wake word detection with no API keys required."})]}),p.jsxs("div",{className:"space-y-2",children:[p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Wake Words:"})," Say any enabled wake word to start voice recording."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Auto-Stop:"})," Recording stops automatically after 1.5 seconds of silence."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Sensitivity:"})," Higher values detect wake words more easily but may increase false positives."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Available Models:"}),' "hey jarvis", "alexa", "hey mycroft", "hey rhasspy"']})]})]})]})]}):p.jsx("div",{className:"fixed z-50 cursor-move",style:{left:k.x,top:k.y},onMouseDown:T,children:p.jsx("button",{onClick:t,className:"bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 text-white/80 hover:bg-black/30 transition-all duration-300",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx($u,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:"Control Centre"})]})})})};class Vu{constructor(t){Gi(this,"config");Gi(this,"sessionPath");this.config=t,this.sessionPath=`projects/${t.projectId}/agent/sessions/${t.sessionId}`}async detectIntent(t){var n,r;try{const i=await fetch("/api/dialogflow/detect-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionPath:this.sessionPath,queryInput:{text:{text:t,languageCode:this.config.languageCode}}})});if(!i.ok)throw new Error(`DialogFlow API error: ${i.statusText}`);const o=await i.json();return{queryText:o.queryResult.queryText,fulfillmentText:o.queryResult.fulfillmentText,intent:{name:((n=o.queryResult.intent)==null?void 0:n.name)||"",displayName:((r=o.queryResult.intent)==null?void 0:r.displayName)||"",confidence:o.queryResult.intentDetectionConfidence||0},parameters:o.queryResult.parameters||{},contexts:o.queryResult.outputContexts||[],action:o.queryResult.action||""}}catch(i){return console.error("DialogFlow detection error:",i),null}}async canHandleQuickly(t){try{const n=await this.detectIntent(t);return n?n.intent.confidence>.8&&n.fulfillmentText.length>0&&this.isQuickResponseIntent(n.intent.displayName):!1}catch(n){return console.error("DialogFlow quick check error:",n),!1}}isQuickResponseIntent(t){return["Default Welcome Intent","Default Fallback Intent","greeting","goodbye","thanks","help","what_can_you_do","how_are_you","tell_joke","weather","time","date","small_talk","encouragement","support","compliment","motivation"].some(r=>t.toLowerCase().includes(r.toLowerCase()))}async getContextualResponse(t,n=[]){try{const r=this.addConversationContext(t,n);return await this.detectIntent(r)}catch(r){return console.error("DialogFlow contextual response error:",r),null}}addConversationContext(t,n){return n.length===0?t:`Context: ${n.slice(-3).join(" ")}. Current query: ${t}`}static generateSessionId(){return`session-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}updateSession(t){console.log("Updated session contexts:",t)}needsAIEnhancement(t){return t.intent.confidence<.7||t.fulfillmentText.length<10||t.action==="input.unknown"||t.intent.displayName.includes("Fallback")}getSuggestedFollowUps(t){const n={greeting:["How are you feeling today?","What would you like to talk about?","Is there anything I can help you with?"],support:["Would you like to talk more about that?","How can I best support you right now?","What would make you feel better?"],help:["What specific area would you like help with?","Can you tell me more about what you need?","Would you like some suggestions?"],motivation:["What goals are you working towards?","What challenges are you facing?","How can I encourage you today?"]},r=Object.keys(n).find(i=>t.intent.displayName.toLowerCase().includes(i));return r?n[r]:["Is there anything else I can help with?","How are you feeling about that?","Would you like to explore this topic more?"]}}const lm=()=>{const[e,t]=I.useState(null),[n,r]=I.useState(!1),[i,o]=I.useState(null),[s,l]=I.useState([]),[u,c]=I.useState(!1),[y,m]=I.useState(!1),[h,w]=I.useState(!1),[x,k]=I.useState(!1),[U,d]=I.useState("idle"),[a,f]=I.useState(!1),[g,E]=I.useState(""),[P,R]=I.useState("hey assistant"),[T,O]=I.useState(!0),[L,te]=I.useState([]),[Ke,Ye]=I.useState(!1),[It,jn]=I.useState(!1),[Dt,Ln]=I.useState(!1),_=I.useRef(null),z=I.useRef(null),A=I.useRef(null);I.useEffect(()=>{const C=oi("http://localhost:5002",{transports:["websocket"]});return C.on("connect",()=>{console.log("🔗 Connected to server"),r(!0),console.log("🎤 Starting OpenAI Realtime conversation..."),C.emit("start_realtime_conversation",{conversation_id:null})}),C.on("disconnect",()=>{console.log("🔌 Disconnected from server"),r(!1)}),C.on("realtime_connected",S=>{console.log("✅ OpenAI Realtime API connected:",S),r(!0)}),C.on("ai_greeting",S=>{console.log("🎤 AI Greeting:",S.message);const j={id:Date.now(),content:S.message,message_type:"assistant",created_at:new Date().toISOString()};l($=>[...$,j]),d("speaking")}),C.on("transcript_received",S=>{console.log("📝 Transcript received:",S.transcript);const j={id:S.message_id,content:S.transcript,message_type:"user",created_at:new Date().toISOString()};l($=>[...$,j])}),C.on("response_complete",()=>{console.log("✅ Response completed"),k(!1),d("idle")}),C.on("message_received",S=>{l(j=>[...j,S.message]),S.type==="assistant"?d("speaking"):S.type}),C.on("voice_response",S=>{if(k(!1),!h&&_.current){const j=new Blob([Uint8Array.from(atob(S.audio_data),je=>je.charCodeAt(0))],{type:"audio/mpeg"}),$=URL.createObjectURL(j);_.current.src=$,_.current.play(),m(!0)}}),C.on("realtime_connected",S=>{console.log("✅ OpenAI Realtime API connected:",S),r(!0)}),C.on("ai_greeting",S=>{console.log("🎤 AI Greeting:",S.message);const j={id:Date.now(),content:S.message,message_type:"assistant",created_at:new Date().toISOString()};l($=>[...$,j]),d("speaking")}),C.on("transcript_received",S=>{console.log("📝 Transcript received:",S.transcript);const j={id:S.message_id,content:S.transcript,message_type:"user",created_at:new Date().toISOString()};l($=>[...$,j])}),C.on("response_complete",()=>{console.log("✅ Response completed"),k(!1),d("idle")}),C.on("voice_transcribed",S=>{console.log("🎯 Voice transcribed:",S.text)}),C.on("command_executed",S=>{console.log("Command executed:",S)}),C.on("error",S=>{console.error("Socket error:",S)}),t(C),()=>{C.close()}},[]),I.useEffect(()=>{e&&n&&Q()},[e,n]),I.useEffect(()=>{(async()=>{if(!(!e||!n))try{console.log("🎧 Initializing wake word detection...");const S=localStorage.getItem("wakeWords"),j=[{id:"hey-chat",keyword:"hey chat",enabled:!0,sensitivity:.5},{id:"hey-babe",keyword:"hey babe",enabled:!1,sensitivity:.5},{id:"hey-cal",keyword:"hey cal",enabled:!1,sensitivity:.5}],$=S?JSON.parse(S):j;te($),S||localStorage.setItem("wakeWords",JSON.stringify(j)),e.on("wake_word_detected",se=>{console.log("🎯 Wake word detected:",se.keyword,"confidence:",se.confidence),d("listening")}),e.on("start_voice_recording",se=>{console.log("🎤 Starting voice recording triggered by:",se.triggered_by),!u&&T&&nt()});const je=$.filter(se=>se.enabled);if(je.length>0){const se=je.map(Se=>Se.keyword);e.emit("start_wake_word_detection",{wake_words:se,threshold:.3}),Ye(!0),console.log("✅ Wake word listening is now ACTIVE for:",se),Jt(),setTimeout(()=>{if(!h&&_.current){const Se="Hi! I'm your chat bot. Just say hey chat to get my attention.";e.emit("synthesize_greeting",{text:Se,conversation_id:i==null?void 0:i.id})}},2e3),setTimeout(()=>{if(!h&&_.current){const Se=`Hi! I'm your chat bot. Whenever you want to get my attention, just say my name like "${se[0]}".`;e.emit("synthesize_greeting",{text:Se,conversation_id:i==null?void 0:i.id})}},2e3)}}catch(S){console.error("Failed to initialize wake word service:",S),Ye(!1)}})()},[e,n]),I.useEffect(()=>{(()=>{try{const S={}.REACT_APP_GOOGLE_CLOUD_PROJECT_ID;if(S){const j=new Vu({projectId:S,sessionId:Vu.generateSessionId(),languageCode:"en-US"});A.current=j,console.log("DialogFlow service initialized")}else console.log("DialogFlow not configured - missing project ID")}catch(S){console.log("DialogFlow initialization failed:",S)}})()},[]);const Q=async()=>{if(!i)try{const S=await(await fetch("http://localhost:5002/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:"AI Voice Chat",user_id:1})})).json();S.success&&(o(S.conversation),e==null||e.emit("join_conversation",{conversation_id:S.conversation.id,user_id:1}))}catch(C){console.error("Error creating conversation:",C)}},Z=async(C,S=!1)=>{if(!e||!i||!C.trim())return;if(!T){console.log("System is paused - message sending disabled");return}f(!0),d("thinking");let j=null;if(A.current)try{if(await A.current.canHandleQuickly(C)&&(j=await A.current.detectIntent(C),j&&j.fulfillmentText)){const je={id:Date.now(),content:C,message_type:"user",created_at:new Date().toISOString()};l(Se=>[...Se,je]);const se={id:Date.now()+1,content:j.fulfillmentText,message_type:"assistant",created_at:new Date().toISOString()};l(Se=>[...Se,se]),S&&!h&&e.emit("generate_voice_only",{text:j.fulfillmentText,conversation_id:i.id}),f(!1),d("idle");return}}catch($){console.log("DialogFlow not available, falling back to AI:",$)}e.emit("send_message",{conversation_id:i.id,content:C,message_type:"user",generate_voice:S,dialogflow_context:j?{intent:j.intent,parameters:j.parameters,contexts:j.contexts}:null}),f(!1)},Jt=async()=>{if(e)try{console.log("🎧 Starting continuous audio stream for wake word detection...");const C=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),S=new AudioContext({sampleRate:16e3}),j=S.createMediaStreamSource(C),$=S.createScriptProcessor(4096,1,1);$.onaudioprocess=je=>{if(!T)return;const Se=je.inputBuffer.getChannelData(0),Cl=new Int16Array(Se.length);for(let Er=0;Er<Se.length;Er++){const Td=Math.max(-1,Math.min(1,Se[Er]));Cl[Er]=Td*32767}const Cd=new Uint8Array(Cl.buffer),Nd=btoa(String.fromCharCode(...Cd));e.emit("wake_word_audio_stream",{audio_data:Nd,sample_rate:16e3,channels:1,format:"pcm_s16le"})},j.connect($),$.connect(S.destination),console.log("✅ Continuous audio stream active for wake word detection")}catch(C){console.error("Error starting continuous audio stream:",C)}},nt=async()=>{if(!T||!e||!i){console.log("System not ready for voice streaming");return}try{console.log("🎤 Starting voice recording for conversation...");const C=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0}}),S=new MediaRecorder(C,{mimeType:"audio/webm;codecs=opus"}),j=[];S.ondataavailable=$=>{$.data.size>0&&j.push($.data)},S.onstop=async()=>{const $=new Blob(j,{type:"audio/webm"}),je=new FileReader;je.onloadend=()=>{const se=je.result.split(",")[1];e.emit("voice_stream",{conversation_id:i.id,audio_data:se})},je.readAsDataURL($),C.getTracks().forEach(se=>se.stop()),c(!1),d("thinking")},z.current=S,S.start(),c(!0),d("listening"),setTimeout(()=>{S.state==="recording"&&S.stop()},1e4),console.log("✅ Voice recording started")}catch(C){console.error("Error starting voice recording:",C)}},On=()=>{u&&(console.log("🛑 Stopping voice streaming..."),e&&i&&e.emit("stop_voice_streaming",{conversation_id:i.id}),c(!1),d("idle"),console.log("✅ Voice streaming stopped"))},Be=C=>{!e||!i||Z(C,!0)},Zt=()=>{m(!1),d("idle"),T&&Ke&&console.log("🔄 Voice response finished, ready for next wake word...")},El=()=>{const C=!h;w(C),C&&y&&_.current?(_.current.pause(),_.current.currentTime=0,m(!1),d("idle"),console.log("🔇 Assistant muted and stopped speaking")):C||console.log("🔊 Assistant unmuted")},Yi=C=>{localStorage.setItem("wakeWords",JSON.stringify(C)),console.log("Wake words saved to localStorage:",C)},Xi=async C=>{if(e)try{e.emit("stop_wake_word_detection");const S=C.filter(j=>j.enabled);if(S.length>0){const j=S.map($=>$.keyword);e.emit("start_wake_word_detection",{wake_words:j,threshold:.3}),console.log("Wake word service restarted with:",j)}}catch(S){console.error("Failed to restart wake word service:",S)}},vd=async C=>{const S={id:`wake-word-${Date.now()}`,keyword:C.toLowerCase(),enabled:!1,sensitivity:.5},j=[...L,S];te(j),Yi(j),await Xi(j),console.log("Added wake word:",C)},wd=async C=>{const S=L.filter(j=>j.id!==C);te(S),Yi(S),await Xi(S),console.log("Deleted wake word:",C)},xd=async C=>{const S=L.map(j=>j.id===C?{...j,enabled:!j.enabled}:j);te(S),Yi(S),await Xi(S),console.log("Toggled wake word:",C)},kd=()=>{l([]),o(null),Q()},_d=C=>{E(C),localStorage.setItem("userName",C)},Sd=C=>{R(C),localStorage.setItem("preferredWakeWord",C)},Ed=()=>{const C=!T;O(C),C?console.log("✅ System reactivated - AI ready to respond"):(console.log("⏸️ System paused - stopping all AI operations"),u&&On(),y&&_.current&&(_.current.pause(),_.current.currentTime=0,m(!1),d("idle")),k(!1))};return I.useEffect(()=>{const C=localStorage.getItem("userName"),S=localStorage.getItem("preferredWakeWord");C&&E(C),S&&R(S)},[]),p.jsxs("div",{className:"min-h-screen gradient-bg flex p-6 gap-6",children:[p.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center",children:[p.jsx("div",{className:"floating-panel w-full max-w-2xl mb-8",children:p.jsxs("div",{className:"glass-card",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h1",{className:"text-2xl font-bold text-white/90 mb-2",children:"AI Assistant"}),p.jsx("p",{className:"text-white/70 text-sm",children:"Click the microphone to start talking"})]}),p.jsx("div",{className:"flex items-center justify-center h-80 avatar-glow",children:p.jsx(Gh,{state:U,isConnected:n,isPlaying:y})})]})}),p.jsxs("div",{className:"glass-panel rounded-3xl p-6 mb-6",children:[p.jsx(om,{isRecording:u,isPlaying:y,isMuted:h,onToggleMute:El,isProcessing:x,systemActive:T}),Ke&&p.jsx("div",{className:"mt-4 text-center",children:p.jsxs("div",{className:"inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-2",children:[p.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),p.jsx("span",{className:"text-green-400 text-sm font-medium",children:"Wake word detection active"})]})})]}),p.jsx("div",{className:`connection-indicator ${n?"status-connected":"status-disconnected"}`,children:p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsx("div",{className:`w-2 h-2 rounded-full ${n?"bg-green-400 animate-pulse":"bg-red-400"}`}),p.jsx("span",{className:"text-sm font-medium",children:n?"Connected":"Reconnecting..."})]})})]}),p.jsx("div",{className:`flex flex-col h-screen transition-all duration-300 ${Dt?"w-[600px]":"w-96"}`,children:p.jsxs("div",{className:"glass-panel rounded-3xl flex flex-col h-full max-h-screen overflow-hidden",children:[p.jsxs("div",{className:"p-6 border-b border-white/10 flex-shrink-0",children:[p.jsx("h2",{className:"text-xl font-semibold text-white/90",children:"Our Conversation"}),p.jsx("p",{className:"text-white/60 text-sm mt-1",children:"I'm here to listen and support you"})]}),p.jsx("div",{className:"flex-1 overflow-hidden min-h-0",children:p.jsx(Jh,{messages:s,isLoading:a})}),p.jsxs("div",{className:"p-6 border-t border-white/10 flex-shrink-0",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsx("p",{className:"text-white/70 text-sm",children:"🎤 Voice-only conversation mode"}),p.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Use your wake word to start talking"})]}),p.jsxs("div",{className:"flex flex-wrap gap-2",children:[p.jsx("button",{onClick:()=>Be("I need some encouragement today"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Need Encouragement"}),p.jsx("button",{onClick:()=>Be("I'm feeling stressed and need support"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Feeling Stressed"}),p.jsx("button",{onClick:()=>Be("Tell me something positive"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Something Positive"})]})]})]})}),p.jsx(sm,{isExpanded:It,onToggleExpanded:()=>jn(!It),isMuted:h,onToggleMute:El,onRefreshChat:kd,chatExpanded:Dt,onToggleChatExpanded:()=>Ln(!Dt),userName:g,onUserNameChange:_d,preferredWakeWord:P,onWakeWordChange:Sd,systemActive:T,onToggleSystemActive:Ed,wakeWords:L,onAddWakeWord:vd,onDeleteWakeWord:wd,onToggleWakeWord:xd}),p.jsx("audio",{ref:_,onEnded:Zt,style:{display:"none"}})]})};nd(document.getElementById("root")).render(p.jsx(I.StrictMode,{children:p.jsx(lm,{})}));
