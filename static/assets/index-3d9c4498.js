var kf=Object.defineProperty;var Sf=(e,t,n)=>t in e?kf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var qi=(e,t,n)=>(Sf(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var Bu={exports:{}},Ci={},Fu={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yr=Symbol.for("react.element"),_f=Symbol.for("react.portal"),Ef=Symbol.for("react.fragment"),Nf=Symbol.for("react.strict_mode"),Cf=Symbol.for("react.profiler"),Tf=Symbol.for("react.provider"),Rf=Symbol.for("react.context"),Pf=Symbol.for("react.forward_ref"),jf=Symbol.for("react.suspense"),Lf=Symbol.for("react.memo"),Of=Symbol.for("react.lazy"),xl=Symbol.iterator;function Af(e){return e===null||typeof e!="object"?null:(e=xl&&e[xl]||e["@@iterator"],typeof e=="function"?e:null)}var Uu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},$u=Object.assign,Vu={};function En(e,t,n){this.props=e,this.context=t,this.refs=Vu,this.updater=n||Uu}En.prototype.isReactComponent={};En.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};En.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wu(){}Wu.prototype=En.prototype;function ks(e,t,n){this.props=e,this.context=t,this.refs=Vu,this.updater=n||Uu}var Ss=ks.prototype=new Wu;Ss.constructor=ks;$u(Ss,En.prototype);Ss.isPureReactComponent=!0;var kl=Array.isArray,Hu=Object.prototype.hasOwnProperty,_s={current:null},Qu={key:!0,ref:!0,__self:!0,__source:!0};function qu(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)Hu.call(t,r)&&!Qu.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:yr,type:e,key:o,ref:s,props:i,_owner:_s.current}}function zf(e,t){return{$$typeof:yr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Es(e){return typeof e=="object"&&e!==null&&e.$$typeof===yr}function If(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Sl=/\/+/g;function Ki(e,t){return typeof e=="object"&&e!==null&&e.key!=null?If(""+e.key):t.toString(36)}function Ur(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case yr:case _f:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Ki(s,0):r,kl(i)?(n="",e!=null&&(n=e.replace(Sl,"$&/")+"/"),Ur(i,t,n,"",function(c){return c})):i!=null&&(Es(i)&&(i=zf(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Sl,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",kl(e))for(var l=0;l<e.length;l++){o=e[l];var u=r+Ki(o,l);s+=Ur(o,t,n,u,i)}else if(u=Af(e),typeof u=="function")for(e=u.call(e),l=0;!(o=e.next()).done;)o=o.value,u=r+Ki(o,l++),s+=Ur(o,t,n,u,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function kr(e,t,n){if(e==null)return e;var r=[],i=0;return Ur(e,r,"","",function(o){return t.call(n,o,i++)}),r}function Df(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var he={current:null},$r={transition:null},Mf={ReactCurrentDispatcher:he,ReactCurrentBatchConfig:$r,ReactCurrentOwner:_s};function Ku(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:kr,forEach:function(e,t,n){kr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return kr(e,function(){t++}),t},toArray:function(e){return kr(e,function(t){return t})||[]},only:function(e){if(!Es(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=En;D.Fragment=Ef;D.Profiler=Cf;D.PureComponent=ks;D.StrictMode=Nf;D.Suspense=jf;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Mf;D.act=Ku;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=$u({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=_s.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)Hu.call(t,u)&&!Qu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&l!==void 0?l[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:yr,type:e.type,key:i,ref:o,props:r,_owner:s}};D.createContext=function(e){return e={$$typeof:Rf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Tf,_context:e},e.Consumer=e};D.createElement=qu;D.createFactory=function(e){var t=qu.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:Pf,render:e}};D.isValidElement=Es;D.lazy=function(e){return{$$typeof:Of,_payload:{_status:-1,_result:e},_init:Df}};D.memo=function(e,t){return{$$typeof:Lf,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=$r.transition;$r.transition={};try{e()}finally{$r.transition=t}};D.unstable_act=Ku;D.useCallback=function(e,t){return he.current.useCallback(e,t)};D.useContext=function(e){return he.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return he.current.useDeferredValue(e)};D.useEffect=function(e,t){return he.current.useEffect(e,t)};D.useId=function(){return he.current.useId()};D.useImperativeHandle=function(e,t,n){return he.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return he.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return he.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return he.current.useMemo(e,t)};D.useReducer=function(e,t,n){return he.current.useReducer(e,t,n)};D.useRef=function(e){return he.current.useRef(e)};D.useState=function(e){return he.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return he.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return he.current.useTransition()};D.version="18.3.1";Fu.exports=D;var I=Fu.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bf=I,Ff=Symbol.for("react.element"),Uf=Symbol.for("react.fragment"),$f=Object.prototype.hasOwnProperty,Vf=Bf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Wf={key:!0,ref:!0,__self:!0,__source:!0};function Yu(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)$f.call(t,r)&&!Wf.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Ff,type:e,key:o,ref:s,props:i,_owner:Vf.current}}Ci.Fragment=Uf;Ci.jsx=Yu;Ci.jsxs=Yu;Bu.exports=Ci;var d=Bu.exports,Xu={exports:{}},Ce={},Gu={exports:{}},Ju={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(S,L){var A=S.length;S.push(L);e:for(;0<A;){var Q=A-1>>>1,J=S[Q];if(0<i(J,L))S[Q]=L,S[A]=J,A=Q;else break e}}function n(S){return S.length===0?null:S[0]}function r(S){if(S.length===0)return null;var L=S[0],A=S.pop();if(A!==L){S[0]=A;e:for(var Q=0,J=S.length,ft=J>>>1;Q<ft;){var be=2*(Q+1)-1,Gt=S[be],Ie=be+1,dt=S[Ie];if(0>i(Gt,A))Ie<J&&0>i(dt,Gt)?(S[Q]=dt,S[Ie]=A,Q=Ie):(S[Q]=Gt,S[be]=A,Q=be);else if(Ie<J&&0>i(dt,A))S[Q]=dt,S[Ie]=A,Q=Ie;else break e}}return L}function i(S,L){var A=S.sortIndex-L.sortIndex;return A!==0?A:S.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var u=[],c=[],y=1,m=null,h=3,w=!1,x=!1,k=!1,U=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function p(S){for(var L=n(c);L!==null;){if(L.callback===null)r(c);else if(L.startTime<=S)r(c),L.sortIndex=L.expirationTime,t(u,L);else break;L=n(c)}}function g(S){if(k=!1,p(S),!x)if(n(u)!==null)x=!0,It(_);else{var L=n(c);L!==null&&Pn(g,L.startTime-S)}}function _(S,L){x=!1,k&&(k=!1,f(T),T=-1),w=!0;var A=h;try{for(p(L),m=n(u);m!==null&&(!(m.expirationTime>L)||S&&!ee());){var Q=m.callback;if(typeof Q=="function"){m.callback=null,h=m.priorityLevel;var J=Q(m.expirationTime<=L);L=e.unstable_now(),typeof J=="function"?m.callback=J:m===n(u)&&r(u),p(L)}else r(u);m=n(u)}if(m!==null)var ft=!0;else{var be=n(c);be!==null&&Pn(g,be.startTime-L),ft=!1}return ft}finally{m=null,h=A,w=!1}}var C=!1,N=null,T=-1,O=5,j=-1;function ee(){return!(e.unstable_now()-j<O)}function Je(){if(N!==null){var S=e.unstable_now();j=S;var L=!0;try{L=N(!0,S)}finally{L?Ze():(C=!1,N=null)}}else C=!1}var Ze;if(typeof a=="function")Ze=function(){a(Je)};else if(typeof MessageChannel<"u"){var zt=new MessageChannel,Rn=zt.port2;zt.port1.onmessage=Je,Ze=function(){Rn.postMessage(null)}}else Ze=function(){U(Je,0)};function It(S){N=S,C||(C=!0,Ze())}function Pn(S,L){T=U(function(){S(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(S){S.callback=null},e.unstable_continueExecution=function(){x||w||(x=!0,It(_))},e.unstable_forceFrameRate=function(S){0>S||125<S?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<S?Math.floor(1e3/S):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(S){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var A=h;h=L;try{return S()}finally{h=A}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(S,L){switch(S){case 1:case 2:case 3:case 4:case 5:break;default:S=3}var A=h;h=S;try{return L()}finally{h=A}},e.unstable_scheduleCallback=function(S,L,A){var Q=e.unstable_now();switch(typeof A=="object"&&A!==null?(A=A.delay,A=typeof A=="number"&&0<A?Q+A:Q):A=Q,S){case 1:var J=-1;break;case 2:J=250;break;case 5:J=**********;break;case 4:J=1e4;break;default:J=5e3}return J=A+J,S={id:y++,callback:L,priorityLevel:S,startTime:A,expirationTime:J,sortIndex:-1},A>Q?(S.sortIndex=A,t(c,S),n(u)===null&&S===n(c)&&(k?(f(T),T=-1):k=!0,Pn(g,A-Q))):(S.sortIndex=J,t(u,S),x||w||(x=!0,It(_))),S},e.unstable_shouldYield=ee,e.unstable_wrapCallback=function(S){var L=h;return function(){var A=h;h=L;try{return S.apply(this,arguments)}finally{h=A}}}})(Ju);Gu.exports=Ju;var Hf=Gu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qf=I,Ne=Hf;function v(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Zu=new Set,bn={};function Yt(e,t){gn(e,t),gn(e+"Capture",t)}function gn(e,t){for(bn[e]=t,e=0;e<t.length;e++)Zu.add(t[e])}var ot=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_o=Object.prototype.hasOwnProperty,qf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,_l={},El={};function Kf(e){return _o.call(El,e)?!0:_o.call(_l,e)?!1:qf.test(e)?El[e]=!0:(_l[e]=!0,!1)}function Yf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Xf(e,t,n,r){if(t===null||typeof t>"u"||Yf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function me(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){le[e]=new me(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];le[t]=new me(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){le[e]=new me(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){le[e]=new me(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){le[e]=new me(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){le[e]=new me(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){le[e]=new me(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){le[e]=new me(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){le[e]=new me(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ns=/[\-:]([a-z])/g;function Cs(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ns,Cs);le[t]=new me(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ns,Cs);le[t]=new me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ns,Cs);le[t]=new me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){le[e]=new me(e,1,!1,e.toLowerCase(),null,!1,!1)});le.xlinkHref=new me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){le[e]=new me(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ts(e,t,n,r){var i=le.hasOwnProperty(t)?le[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Xf(t,n,i,r)&&(n=null),r||i===null?Kf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var at=Qf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Sr=Symbol.for("react.element"),Zt=Symbol.for("react.portal"),bt=Symbol.for("react.fragment"),Rs=Symbol.for("react.strict_mode"),Eo=Symbol.for("react.profiler"),bu=Symbol.for("react.provider"),ea=Symbol.for("react.context"),Ps=Symbol.for("react.forward_ref"),No=Symbol.for("react.suspense"),Co=Symbol.for("react.suspense_list"),js=Symbol.for("react.memo"),ht=Symbol.for("react.lazy"),ta=Symbol.for("react.offscreen"),Nl=Symbol.iterator;function jn(e){return e===null||typeof e!="object"?null:(e=Nl&&e[Nl]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,Yi;function Fn(e){if(Yi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Yi=t&&t[1]||""}return`
`+Yi+e}var Xi=!1;function Gi(e,t){if(!e||Xi)return"";Xi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var u=`
`+i[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=l);break}}}finally{Xi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Fn(e):""}function Gf(e){switch(e.tag){case 5:return Fn(e.type);case 16:return Fn("Lazy");case 13:return Fn("Suspense");case 19:return Fn("SuspenseList");case 0:case 2:case 15:return e=Gi(e.type,!1),e;case 11:return e=Gi(e.type.render,!1),e;case 1:return e=Gi(e.type,!0),e;default:return""}}function To(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case bt:return"Fragment";case Zt:return"Portal";case Eo:return"Profiler";case Rs:return"StrictMode";case No:return"Suspense";case Co:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ea:return(e.displayName||"Context")+".Consumer";case bu:return(e._context.displayName||"Context")+".Provider";case Ps:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case js:return t=e.displayName||null,t!==null?t:To(e.type)||"Memo";case ht:t=e._payload,e=e._init;try{return To(e(t))}catch{}}return null}function Jf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return To(t);case 8:return t===Rs?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Pt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function na(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Zf(e){var t=na(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _r(e){e._valueTracker||(e._valueTracker=Zf(e))}function ra(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=na(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ni(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Ro(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Cl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Pt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ia(e,t){t=t.checked,t!=null&&Ts(e,"checked",t,!1)}function Po(e,t){ia(e,t);var n=Pt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?jo(e,t.type,n):t.hasOwnProperty("defaultValue")&&jo(e,t.type,Pt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Tl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function jo(e,t,n){(t!=="number"||ni(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Un=Array.isArray;function fn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Pt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Lo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(v(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Rl(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(v(92));if(Un(n)){if(1<n.length)throw Error(v(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Pt(n)}}function oa(e,t){var n=Pt(t.value),r=Pt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Pl(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function sa(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Oo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?sa(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Er,la=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Er=Er||document.createElement("div"),Er.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Er.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function er(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Hn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},bf=["Webkit","ms","Moz","O"];Object.keys(Hn).forEach(function(e){bf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Hn[t]=Hn[e]})});function ua(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Hn.hasOwnProperty(e)&&Hn[e]?(""+t).trim():t+"px"}function aa(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=ua(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var ed=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ao(e,t){if(t){if(ed[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(v(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(v(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(v(61))}if(t.style!=null&&typeof t.style!="object")throw Error(v(62))}}function zo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Io=null;function Ls(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Do=null,dn=null,pn=null;function jl(e){if(e=wr(e)){if(typeof Do!="function")throw Error(v(280));var t=e.stateNode;t&&(t=Li(t),Do(e.stateNode,e.type,t))}}function ca(e){dn?pn?pn.push(e):pn=[e]:dn=e}function fa(){if(dn){var e=dn,t=pn;if(pn=dn=null,jl(e),t)for(e=0;e<t.length;e++)jl(t[e])}}function da(e,t){return e(t)}function pa(){}var Ji=!1;function ha(e,t,n){if(Ji)return e(t,n);Ji=!0;try{return da(e,t,n)}finally{Ji=!1,(dn!==null||pn!==null)&&(pa(),fa())}}function tr(e,t){var n=e.stateNode;if(n===null)return null;var r=Li(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(v(231,t,typeof n));return n}var Mo=!1;if(ot)try{var Ln={};Object.defineProperty(Ln,"passive",{get:function(){Mo=!0}}),window.addEventListener("test",Ln,Ln),window.removeEventListener("test",Ln,Ln)}catch{Mo=!1}function td(e,t,n,r,i,o,s,l,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(y){this.onError(y)}}var Qn=!1,ri=null,ii=!1,Bo=null,nd={onError:function(e){Qn=!0,ri=e}};function rd(e,t,n,r,i,o,s,l,u){Qn=!1,ri=null,td.apply(nd,arguments)}function id(e,t,n,r,i,o,s,l,u){if(rd.apply(this,arguments),Qn){if(Qn){var c=ri;Qn=!1,ri=null}else throw Error(v(198));ii||(ii=!0,Bo=c)}}function Xt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ma(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ll(e){if(Xt(e)!==e)throw Error(v(188))}function od(e){var t=e.alternate;if(!t){if(t=Xt(e),t===null)throw Error(v(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Ll(i),e;if(o===r)return Ll(i),t;o=o.sibling}throw Error(v(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(v(189))}}if(n.alternate!==r)throw Error(v(190))}if(n.tag!==3)throw Error(v(188));return n.stateNode.current===n?e:t}function ya(e){return e=od(e),e!==null?ga(e):null}function ga(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=ga(e);if(t!==null)return t;e=e.sibling}return null}var va=Ne.unstable_scheduleCallback,Ol=Ne.unstable_cancelCallback,sd=Ne.unstable_shouldYield,ld=Ne.unstable_requestPaint,G=Ne.unstable_now,ud=Ne.unstable_getCurrentPriorityLevel,Os=Ne.unstable_ImmediatePriority,wa=Ne.unstable_UserBlockingPriority,oi=Ne.unstable_NormalPriority,ad=Ne.unstable_LowPriority,xa=Ne.unstable_IdlePriority,Ti=null,Ke=null;function cd(e){if(Ke&&typeof Ke.onCommitFiberRoot=="function")try{Ke.onCommitFiberRoot(Ti,e,void 0,(e.current.flags&128)===128)}catch{}}var $e=Math.clz32?Math.clz32:pd,fd=Math.log,dd=Math.LN2;function pd(e){return e>>>=0,e===0?32:31-(fd(e)/dd|0)|0}var Nr=64,Cr=4194304;function $n(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function si(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=$n(l):(o&=s,o!==0&&(r=$n(o)))}else s=n&~i,s!==0?r=$n(s):o!==0&&(r=$n(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-$e(t),i=1<<n,r|=e[n],t&=~i;return r}function hd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function md(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-$e(o),l=1<<s,u=i[s];u===-1?(!(l&n)||l&r)&&(i[s]=hd(l,t)):u<=t&&(e.expiredLanes|=l),o&=~l}}function Fo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ka(){var e=Nr;return Nr<<=1,!(Nr&4194240)&&(Nr=64),e}function Zi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-$e(t),e[t]=n}function yd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-$e(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function As(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-$e(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var F=0;function Sa(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var _a,zs,Ea,Na,Ca,Uo=!1,Tr=[],xt=null,kt=null,St=null,nr=new Map,rr=new Map,yt=[],gd="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Al(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":kt=null;break;case"mouseover":case"mouseout":St=null;break;case"pointerover":case"pointerout":nr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rr.delete(t.pointerId)}}function On(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=wr(t),t!==null&&zs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function vd(e,t,n,r,i){switch(t){case"focusin":return xt=On(xt,e,t,n,r,i),!0;case"dragenter":return kt=On(kt,e,t,n,r,i),!0;case"mouseover":return St=On(St,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return nr.set(o,On(nr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,rr.set(o,On(rr.get(o)||null,e,t,n,r,i)),!0}return!1}function Ta(e){var t=Bt(e.target);if(t!==null){var n=Xt(t);if(n!==null){if(t=n.tag,t===13){if(t=ma(n),t!==null){e.blockedOn=t,Ca(e.priority,function(){Ea(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=$o(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Io=r,n.target.dispatchEvent(r),Io=null}else return t=wr(n),t!==null&&zs(t),e.blockedOn=n,!1;t.shift()}return!0}function zl(e,t,n){Vr(e)&&n.delete(t)}function wd(){Uo=!1,xt!==null&&Vr(xt)&&(xt=null),kt!==null&&Vr(kt)&&(kt=null),St!==null&&Vr(St)&&(St=null),nr.forEach(zl),rr.forEach(zl)}function An(e,t){e.blockedOn===t&&(e.blockedOn=null,Uo||(Uo=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,wd)))}function ir(e){function t(i){return An(i,e)}if(0<Tr.length){An(Tr[0],e);for(var n=1;n<Tr.length;n++){var r=Tr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xt!==null&&An(xt,e),kt!==null&&An(kt,e),St!==null&&An(St,e),nr.forEach(t),rr.forEach(t),n=0;n<yt.length;n++)r=yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<yt.length&&(n=yt[0],n.blockedOn===null);)Ta(n),n.blockedOn===null&&yt.shift()}var hn=at.ReactCurrentBatchConfig,li=!0;function xd(e,t,n,r){var i=F,o=hn.transition;hn.transition=null;try{F=1,Is(e,t,n,r)}finally{F=i,hn.transition=o}}function kd(e,t,n,r){var i=F,o=hn.transition;hn.transition=null;try{F=4,Is(e,t,n,r)}finally{F=i,hn.transition=o}}function Is(e,t,n,r){if(li){var i=$o(e,t,n,r);if(i===null)uo(e,t,r,ui,n),Al(e,r);else if(vd(i,e,t,n,r))r.stopPropagation();else if(Al(e,r),t&4&&-1<gd.indexOf(e)){for(;i!==null;){var o=wr(i);if(o!==null&&_a(o),o=$o(e,t,n,r),o===null&&uo(e,t,r,ui,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else uo(e,t,r,null,n)}}var ui=null;function $o(e,t,n,r){if(ui=null,e=Ls(r),e=Bt(e),e!==null)if(t=Xt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ma(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ui=e,null}function Ra(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(ud()){case Os:return 1;case wa:return 4;case oi:case ad:return 16;case xa:return 536870912;default:return 16}default:return 16}}var vt=null,Ds=null,Wr=null;function Pa(){if(Wr)return Wr;var e,t=Ds,n=t.length,r,i="value"in vt?vt.value:vt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Wr=i.slice(e,1<r?1-r:void 0)}function Hr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Rr(){return!0}function Il(){return!1}function Te(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Rr:Il,this.isPropagationStopped=Il,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Rr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Rr)},persist:function(){},isPersistent:Rr}),t}var Nn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ms=Te(Nn),vr=Y({},Nn,{view:0,detail:0}),Sd=Te(vr),bi,eo,zn,Ri=Y({},vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==zn&&(zn&&e.type==="mousemove"?(bi=e.screenX-zn.screenX,eo=e.screenY-zn.screenY):eo=bi=0,zn=e),bi)},movementY:function(e){return"movementY"in e?e.movementY:eo}}),Dl=Te(Ri),_d=Y({},Ri,{dataTransfer:0}),Ed=Te(_d),Nd=Y({},vr,{relatedTarget:0}),to=Te(Nd),Cd=Y({},Nn,{animationName:0,elapsedTime:0,pseudoElement:0}),Td=Te(Cd),Rd=Y({},Nn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Pd=Te(Rd),jd=Y({},Nn,{data:0}),Ml=Te(jd),Ld={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Od={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ad={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function zd(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ad[e])?!!t[e]:!1}function Bs(){return zd}var Id=Y({},vr,{key:function(e){if(e.key){var t=Ld[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Hr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Od[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bs,charCode:function(e){return e.type==="keypress"?Hr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Hr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Dd=Te(Id),Md=Y({},Ri,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Bl=Te(Md),Bd=Y({},vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bs}),Fd=Te(Bd),Ud=Y({},Nn,{propertyName:0,elapsedTime:0,pseudoElement:0}),$d=Te(Ud),Vd=Y({},Ri,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Wd=Te(Vd),Hd=[9,13,27,32],Fs=ot&&"CompositionEvent"in window,qn=null;ot&&"documentMode"in document&&(qn=document.documentMode);var Qd=ot&&"TextEvent"in window&&!qn,ja=ot&&(!Fs||qn&&8<qn&&11>=qn),Fl=String.fromCharCode(32),Ul=!1;function La(e,t){switch(e){case"keyup":return Hd.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Oa(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var en=!1;function qd(e,t){switch(e){case"compositionend":return Oa(t);case"keypress":return t.which!==32?null:(Ul=!0,Fl);case"textInput":return e=t.data,e===Fl&&Ul?null:e;default:return null}}function Kd(e,t){if(en)return e==="compositionend"||!Fs&&La(e,t)?(e=Pa(),Wr=Ds=vt=null,en=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ja&&t.locale!=="ko"?null:t.data;default:return null}}var Yd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $l(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Yd[e.type]:t==="textarea"}function Aa(e,t,n,r){ca(r),t=ai(t,"onChange"),0<t.length&&(n=new Ms("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,or=null;function Xd(e){Ha(e,0)}function Pi(e){var t=rn(e);if(ra(t))return e}function Gd(e,t){if(e==="change")return t}var za=!1;if(ot){var no;if(ot){var ro="oninput"in document;if(!ro){var Vl=document.createElement("div");Vl.setAttribute("oninput","return;"),ro=typeof Vl.oninput=="function"}no=ro}else no=!1;za=no&&(!document.documentMode||9<document.documentMode)}function Wl(){Kn&&(Kn.detachEvent("onpropertychange",Ia),or=Kn=null)}function Ia(e){if(e.propertyName==="value"&&Pi(or)){var t=[];Aa(t,or,e,Ls(e)),ha(Xd,t)}}function Jd(e,t,n){e==="focusin"?(Wl(),Kn=t,or=n,Kn.attachEvent("onpropertychange",Ia)):e==="focusout"&&Wl()}function Zd(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Pi(or)}function bd(e,t){if(e==="click")return Pi(t)}function ep(e,t){if(e==="input"||e==="change")return Pi(t)}function tp(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var We=typeof Object.is=="function"?Object.is:tp;function sr(e,t){if(We(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!_o.call(t,i)||!We(e[i],t[i]))return!1}return!0}function Hl(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ql(e,t){var n=Hl(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Hl(n)}}function Da(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Da(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ma(){for(var e=window,t=ni();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ni(e.document)}return t}function Us(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function np(e){var t=Ma(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Da(n.ownerDocument.documentElement,n)){if(r!==null&&Us(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Ql(n,o);var s=Ql(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var rp=ot&&"documentMode"in document&&11>=document.documentMode,tn=null,Vo=null,Yn=null,Wo=!1;function ql(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Wo||tn==null||tn!==ni(r)||(r=tn,"selectionStart"in r&&Us(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Yn&&sr(Yn,r)||(Yn=r,r=ai(Vo,"onSelect"),0<r.length&&(t=new Ms("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=tn)))}function Pr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var nn={animationend:Pr("Animation","AnimationEnd"),animationiteration:Pr("Animation","AnimationIteration"),animationstart:Pr("Animation","AnimationStart"),transitionend:Pr("Transition","TransitionEnd")},io={},Ba={};ot&&(Ba=document.createElement("div").style,"AnimationEvent"in window||(delete nn.animationend.animation,delete nn.animationiteration.animation,delete nn.animationstart.animation),"TransitionEvent"in window||delete nn.transitionend.transition);function ji(e){if(io[e])return io[e];if(!nn[e])return e;var t=nn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ba)return io[e]=t[n];return e}var Fa=ji("animationend"),Ua=ji("animationiteration"),$a=ji("animationstart"),Va=ji("transitionend"),Wa=new Map,Kl="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lt(e,t){Wa.set(e,t),Yt(t,[e])}for(var oo=0;oo<Kl.length;oo++){var so=Kl[oo],ip=so.toLowerCase(),op=so[0].toUpperCase()+so.slice(1);Lt(ip,"on"+op)}Lt(Fa,"onAnimationEnd");Lt(Ua,"onAnimationIteration");Lt($a,"onAnimationStart");Lt("dblclick","onDoubleClick");Lt("focusin","onFocus");Lt("focusout","onBlur");Lt(Va,"onTransitionEnd");gn("onMouseEnter",["mouseout","mouseover"]);gn("onMouseLeave",["mouseout","mouseover"]);gn("onPointerEnter",["pointerout","pointerover"]);gn("onPointerLeave",["pointerout","pointerover"]);Yt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Yt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Yt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Yt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Yt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Yt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Vn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),sp=new Set("cancel close invalid load scroll toggle".split(" ").concat(Vn));function Yl(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,id(r,t,void 0,e),e.currentTarget=null}function Ha(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],u=l.instance,c=l.currentTarget;if(l=l.listener,u!==o&&i.isPropagationStopped())break e;Yl(i,l,c),o=u}else for(s=0;s<r.length;s++){if(l=r[s],u=l.instance,c=l.currentTarget,l=l.listener,u!==o&&i.isPropagationStopped())break e;Yl(i,l,c),o=u}}}if(ii)throw e=Bo,ii=!1,Bo=null,e}function V(e,t){var n=t[Yo];n===void 0&&(n=t[Yo]=new Set);var r=e+"__bubble";n.has(r)||(Qa(t,e,2,!1),n.add(r))}function lo(e,t,n){var r=0;t&&(r|=4),Qa(n,e,r,t)}var jr="_reactListening"+Math.random().toString(36).slice(2);function lr(e){if(!e[jr]){e[jr]=!0,Zu.forEach(function(n){n!=="selectionchange"&&(sp.has(n)||lo(n,!1,e),lo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[jr]||(t[jr]=!0,lo("selectionchange",!1,t))}}function Qa(e,t,n,r){switch(Ra(t)){case 1:var i=xd;break;case 4:i=kd;break;default:i=Is}n=i.bind(null,t,n,e),i=void 0,!Mo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function uo(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Bt(l),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}l=l.parentNode}}r=r.return}ha(function(){var c=o,y=Ls(n),m=[];e:{var h=Wa.get(e);if(h!==void 0){var w=Ms,x=e;switch(e){case"keypress":if(Hr(n)===0)break e;case"keydown":case"keyup":w=Dd;break;case"focusin":x="focus",w=to;break;case"focusout":x="blur",w=to;break;case"beforeblur":case"afterblur":w=to;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Dl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=Ed;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Fd;break;case Fa:case Ua:case $a:w=Td;break;case Va:w=$d;break;case"scroll":w=Sd;break;case"wheel":w=Wd;break;case"copy":case"cut":case"paste":w=Pd;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Bl}var k=(t&4)!==0,U=!k&&e==="scroll",f=k?h!==null?h+"Capture":null:h;k=[];for(var a=c,p;a!==null;){p=a;var g=p.stateNode;if(p.tag===5&&g!==null&&(p=g,f!==null&&(g=tr(a,f),g!=null&&k.push(ur(a,g,p)))),U)break;a=a.return}0<k.length&&(h=new w(h,x,null,n,y),m.push({event:h,listeners:k}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&n!==Io&&(x=n.relatedTarget||n.fromElement)&&(Bt(x)||x[st]))break e;if((w||h)&&(h=y.window===y?y:(h=y.ownerDocument)?h.defaultView||h.parentWindow:window,w?(x=n.relatedTarget||n.toElement,w=c,x=x?Bt(x):null,x!==null&&(U=Xt(x),x!==U||x.tag!==5&&x.tag!==6)&&(x=null)):(w=null,x=c),w!==x)){if(k=Dl,g="onMouseLeave",f="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(k=Bl,g="onPointerLeave",f="onPointerEnter",a="pointer"),U=w==null?h:rn(w),p=x==null?h:rn(x),h=new k(g,a+"leave",w,n,y),h.target=U,h.relatedTarget=p,g=null,Bt(y)===c&&(k=new k(f,a+"enter",x,n,y),k.target=p,k.relatedTarget=U,g=k),U=g,w&&x)t:{for(k=w,f=x,a=0,p=k;p;p=Jt(p))a++;for(p=0,g=f;g;g=Jt(g))p++;for(;0<a-p;)k=Jt(k),a--;for(;0<p-a;)f=Jt(f),p--;for(;a--;){if(k===f||f!==null&&k===f.alternate)break t;k=Jt(k),f=Jt(f)}k=null}else k=null;w!==null&&Xl(m,h,w,k,!1),x!==null&&U!==null&&Xl(m,U,x,k,!0)}}e:{if(h=c?rn(c):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var _=Gd;else if($l(h))if(za)_=ep;else{_=Zd;var C=Jd}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(_=bd);if(_&&(_=_(e,c))){Aa(m,_,n,y);break e}C&&C(e,h,c),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&jo(h,"number",h.value)}switch(C=c?rn(c):window,e){case"focusin":($l(C)||C.contentEditable==="true")&&(tn=C,Vo=c,Yn=null);break;case"focusout":Yn=Vo=tn=null;break;case"mousedown":Wo=!0;break;case"contextmenu":case"mouseup":case"dragend":Wo=!1,ql(m,n,y);break;case"selectionchange":if(rp)break;case"keydown":case"keyup":ql(m,n,y)}var N;if(Fs)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else en?La(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(ja&&n.locale!=="ko"&&(en||T!=="onCompositionStart"?T==="onCompositionEnd"&&en&&(N=Pa()):(vt=y,Ds="value"in vt?vt.value:vt.textContent,en=!0)),C=ai(c,T),0<C.length&&(T=new Ml(T,e,null,n,y),m.push({event:T,listeners:C}),N?T.data=N:(N=Oa(n),N!==null&&(T.data=N)))),(N=Qd?qd(e,n):Kd(e,n))&&(c=ai(c,"onBeforeInput"),0<c.length&&(y=new Ml("onBeforeInput","beforeinput",null,n,y),m.push({event:y,listeners:c}),y.data=N))}Ha(m,t)})}function ur(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ai(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=tr(e,n),o!=null&&r.unshift(ur(e,o,i)),o=tr(e,t),o!=null&&r.push(ur(e,o,i))),e=e.return}return r}function Jt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xl(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,u=l.alternate,c=l.stateNode;if(u!==null&&u===r)break;l.tag===5&&c!==null&&(l=c,i?(u=tr(n,o),u!=null&&s.unshift(ur(n,u,l))):i||(u=tr(n,o),u!=null&&s.push(ur(n,u,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var lp=/\r\n?/g,up=/\u0000|\uFFFD/g;function Gl(e){return(typeof e=="string"?e:""+e).replace(lp,`
`).replace(up,"")}function Lr(e,t,n){if(t=Gl(t),Gl(e)!==t&&n)throw Error(v(425))}function ci(){}var Ho=null,Qo=null;function qo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ko=typeof setTimeout=="function"?setTimeout:void 0,ap=typeof clearTimeout=="function"?clearTimeout:void 0,Jl=typeof Promise=="function"?Promise:void 0,cp=typeof queueMicrotask=="function"?queueMicrotask:typeof Jl<"u"?function(e){return Jl.resolve(null).then(e).catch(fp)}:Ko;function fp(e){setTimeout(function(){throw e})}function ao(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),ir(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);ir(t)}function _t(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Zl(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Cn=Math.random().toString(36).slice(2),qe="__reactFiber$"+Cn,ar="__reactProps$"+Cn,st="__reactContainer$"+Cn,Yo="__reactEvents$"+Cn,dp="__reactListeners$"+Cn,pp="__reactHandles$"+Cn;function Bt(e){var t=e[qe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[st]||n[qe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Zl(e);e!==null;){if(n=e[qe])return n;e=Zl(e)}return t}e=n,n=e.parentNode}return null}function wr(e){return e=e[qe]||e[st],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function rn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(v(33))}function Li(e){return e[ar]||null}var Xo=[],on=-1;function Ot(e){return{current:e}}function W(e){0>on||(e.current=Xo[on],Xo[on]=null,on--)}function $(e,t){on++,Xo[on]=e.current,e.current=t}var jt={},fe=Ot(jt),we=Ot(!1),Wt=jt;function vn(e,t){var n=e.type.contextTypes;if(!n)return jt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function xe(e){return e=e.childContextTypes,e!=null}function fi(){W(we),W(fe)}function bl(e,t,n){if(fe.current!==jt)throw Error(v(168));$(fe,t),$(we,n)}function qa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(v(108,Jf(e)||"Unknown",i));return Y({},n,r)}function di(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||jt,Wt=fe.current,$(fe,e),$(we,we.current),!0}function eu(e,t,n){var r=e.stateNode;if(!r)throw Error(v(169));n?(e=qa(e,t,Wt),r.__reactInternalMemoizedMergedChildContext=e,W(we),W(fe),$(fe,e)):W(we),$(we,n)}var tt=null,Oi=!1,co=!1;function Ka(e){tt===null?tt=[e]:tt.push(e)}function hp(e){Oi=!0,Ka(e)}function At(){if(!co&&tt!==null){co=!0;var e=0,t=F;try{var n=tt;for(F=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}tt=null,Oi=!1}catch(i){throw tt!==null&&(tt=tt.slice(e+1)),va(Os,At),i}finally{F=t,co=!1}}return null}var sn=[],ln=0,pi=null,hi=0,Re=[],Pe=0,Ht=null,nt=1,rt="";function Dt(e,t){sn[ln++]=hi,sn[ln++]=pi,pi=e,hi=t}function Ya(e,t,n){Re[Pe++]=nt,Re[Pe++]=rt,Re[Pe++]=Ht,Ht=e;var r=nt;e=rt;var i=32-$e(r)-1;r&=~(1<<i),n+=1;var o=32-$e(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,nt=1<<32-$e(t)+i|n<<i|r,rt=o+e}else nt=1<<o|n<<i|r,rt=e}function $s(e){e.return!==null&&(Dt(e,1),Ya(e,1,0))}function Vs(e){for(;e===pi;)pi=sn[--ln],sn[ln]=null,hi=sn[--ln],sn[ln]=null;for(;e===Ht;)Ht=Re[--Pe],Re[Pe]=null,rt=Re[--Pe],Re[Pe]=null,nt=Re[--Pe],Re[Pe]=null}var Ee=null,_e=null,H=!1,Ue=null;function Xa(e,t){var n=Le(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function tu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ee=e,_e=_t(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ee=e,_e=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Ht!==null?{id:nt,overflow:rt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Le(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ee=e,_e=null,!0):!1;default:return!1}}function Go(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Jo(e){if(H){var t=_e;if(t){var n=t;if(!tu(e,t)){if(Go(e))throw Error(v(418));t=_t(n.nextSibling);var r=Ee;t&&tu(e,t)?Xa(r,n):(e.flags=e.flags&-4097|2,H=!1,Ee=e)}}else{if(Go(e))throw Error(v(418));e.flags=e.flags&-4097|2,H=!1,Ee=e}}}function nu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ee=e}function Or(e){if(e!==Ee)return!1;if(!H)return nu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!qo(e.type,e.memoizedProps)),t&&(t=_e)){if(Go(e))throw Ga(),Error(v(418));for(;t;)Xa(e,t),t=_t(t.nextSibling)}if(nu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(v(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){_e=_t(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}_e=null}}else _e=Ee?_t(e.stateNode.nextSibling):null;return!0}function Ga(){for(var e=_e;e;)e=_t(e.nextSibling)}function wn(){_e=Ee=null,H=!1}function Ws(e){Ue===null?Ue=[e]:Ue.push(e)}var mp=at.ReactCurrentBatchConfig;function In(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(v(309));var r=n.stateNode}if(!r)throw Error(v(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(v(284));if(!n._owner)throw Error(v(290,e))}return e}function Ar(e,t){throw e=Object.prototype.toString.call(t),Error(v(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ru(e){var t=e._init;return t(e._payload)}function Ja(e){function t(f,a){if(e){var p=f.deletions;p===null?(f.deletions=[a],f.flags|=16):p.push(a)}}function n(f,a){if(!e)return null;for(;a!==null;)t(f,a),a=a.sibling;return null}function r(f,a){for(f=new Map;a!==null;)a.key!==null?f.set(a.key,a):f.set(a.index,a),a=a.sibling;return f}function i(f,a){return f=Tt(f,a),f.index=0,f.sibling=null,f}function o(f,a,p){return f.index=p,e?(p=f.alternate,p!==null?(p=p.index,p<a?(f.flags|=2,a):p):(f.flags|=2,a)):(f.flags|=1048576,a)}function s(f){return e&&f.alternate===null&&(f.flags|=2),f}function l(f,a,p,g){return a===null||a.tag!==6?(a=vo(p,f.mode,g),a.return=f,a):(a=i(a,p),a.return=f,a)}function u(f,a,p,g){var _=p.type;return _===bt?y(f,a,p.props.children,g,p.key):a!==null&&(a.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===ht&&ru(_)===a.type)?(g=i(a,p.props),g.ref=In(f,a,p),g.return=f,g):(g=Jr(p.type,p.key,p.props,null,f.mode,g),g.ref=In(f,a,p),g.return=f,g)}function c(f,a,p,g){return a===null||a.tag!==4||a.stateNode.containerInfo!==p.containerInfo||a.stateNode.implementation!==p.implementation?(a=wo(p,f.mode,g),a.return=f,a):(a=i(a,p.children||[]),a.return=f,a)}function y(f,a,p,g,_){return a===null||a.tag!==7?(a=Vt(p,f.mode,g,_),a.return=f,a):(a=i(a,p),a.return=f,a)}function m(f,a,p){if(typeof a=="string"&&a!==""||typeof a=="number")return a=vo(""+a,f.mode,p),a.return=f,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case Sr:return p=Jr(a.type,a.key,a.props,null,f.mode,p),p.ref=In(f,null,a),p.return=f,p;case Zt:return a=wo(a,f.mode,p),a.return=f,a;case ht:var g=a._init;return m(f,g(a._payload),p)}if(Un(a)||jn(a))return a=Vt(a,f.mode,p,null),a.return=f,a;Ar(f,a)}return null}function h(f,a,p,g){var _=a!==null?a.key:null;if(typeof p=="string"&&p!==""||typeof p=="number")return _!==null?null:l(f,a,""+p,g);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Sr:return p.key===_?u(f,a,p,g):null;case Zt:return p.key===_?c(f,a,p,g):null;case ht:return _=p._init,h(f,a,_(p._payload),g)}if(Un(p)||jn(p))return _!==null?null:y(f,a,p,g,null);Ar(f,p)}return null}function w(f,a,p,g,_){if(typeof g=="string"&&g!==""||typeof g=="number")return f=f.get(p)||null,l(a,f,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Sr:return f=f.get(g.key===null?p:g.key)||null,u(a,f,g,_);case Zt:return f=f.get(g.key===null?p:g.key)||null,c(a,f,g,_);case ht:var C=g._init;return w(f,a,p,C(g._payload),_)}if(Un(g)||jn(g))return f=f.get(p)||null,y(a,f,g,_,null);Ar(a,g)}return null}function x(f,a,p,g){for(var _=null,C=null,N=a,T=a=0,O=null;N!==null&&T<p.length;T++){N.index>T?(O=N,N=null):O=N.sibling;var j=h(f,N,p[T],g);if(j===null){N===null&&(N=O);break}e&&N&&j.alternate===null&&t(f,N),a=o(j,a,T),C===null?_=j:C.sibling=j,C=j,N=O}if(T===p.length)return n(f,N),H&&Dt(f,T),_;if(N===null){for(;T<p.length;T++)N=m(f,p[T],g),N!==null&&(a=o(N,a,T),C===null?_=N:C.sibling=N,C=N);return H&&Dt(f,T),_}for(N=r(f,N);T<p.length;T++)O=w(N,f,T,p[T],g),O!==null&&(e&&O.alternate!==null&&N.delete(O.key===null?T:O.key),a=o(O,a,T),C===null?_=O:C.sibling=O,C=O);return e&&N.forEach(function(ee){return t(f,ee)}),H&&Dt(f,T),_}function k(f,a,p,g){var _=jn(p);if(typeof _!="function")throw Error(v(150));if(p=_.call(p),p==null)throw Error(v(151));for(var C=_=null,N=a,T=a=0,O=null,j=p.next();N!==null&&!j.done;T++,j=p.next()){N.index>T?(O=N,N=null):O=N.sibling;var ee=h(f,N,j.value,g);if(ee===null){N===null&&(N=O);break}e&&N&&ee.alternate===null&&t(f,N),a=o(ee,a,T),C===null?_=ee:C.sibling=ee,C=ee,N=O}if(j.done)return n(f,N),H&&Dt(f,T),_;if(N===null){for(;!j.done;T++,j=p.next())j=m(f,j.value,g),j!==null&&(a=o(j,a,T),C===null?_=j:C.sibling=j,C=j);return H&&Dt(f,T),_}for(N=r(f,N);!j.done;T++,j=p.next())j=w(N,f,T,j.value,g),j!==null&&(e&&j.alternate!==null&&N.delete(j.key===null?T:j.key),a=o(j,a,T),C===null?_=j:C.sibling=j,C=j);return e&&N.forEach(function(Je){return t(f,Je)}),H&&Dt(f,T),_}function U(f,a,p,g){if(typeof p=="object"&&p!==null&&p.type===bt&&p.key===null&&(p=p.props.children),typeof p=="object"&&p!==null){switch(p.$$typeof){case Sr:e:{for(var _=p.key,C=a;C!==null;){if(C.key===_){if(_=p.type,_===bt){if(C.tag===7){n(f,C.sibling),a=i(C,p.props.children),a.return=f,f=a;break e}}else if(C.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===ht&&ru(_)===C.type){n(f,C.sibling),a=i(C,p.props),a.ref=In(f,C,p),a.return=f,f=a;break e}n(f,C);break}else t(f,C);C=C.sibling}p.type===bt?(a=Vt(p.props.children,f.mode,g,p.key),a.return=f,f=a):(g=Jr(p.type,p.key,p.props,null,f.mode,g),g.ref=In(f,a,p),g.return=f,f=g)}return s(f);case Zt:e:{for(C=p.key;a!==null;){if(a.key===C)if(a.tag===4&&a.stateNode.containerInfo===p.containerInfo&&a.stateNode.implementation===p.implementation){n(f,a.sibling),a=i(a,p.children||[]),a.return=f,f=a;break e}else{n(f,a);break}else t(f,a);a=a.sibling}a=wo(p,f.mode,g),a.return=f,f=a}return s(f);case ht:return C=p._init,U(f,a,C(p._payload),g)}if(Un(p))return x(f,a,p,g);if(jn(p))return k(f,a,p,g);Ar(f,p)}return typeof p=="string"&&p!==""||typeof p=="number"?(p=""+p,a!==null&&a.tag===6?(n(f,a.sibling),a=i(a,p),a.return=f,f=a):(n(f,a),a=vo(p,f.mode,g),a.return=f,f=a),s(f)):n(f,a)}return U}var xn=Ja(!0),Za=Ja(!1),mi=Ot(null),yi=null,un=null,Hs=null;function Qs(){Hs=un=yi=null}function qs(e){var t=mi.current;W(mi),e._currentValue=t}function Zo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function mn(e,t){yi=e,Hs=un=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ve=!0),e.firstContext=null)}function Ae(e){var t=e._currentValue;if(Hs!==e)if(e={context:e,memoizedValue:t,next:null},un===null){if(yi===null)throw Error(v(308));un=e,yi.dependencies={lanes:0,firstContext:e}}else un=un.next=e;return t}var Ft=null;function Ks(e){Ft===null?Ft=[e]:Ft.push(e)}function ba(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ks(t)):(n.next=i.next,i.next=n),t.interleaved=n,lt(e,r)}function lt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var mt=!1;function Ys(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ec(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function it(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Et(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,lt(e,n)}return i=r.interleaved,i===null?(t.next=t,Ks(r)):(t.next=i.next,i.next=t),r.interleaved=t,lt(e,n)}function Qr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,As(e,n)}}function iu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function gi(e,t,n,r){var i=e.updateQueue;mt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var u=l,c=u.next;u.next=null,s===null?o=c:s.next=c,s=u;var y=e.alternate;y!==null&&(y=y.updateQueue,l=y.lastBaseUpdate,l!==s&&(l===null?y.firstBaseUpdate=c:l.next=c,y.lastBaseUpdate=u))}if(o!==null){var m=i.baseState;s=0,y=c=u=null,l=o;do{var h=l.lane,w=l.eventTime;if((r&h)===h){y!==null&&(y=y.next={eventTime:w,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var x=e,k=l;switch(h=t,w=n,k.tag){case 1:if(x=k.payload,typeof x=="function"){m=x.call(w,m,h);break e}m=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=k.payload,h=typeof x=="function"?x.call(w,m,h):x,h==null)break e;m=Y({},m,h);break e;case 2:mt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else w={eventTime:w,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},y===null?(c=y=w,u=m):y=y.next=w,s|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(y===null&&(u=m),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=y,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);qt|=s,e.lanes=s,e.memoizedState=m}}function ou(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(v(191,i));i.call(r)}}}var xr={},Ye=Ot(xr),cr=Ot(xr),fr=Ot(xr);function Ut(e){if(e===xr)throw Error(v(174));return e}function Xs(e,t){switch($(fr,t),$(cr,e),$(Ye,xr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Oo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Oo(t,e)}W(Ye),$(Ye,t)}function kn(){W(Ye),W(cr),W(fr)}function tc(e){Ut(fr.current);var t=Ut(Ye.current),n=Oo(t,e.type);t!==n&&($(cr,e),$(Ye,n))}function Gs(e){cr.current===e&&(W(Ye),W(cr))}var q=Ot(0);function vi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var fo=[];function Js(){for(var e=0;e<fo.length;e++)fo[e]._workInProgressVersionPrimary=null;fo.length=0}var qr=at.ReactCurrentDispatcher,po=at.ReactCurrentBatchConfig,Qt=0,K=null,te=null,re=null,wi=!1,Xn=!1,dr=0,yp=0;function ue(){throw Error(v(321))}function Zs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!We(e[n],t[n]))return!1;return!0}function bs(e,t,n,r,i,o){if(Qt=o,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,qr.current=e===null||e.memoizedState===null?xp:kp,e=n(r,i),Xn){o=0;do{if(Xn=!1,dr=0,25<=o)throw Error(v(301));o+=1,re=te=null,t.updateQueue=null,qr.current=Sp,e=n(r,i)}while(Xn)}if(qr.current=xi,t=te!==null&&te.next!==null,Qt=0,re=te=K=null,wi=!1,t)throw Error(v(300));return e}function el(){var e=dr!==0;return dr=0,e}function Qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return re===null?K.memoizedState=re=e:re=re.next=e,re}function ze(){if(te===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=re===null?K.memoizedState:re.next;if(t!==null)re=t,te=e;else{if(e===null)throw Error(v(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},re===null?K.memoizedState=re=e:re=re.next=e}return re}function pr(e,t){return typeof t=="function"?t(e):t}function ho(e){var t=ze(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,u=null,c=o;do{var y=c.lane;if((Qt&y)===y)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:y,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(l=u=m,s=r):u=u.next=m,K.lanes|=y,qt|=y}c=c.next}while(c!==null&&c!==o);u===null?s=r:u.next=l,We(r,t.memoizedState)||(ve=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,K.lanes|=o,qt|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function mo(e){var t=ze(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);We(o,t.memoizedState)||(ve=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function nc(){}function rc(e,t){var n=K,r=ze(),i=t(),o=!We(r.memoizedState,i);if(o&&(r.memoizedState=i,ve=!0),r=r.queue,tl(sc.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||re!==null&&re.memoizedState.tag&1){if(n.flags|=2048,hr(9,oc.bind(null,n,r,i,t),void 0,null),ie===null)throw Error(v(349));Qt&30||ic(n,t,i)}return i}function ic(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function oc(e,t,n,r){t.value=n,t.getSnapshot=r,lc(t)&&uc(e)}function sc(e,t,n){return n(function(){lc(t)&&uc(e)})}function lc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!We(e,n)}catch{return!0}}function uc(e){var t=lt(e,1);t!==null&&Ve(t,e,1,-1)}function su(e){var t=Qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:pr,lastRenderedState:e},t.queue=e,e=e.dispatch=wp.bind(null,K,e),[t.memoizedState,e]}function hr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function ac(){return ze().memoizedState}function Kr(e,t,n,r){var i=Qe();K.flags|=e,i.memoizedState=hr(1|t,n,void 0,r===void 0?null:r)}function Ai(e,t,n,r){var i=ze();r=r===void 0?null:r;var o=void 0;if(te!==null){var s=te.memoizedState;if(o=s.destroy,r!==null&&Zs(r,s.deps)){i.memoizedState=hr(t,n,o,r);return}}K.flags|=e,i.memoizedState=hr(1|t,n,o,r)}function lu(e,t){return Kr(8390656,8,e,t)}function tl(e,t){return Ai(2048,8,e,t)}function cc(e,t){return Ai(4,2,e,t)}function fc(e,t){return Ai(4,4,e,t)}function dc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function pc(e,t,n){return n=n!=null?n.concat([e]):null,Ai(4,4,dc.bind(null,t,e),n)}function nl(){}function hc(e,t){var n=ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function mc(e,t){var n=ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Zs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function yc(e,t,n){return Qt&21?(We(n,t)||(n=ka(),K.lanes|=n,qt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ve=!0),e.memoizedState=n)}function gp(e,t){var n=F;F=n!==0&&4>n?n:4,e(!0);var r=po.transition;po.transition={};try{e(!1),t()}finally{F=n,po.transition=r}}function gc(){return ze().memoizedState}function vp(e,t,n){var r=Ct(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},vc(e))wc(t,n);else if(n=ba(e,t,n,r),n!==null){var i=pe();Ve(n,e,r,i),xc(n,t,r)}}function wp(e,t,n){var r=Ct(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(vc(e))wc(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,We(l,s)){var u=t.interleaved;u===null?(i.next=i,Ks(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=ba(e,t,i,r),n!==null&&(i=pe(),Ve(n,e,r,i),xc(n,t,r))}}function vc(e){var t=e.alternate;return e===K||t!==null&&t===K}function wc(e,t){Xn=wi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function xc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,As(e,n)}}var xi={readContext:Ae,useCallback:ue,useContext:ue,useEffect:ue,useImperativeHandle:ue,useInsertionEffect:ue,useLayoutEffect:ue,useMemo:ue,useReducer:ue,useRef:ue,useState:ue,useDebugValue:ue,useDeferredValue:ue,useTransition:ue,useMutableSource:ue,useSyncExternalStore:ue,useId:ue,unstable_isNewReconciler:!1},xp={readContext:Ae,useCallback:function(e,t){return Qe().memoizedState=[e,t===void 0?null:t],e},useContext:Ae,useEffect:lu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Kr(4194308,4,dc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Kr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Kr(4,2,e,t)},useMemo:function(e,t){var n=Qe();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Qe();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=vp.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=Qe();return e={current:e},t.memoizedState=e},useState:su,useDebugValue:nl,useDeferredValue:function(e){return Qe().memoizedState=e},useTransition:function(){var e=su(!1),t=e[0];return e=gp.bind(null,e[1]),Qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,i=Qe();if(H){if(n===void 0)throw Error(v(407));n=n()}else{if(n=t(),ie===null)throw Error(v(349));Qt&30||ic(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,lu(sc.bind(null,r,o,e),[e]),r.flags|=2048,hr(9,oc.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Qe(),t=ie.identifierPrefix;if(H){var n=rt,r=nt;n=(r&~(1<<32-$e(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=dr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=yp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},kp={readContext:Ae,useCallback:hc,useContext:Ae,useEffect:tl,useImperativeHandle:pc,useInsertionEffect:cc,useLayoutEffect:fc,useMemo:mc,useReducer:ho,useRef:ac,useState:function(){return ho(pr)},useDebugValue:nl,useDeferredValue:function(e){var t=ze();return yc(t,te.memoizedState,e)},useTransition:function(){var e=ho(pr)[0],t=ze().memoizedState;return[e,t]},useMutableSource:nc,useSyncExternalStore:rc,useId:gc,unstable_isNewReconciler:!1},Sp={readContext:Ae,useCallback:hc,useContext:Ae,useEffect:tl,useImperativeHandle:pc,useInsertionEffect:cc,useLayoutEffect:fc,useMemo:mc,useReducer:mo,useRef:ac,useState:function(){return mo(pr)},useDebugValue:nl,useDeferredValue:function(e){var t=ze();return te===null?t.memoizedState=e:yc(t,te.memoizedState,e)},useTransition:function(){var e=mo(pr)[0],t=ze().memoizedState;return[e,t]},useMutableSource:nc,useSyncExternalStore:rc,useId:gc,unstable_isNewReconciler:!1};function Me(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function bo(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var zi={isMounted:function(e){return(e=e._reactInternals)?Xt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=pe(),i=Ct(e),o=it(r,i);o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(Ve(t,e,i,r),Qr(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=pe(),i=Ct(e),o=it(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(Ve(t,e,i,r),Qr(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=pe(),r=Ct(e),i=it(n,r);i.tag=2,t!=null&&(i.callback=t),t=Et(e,i,r),t!==null&&(Ve(t,e,r,n),Qr(t,e,r))}};function uu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!sr(n,r)||!sr(i,o):!0}function kc(e,t,n){var r=!1,i=jt,o=t.contextType;return typeof o=="object"&&o!==null?o=Ae(o):(i=xe(t)?Wt:fe.current,r=t.contextTypes,o=(r=r!=null)?vn(e,i):jt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=zi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function au(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&zi.enqueueReplaceState(t,t.state,null)}function es(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Ys(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Ae(o):(o=xe(t)?Wt:fe.current,i.context=vn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(bo(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&zi.enqueueReplaceState(i,i.state,null),gi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Sn(e,t){try{var n="",r=t;do n+=Gf(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function yo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ts(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var _p=typeof WeakMap=="function"?WeakMap:Map;function Sc(e,t,n){n=it(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Si||(Si=!0,fs=r),ts(e,t)},n}function _c(e,t,n){n=it(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ts(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ts(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function cu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new _p;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Mp.bind(null,e,t,n),t.then(e,e))}function fu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function du(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=it(-1,1),t.tag=2,Et(n,t,1))),n.lanes|=1),e)}var Ep=at.ReactCurrentOwner,ve=!1;function de(e,t,n,r){t.child=e===null?Za(t,null,n,r):xn(t,e.child,n,r)}function pu(e,t,n,r,i){n=n.render;var o=t.ref;return mn(t,i),r=bs(e,t,n,r,o,i),n=el(),e!==null&&!ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ut(e,t,i)):(H&&n&&$s(t),t.flags|=1,de(e,t,r,i),t.child)}function hu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!cl(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Ec(e,t,o,r,i)):(e=Jr(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:sr,n(s,r)&&e.ref===t.ref)return ut(e,t,i)}return t.flags|=1,e=Tt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Ec(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(sr(o,r)&&e.ref===t.ref)if(ve=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(ve=!0);else return t.lanes=e.lanes,ut(e,t,i)}return ns(e,t,n,r,i)}function Nc(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(cn,Se),Se|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(cn,Se),Se|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,$(cn,Se),Se|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,$(cn,Se),Se|=r;return de(e,t,i,n),t.child}function Cc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ns(e,t,n,r,i){var o=xe(n)?Wt:fe.current;return o=vn(t,o),mn(t,i),n=bs(e,t,n,r,o,i),r=el(),e!==null&&!ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,ut(e,t,i)):(H&&r&&$s(t),t.flags|=1,de(e,t,n,i),t.child)}function mu(e,t,n,r,i){if(xe(n)){var o=!0;di(t)}else o=!1;if(mn(t,i),t.stateNode===null)Yr(e,t),kc(t,n,r),es(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ae(c):(c=xe(n)?Wt:fe.current,c=vn(t,c));var y=n.getDerivedStateFromProps,m=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function";m||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||u!==c)&&au(t,s,r,c),mt=!1;var h=t.memoizedState;s.state=h,gi(t,r,s,i),u=t.memoizedState,l!==r||h!==u||we.current||mt?(typeof y=="function"&&(bo(t,n,y,r),u=t.memoizedState),(l=mt||uu(t,n,l,r,h,u,c))?(m||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,ec(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Me(t.type,l),s.props=c,m=t.pendingProps,h=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ae(u):(u=xe(n)?Wt:fe.current,u=vn(t,u));var w=n.getDerivedStateFromProps;(y=typeof w=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==m||h!==u)&&au(t,s,r,u),mt=!1,h=t.memoizedState,s.state=h,gi(t,r,s,i);var x=t.memoizedState;l!==m||h!==x||we.current||mt?(typeof w=="function"&&(bo(t,n,w,r),x=t.memoizedState),(c=mt||uu(t,n,c,r,h,x,u)||!1)?(y||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return rs(e,t,n,r,o,i)}function rs(e,t,n,r,i,o){Cc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&eu(t,n,!1),ut(e,t,o);r=t.stateNode,Ep.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=xn(t,e.child,null,o),t.child=xn(t,null,l,o)):de(e,t,l,o),t.memoizedState=r.state,i&&eu(t,n,!0),t.child}function Tc(e){var t=e.stateNode;t.pendingContext?bl(e,t.pendingContext,t.pendingContext!==t.context):t.context&&bl(e,t.context,!1),Xs(e,t.containerInfo)}function yu(e,t,n,r,i){return wn(),Ws(i),t.flags|=256,de(e,t,n,r),t.child}var is={dehydrated:null,treeContext:null,retryLane:0};function os(e){return{baseLanes:e,cachePool:null,transitions:null}}function Rc(e,t,n){var r=t.pendingProps,i=q.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),$(q,i&1),e===null)return Jo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Mi(s,r,0,null),e=Vt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=os(n),t.memoizedState=is,e):rl(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Np(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Tt(i,u),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Tt(l,o):(o=Vt(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?os(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=is,r}return o=e.child,e=o.sibling,r=Tt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function rl(e,t){return t=Mi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function zr(e,t,n,r){return r!==null&&Ws(r),xn(t,e.child,null,n),e=rl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Np(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=yo(Error(v(422))),zr(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Mi({mode:"visible",children:r.children},i,0,null),o=Vt(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&xn(t,e.child,null,s),t.child.memoizedState=os(s),t.memoizedState=is,o);if(!(t.mode&1))return zr(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(v(419)),r=yo(o,r,void 0),zr(e,t,s,r)}if(l=(s&e.childLanes)!==0,ve||l){if(r=ie,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,lt(e,i),Ve(r,e,i,-1))}return al(),r=yo(Error(v(421))),zr(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Bp.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,_e=_t(i.nextSibling),Ee=t,H=!0,Ue=null,e!==null&&(Re[Pe++]=nt,Re[Pe++]=rt,Re[Pe++]=Ht,nt=e.id,rt=e.overflow,Ht=t),t=rl(t,r.children),t.flags|=4096,t)}function gu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Zo(e.return,t,n)}function go(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Pc(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(de(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&gu(e,n,t);else if(e.tag===19)gu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&vi(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),go(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&vi(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}go(t,!0,n,null,o);break;case"together":go(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function ut(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),qt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(v(153));if(t.child!==null){for(e=t.child,n=Tt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Cp(e,t,n){switch(t.tag){case 3:Tc(t),wn();break;case 5:tc(t);break;case 1:xe(t.type)&&di(t);break;case 4:Xs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;$(mi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?Rc(e,t,n):($(q,q.current&1),e=ut(e,t,n),e!==null?e.sibling:null);$(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Pc(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Nc(e,t,n)}return ut(e,t,n)}var jc,ss,Lc,Oc;jc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ss=function(){};Lc=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Ut(Ye.current);var o=null;switch(n){case"input":i=Ro(e,i),r=Ro(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=Lo(e,i),r=Lo(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ci)}Ao(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var l=i[c];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(bn.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(u!=null||l!=null))if(c==="style")if(l){for(s in l)!l.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&l[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,l=l?l.__html:void 0,u!=null&&l!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(bn.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&V("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Oc=function(e,t,n,r){n!==r&&(t.flags|=4)};function Dn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ae(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Tp(e,t,n){var r=t.pendingProps;switch(Vs(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ae(t),null;case 1:return xe(t.type)&&fi(),ae(t),null;case 3:return r=t.stateNode,kn(),W(we),W(fe),Js(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Or(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ue!==null&&(hs(Ue),Ue=null))),ss(e,t),ae(t),null;case 5:Gs(t);var i=Ut(fr.current);if(n=t.type,e!==null&&t.stateNode!=null)Lc(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(v(166));return ae(t),null}if(e=Ut(Ye.current),Or(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[qe]=t,r[ar]=o,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(i=0;i<Vn.length;i++)V(Vn[i],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":Cl(r,o),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},V("invalid",r);break;case"textarea":Rl(r,o),V("invalid",r)}Ao(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&Lr(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&Lr(r.textContent,l,e),i=["children",""+l]):bn.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&V("scroll",r)}switch(n){case"input":_r(r),Tl(r,o,!0);break;case"textarea":_r(r),Pl(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=ci)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=sa(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[qe]=t,e[ar]=r,jc(e,t,!1,!1),t.stateNode=e;e:{switch(s=zo(n,r),n){case"dialog":V("cancel",e),V("close",e),i=r;break;case"iframe":case"object":case"embed":V("load",e),i=r;break;case"video":case"audio":for(i=0;i<Vn.length;i++)V(Vn[i],e);i=r;break;case"source":V("error",e),i=r;break;case"img":case"image":case"link":V("error",e),V("load",e),i=r;break;case"details":V("toggle",e),i=r;break;case"input":Cl(e,r),i=Ro(e,r),V("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),V("invalid",e);break;case"textarea":Rl(e,r),i=Lo(e,r),V("invalid",e);break;default:i=r}Ao(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var u=l[o];o==="style"?aa(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&la(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&er(e,u):typeof u=="number"&&er(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(bn.hasOwnProperty(o)?u!=null&&o==="onScroll"&&V("scroll",e):u!=null&&Ts(e,o,u,s))}switch(n){case"input":_r(e),Tl(e,r,!1);break;case"textarea":_r(e),Pl(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Pt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?fn(e,!!r.multiple,o,!1):r.defaultValue!=null&&fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ci)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ae(t),null;case 6:if(e&&t.stateNode!=null)Oc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(v(166));if(n=Ut(fr.current),Ut(Ye.current),Or(t)){if(r=t.stateNode,n=t.memoizedProps,r[qe]=t,(o=r.nodeValue!==n)&&(e=Ee,e!==null))switch(e.tag){case 3:Lr(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Lr(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[qe]=t,t.stateNode=r}return ae(t),null;case 13:if(W(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&_e!==null&&t.mode&1&&!(t.flags&128))Ga(),wn(),t.flags|=98560,o=!1;else if(o=Or(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(v(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(v(317));o[qe]=t}else wn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ae(t),o=!1}else Ue!==null&&(hs(Ue),Ue=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?ne===0&&(ne=3):al())),t.updateQueue!==null&&(t.flags|=4),ae(t),null);case 4:return kn(),ss(e,t),e===null&&lr(t.stateNode.containerInfo),ae(t),null;case 10:return qs(t.type._context),ae(t),null;case 17:return xe(t.type)&&fi(),ae(t),null;case 19:if(W(q),o=t.memoizedState,o===null)return ae(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Dn(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=vi(e),s!==null){for(t.flags|=128,Dn(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(q,q.current&1|2),t.child}e=e.sibling}o.tail!==null&&G()>_n&&(t.flags|=128,r=!0,Dn(o,!1),t.lanes=4194304)}else{if(!r)if(e=vi(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Dn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return ae(t),null}else 2*G()-o.renderingStartTime>_n&&n!==1073741824&&(t.flags|=128,r=!0,Dn(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=G(),t.sibling=null,n=q.current,$(q,r?n&1|2:n&1),t):(ae(t),null);case 22:case 23:return ul(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Se&1073741824&&(ae(t),t.subtreeFlags&6&&(t.flags|=8192)):ae(t),null;case 24:return null;case 25:return null}throw Error(v(156,t.tag))}function Rp(e,t){switch(Vs(t),t.tag){case 1:return xe(t.type)&&fi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return kn(),W(we),W(fe),Js(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Gs(t),null;case 13:if(W(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(v(340));wn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(q),null;case 4:return kn(),null;case 10:return qs(t.type._context),null;case 22:case 23:return ul(),null;case 24:return null;default:return null}}var Ir=!1,ce=!1,Pp=typeof WeakSet=="function"?WeakSet:Set,E=null;function an(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){X(e,t,r)}else n.current=null}function ls(e,t,n){try{n()}catch(r){X(e,t,r)}}var vu=!1;function jp(e,t){if(Ho=li,e=Ma(),Us(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,u=-1,c=0,y=0,m=e,h=null;t:for(;;){for(var w;m!==n||i!==0&&m.nodeType!==3||(l=s+i),m!==o||r!==0&&m.nodeType!==3||(u=s+r),m.nodeType===3&&(s+=m.nodeValue.length),(w=m.firstChild)!==null;)h=m,m=w;for(;;){if(m===e)break t;if(h===n&&++c===i&&(l=s),h===o&&++y===r&&(u=s),(w=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=w}n=l===-1||u===-1?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qo={focusedElem:e,selectionRange:n},li=!1,E=t;E!==null;)if(t=E,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,E=e;else for(;E!==null;){t=E;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var k=x.memoizedProps,U=x.memoizedState,f=t.stateNode,a=f.getSnapshotBeforeUpdate(t.elementType===t.type?k:Me(t.type,k),U);f.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var p=t.stateNode.containerInfo;p.nodeType===1?p.textContent="":p.nodeType===9&&p.documentElement&&p.removeChild(p.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(v(163))}}catch(g){X(t,t.return,g)}if(e=t.sibling,e!==null){e.return=t.return,E=e;break}E=t.return}return x=vu,vu=!1,x}function Gn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&ls(t,n,o)}i=i.next}while(i!==r)}}function Ii(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function us(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ac(e){var t=e.alternate;t!==null&&(e.alternate=null,Ac(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[qe],delete t[ar],delete t[Yo],delete t[dp],delete t[pp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function zc(e){return e.tag===5||e.tag===3||e.tag===4}function wu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||zc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function as(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ci));else if(r!==4&&(e=e.child,e!==null))for(as(e,t,n),e=e.sibling;e!==null;)as(e,t,n),e=e.sibling}function cs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(cs(e,t,n),e=e.sibling;e!==null;)cs(e,t,n),e=e.sibling}var oe=null,Be=!1;function pt(e,t,n){for(n=n.child;n!==null;)Ic(e,t,n),n=n.sibling}function Ic(e,t,n){if(Ke&&typeof Ke.onCommitFiberUnmount=="function")try{Ke.onCommitFiberUnmount(Ti,n)}catch{}switch(n.tag){case 5:ce||an(n,t);case 6:var r=oe,i=Be;oe=null,pt(e,t,n),oe=r,Be=i,oe!==null&&(Be?(e=oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):oe.removeChild(n.stateNode));break;case 18:oe!==null&&(Be?(e=oe,n=n.stateNode,e.nodeType===8?ao(e.parentNode,n):e.nodeType===1&&ao(e,n),ir(e)):ao(oe,n.stateNode));break;case 4:r=oe,i=Be,oe=n.stateNode.containerInfo,Be=!0,pt(e,t,n),oe=r,Be=i;break;case 0:case 11:case 14:case 15:if(!ce&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&ls(n,t,s),i=i.next}while(i!==r)}pt(e,t,n);break;case 1:if(!ce&&(an(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){X(n,t,l)}pt(e,t,n);break;case 21:pt(e,t,n);break;case 22:n.mode&1?(ce=(r=ce)||n.memoizedState!==null,pt(e,t,n),ce=r):pt(e,t,n);break;default:pt(e,t,n)}}function xu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Pp),t.forEach(function(r){var i=Fp.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function De(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:oe=l.stateNode,Be=!1;break e;case 3:oe=l.stateNode.containerInfo,Be=!0;break e;case 4:oe=l.stateNode.containerInfo,Be=!0;break e}l=l.return}if(oe===null)throw Error(v(160));Ic(o,s,i),oe=null,Be=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(c){X(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Dc(t,e),t=t.sibling}function Dc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(De(t,e),He(e),r&4){try{Gn(3,e,e.return),Ii(3,e)}catch(k){X(e,e.return,k)}try{Gn(5,e,e.return)}catch(k){X(e,e.return,k)}}break;case 1:De(t,e),He(e),r&512&&n!==null&&an(n,n.return);break;case 5:if(De(t,e),He(e),r&512&&n!==null&&an(n,n.return),e.flags&32){var i=e.stateNode;try{er(i,"")}catch(k){X(e,e.return,k)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&ia(i,o),zo(l,s);var c=zo(l,o);for(s=0;s<u.length;s+=2){var y=u[s],m=u[s+1];y==="style"?aa(i,m):y==="dangerouslySetInnerHTML"?la(i,m):y==="children"?er(i,m):Ts(i,y,m,c)}switch(l){case"input":Po(i,o);break;case"textarea":oa(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var w=o.value;w!=null?fn(i,!!o.multiple,w,!1):h!==!!o.multiple&&(o.defaultValue!=null?fn(i,!!o.multiple,o.defaultValue,!0):fn(i,!!o.multiple,o.multiple?[]:"",!1))}i[ar]=o}catch(k){X(e,e.return,k)}}break;case 6:if(De(t,e),He(e),r&4){if(e.stateNode===null)throw Error(v(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(k){X(e,e.return,k)}}break;case 3:if(De(t,e),He(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ir(t.containerInfo)}catch(k){X(e,e.return,k)}break;case 4:De(t,e),He(e);break;case 13:De(t,e),He(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(sl=G())),r&4&&xu(e);break;case 22:if(y=n!==null&&n.memoizedState!==null,e.mode&1?(ce=(c=ce)||y,De(t,e),ce=c):De(t,e),He(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!y&&e.mode&1)for(E=e,y=e.child;y!==null;){for(m=E=y;E!==null;){switch(h=E,w=h.child,h.tag){case 0:case 11:case 14:case 15:Gn(4,h,h.return);break;case 1:an(h,h.return);var x=h.stateNode;if(typeof x.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(k){X(r,n,k)}}break;case 5:an(h,h.return);break;case 22:if(h.memoizedState!==null){Su(m);continue}}w!==null?(w.return=h,E=w):Su(m)}y=y.sibling}e:for(y=null,m=e;;){if(m.tag===5){if(y===null){y=m;try{i=m.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=m.stateNode,u=m.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,l.style.display=ua("display",s))}catch(k){X(e,e.return,k)}}}else if(m.tag===6){if(y===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(k){X(e,e.return,k)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;y===m&&(y=null),m=m.return}y===m&&(y=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:De(t,e),He(e),r&4&&xu(e);break;case 21:break;default:De(t,e),He(e)}}function He(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(zc(n)){var r=n;break e}n=n.return}throw Error(v(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(er(i,""),r.flags&=-33);var o=wu(e);cs(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=wu(e);as(e,l,s);break;default:throw Error(v(161))}}catch(u){X(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Lp(e,t,n){E=e,Mc(e)}function Mc(e,t,n){for(var r=(e.mode&1)!==0;E!==null;){var i=E,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Ir;if(!s){var l=i.alternate,u=l!==null&&l.memoizedState!==null||ce;l=Ir;var c=ce;if(Ir=s,(ce=u)&&!c)for(E=i;E!==null;)s=E,u=s.child,s.tag===22&&s.memoizedState!==null?_u(i):u!==null?(u.return=s,E=u):_u(i);for(;o!==null;)E=o,Mc(o),o=o.sibling;E=i,Ir=l,ce=c}ku(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,E=o):ku(e)}}function ku(e){for(;E!==null;){var t=E;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ce||Ii(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ce)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Me(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&ou(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}ou(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var y=c.memoizedState;if(y!==null){var m=y.dehydrated;m!==null&&ir(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(v(163))}ce||t.flags&512&&us(t)}catch(h){X(t,t.return,h)}}if(t===e){E=null;break}if(n=t.sibling,n!==null){n.return=t.return,E=n;break}E=t.return}}function Su(e){for(;E!==null;){var t=E;if(t===e){E=null;break}var n=t.sibling;if(n!==null){n.return=t.return,E=n;break}E=t.return}}function _u(e){for(;E!==null;){var t=E;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ii(4,t)}catch(u){X(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){X(t,i,u)}}var o=t.return;try{us(t)}catch(u){X(t,o,u)}break;case 5:var s=t.return;try{us(t)}catch(u){X(t,s,u)}}}catch(u){X(t,t.return,u)}if(t===e){E=null;break}var l=t.sibling;if(l!==null){l.return=t.return,E=l;break}E=t.return}}var Op=Math.ceil,ki=at.ReactCurrentDispatcher,il=at.ReactCurrentOwner,Oe=at.ReactCurrentBatchConfig,B=0,ie=null,Z=null,se=0,Se=0,cn=Ot(0),ne=0,mr=null,qt=0,Di=0,ol=0,Jn=null,ge=null,sl=0,_n=1/0,et=null,Si=!1,fs=null,Nt=null,Dr=!1,wt=null,_i=0,Zn=0,ds=null,Xr=-1,Gr=0;function pe(){return B&6?G():Xr!==-1?Xr:Xr=G()}function Ct(e){return e.mode&1?B&2&&se!==0?se&-se:mp.transition!==null?(Gr===0&&(Gr=ka()),Gr):(e=F,e!==0||(e=window.event,e=e===void 0?16:Ra(e.type)),e):1}function Ve(e,t,n,r){if(50<Zn)throw Zn=0,ds=null,Error(v(185));gr(e,n,r),(!(B&2)||e!==ie)&&(e===ie&&(!(B&2)&&(Di|=n),ne===4&&gt(e,se)),ke(e,r),n===1&&B===0&&!(t.mode&1)&&(_n=G()+500,Oi&&At()))}function ke(e,t){var n=e.callbackNode;md(e,t);var r=si(e,e===ie?se:0);if(r===0)n!==null&&Ol(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ol(n),t===1)e.tag===0?hp(Eu.bind(null,e)):Ka(Eu.bind(null,e)),cp(function(){!(B&6)&&At()}),n=null;else{switch(Sa(r)){case 1:n=Os;break;case 4:n=wa;break;case 16:n=oi;break;case 536870912:n=xa;break;default:n=oi}n=Qc(n,Bc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Bc(e,t){if(Xr=-1,Gr=0,B&6)throw Error(v(327));var n=e.callbackNode;if(yn()&&e.callbackNode!==n)return null;var r=si(e,e===ie?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ei(e,r);else{t=r;var i=B;B|=2;var o=Uc();(ie!==e||se!==t)&&(et=null,_n=G()+500,$t(e,t));do try{Ip();break}catch(l){Fc(e,l)}while(1);Qs(),ki.current=o,B=i,Z!==null?t=0:(ie=null,se=0,t=ne)}if(t!==0){if(t===2&&(i=Fo(e),i!==0&&(r=i,t=ps(e,i))),t===1)throw n=mr,$t(e,0),gt(e,r),ke(e,G()),n;if(t===6)gt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Ap(i)&&(t=Ei(e,r),t===2&&(o=Fo(e),o!==0&&(r=o,t=ps(e,o))),t===1))throw n=mr,$t(e,0),gt(e,r),ke(e,G()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(v(345));case 2:Mt(e,ge,et);break;case 3:if(gt(e,r),(r&130023424)===r&&(t=sl+500-G(),10<t)){if(si(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){pe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Ko(Mt.bind(null,e,ge,et),t);break}Mt(e,ge,et);break;case 4:if(gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-$e(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=G()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Op(r/1960))-r,10<r){e.timeoutHandle=Ko(Mt.bind(null,e,ge,et),r);break}Mt(e,ge,et);break;case 5:Mt(e,ge,et);break;default:throw Error(v(329))}}}return ke(e,G()),e.callbackNode===n?Bc.bind(null,e):null}function ps(e,t){var n=Jn;return e.current.memoizedState.isDehydrated&&($t(e,t).flags|=256),e=Ei(e,t),e!==2&&(t=ge,ge=n,t!==null&&hs(t)),e}function hs(e){ge===null?ge=e:ge.push.apply(ge,e)}function Ap(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!We(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gt(e,t){for(t&=~ol,t&=~Di,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-$e(t),r=1<<n;e[n]=-1,t&=~r}}function Eu(e){if(B&6)throw Error(v(327));yn();var t=si(e,0);if(!(t&1))return ke(e,G()),null;var n=Ei(e,t);if(e.tag!==0&&n===2){var r=Fo(e);r!==0&&(t=r,n=ps(e,r))}if(n===1)throw n=mr,$t(e,0),gt(e,t),ke(e,G()),n;if(n===6)throw Error(v(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Mt(e,ge,et),ke(e,G()),null}function ll(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(_n=G()+500,Oi&&At())}}function Kt(e){wt!==null&&wt.tag===0&&!(B&6)&&yn();var t=B;B|=1;var n=Oe.transition,r=F;try{if(Oe.transition=null,F=1,e)return e()}finally{F=r,Oe.transition=n,B=t,!(B&6)&&At()}}function ul(){Se=cn.current,W(cn)}function $t(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ap(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Vs(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&fi();break;case 3:kn(),W(we),W(fe),Js();break;case 5:Gs(r);break;case 4:kn();break;case 13:W(q);break;case 19:W(q);break;case 10:qs(r.type._context);break;case 22:case 23:ul()}n=n.return}if(ie=e,Z=e=Tt(e.current,null),se=Se=t,ne=0,mr=null,ol=Di=qt=0,ge=Jn=null,Ft!==null){for(t=0;t<Ft.length;t++)if(n=Ft[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}Ft=null}return e}function Fc(e,t){do{var n=Z;try{if(Qs(),qr.current=xi,wi){for(var r=K.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}wi=!1}if(Qt=0,re=te=K=null,Xn=!1,dr=0,il.current=null,n===null||n.return===null){ne=1,mr=t,Z=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=se,l.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,y=l,m=y.tag;if(!(y.mode&1)&&(m===0||m===11||m===15)){var h=y.alternate;h?(y.updateQueue=h.updateQueue,y.memoizedState=h.memoizedState,y.lanes=h.lanes):(y.updateQueue=null,y.memoizedState=null)}var w=fu(s);if(w!==null){w.flags&=-257,du(w,s,l,o,t),w.mode&1&&cu(o,c,t),t=w,u=c;var x=t.updateQueue;if(x===null){var k=new Set;k.add(u),t.updateQueue=k}else x.add(u);break e}else{if(!(t&1)){cu(o,c,t),al();break e}u=Error(v(426))}}else if(H&&l.mode&1){var U=fu(s);if(U!==null){!(U.flags&65536)&&(U.flags|=256),du(U,s,l,o,t),Ws(Sn(u,l));break e}}o=u=Sn(u,l),ne!==4&&(ne=2),Jn===null?Jn=[o]:Jn.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var f=Sc(o,u,t);iu(o,f);break e;case 1:l=u;var a=o.type,p=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||p!==null&&typeof p.componentDidCatch=="function"&&(Nt===null||!Nt.has(p)))){o.flags|=65536,t&=-t,o.lanes|=t;var g=_c(o,l,t);iu(o,g);break e}}o=o.return}while(o!==null)}Vc(n)}catch(_){t=_,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(1)}function Uc(){var e=ki.current;return ki.current=xi,e===null?xi:e}function al(){(ne===0||ne===3||ne===2)&&(ne=4),ie===null||!(qt&268435455)&&!(Di&268435455)||gt(ie,se)}function Ei(e,t){var n=B;B|=2;var r=Uc();(ie!==e||se!==t)&&(et=null,$t(e,t));do try{zp();break}catch(i){Fc(e,i)}while(1);if(Qs(),B=n,ki.current=r,Z!==null)throw Error(v(261));return ie=null,se=0,ne}function zp(){for(;Z!==null;)$c(Z)}function Ip(){for(;Z!==null&&!sd();)$c(Z)}function $c(e){var t=Hc(e.alternate,e,Se);e.memoizedProps=e.pendingProps,t===null?Vc(e):Z=t,il.current=null}function Vc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Rp(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,Z=null;return}}else if(n=Tp(n,t,Se),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);ne===0&&(ne=5)}function Mt(e,t,n){var r=F,i=Oe.transition;try{Oe.transition=null,F=1,Dp(e,t,n,r)}finally{Oe.transition=i,F=r}return null}function Dp(e,t,n,r){do yn();while(wt!==null);if(B&6)throw Error(v(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(v(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(yd(e,o),e===ie&&(Z=ie=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Dr||(Dr=!0,Qc(oi,function(){return yn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Oe.transition,Oe.transition=null;var s=F;F=1;var l=B;B|=4,il.current=null,jp(e,n),Dc(n,e),np(Qo),li=!!Ho,Qo=Ho=null,e.current=n,Lp(n),ld(),B=l,F=s,Oe.transition=o}else e.current=n;if(Dr&&(Dr=!1,wt=e,_i=i),o=e.pendingLanes,o===0&&(Nt=null),cd(n.stateNode),ke(e,G()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Si)throw Si=!1,e=fs,fs=null,e;return _i&1&&e.tag!==0&&yn(),o=e.pendingLanes,o&1?e===ds?Zn++:(Zn=0,ds=e):Zn=0,At(),null}function yn(){if(wt!==null){var e=Sa(_i),t=Oe.transition,n=F;try{if(Oe.transition=null,F=16>e?16:e,wt===null)var r=!1;else{if(e=wt,wt=null,_i=0,B&6)throw Error(v(331));var i=B;for(B|=4,E=e.current;E!==null;){var o=E,s=o.child;if(E.flags&16){var l=o.deletions;if(l!==null){for(var u=0;u<l.length;u++){var c=l[u];for(E=c;E!==null;){var y=E;switch(y.tag){case 0:case 11:case 15:Gn(8,y,o)}var m=y.child;if(m!==null)m.return=y,E=m;else for(;E!==null;){y=E;var h=y.sibling,w=y.return;if(Ac(y),y===c){E=null;break}if(h!==null){h.return=w,E=h;break}E=w}}}var x=o.alternate;if(x!==null){var k=x.child;if(k!==null){x.child=null;do{var U=k.sibling;k.sibling=null,k=U}while(k!==null)}}E=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,E=s;else e:for(;E!==null;){if(o=E,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Gn(9,o,o.return)}var f=o.sibling;if(f!==null){f.return=o.return,E=f;break e}E=o.return}}var a=e.current;for(E=a;E!==null;){s=E;var p=s.child;if(s.subtreeFlags&2064&&p!==null)p.return=s,E=p;else e:for(s=a;E!==null;){if(l=E,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ii(9,l)}}catch(_){X(l,l.return,_)}if(l===s){E=null;break e}var g=l.sibling;if(g!==null){g.return=l.return,E=g;break e}E=l.return}}if(B=i,At(),Ke&&typeof Ke.onPostCommitFiberRoot=="function")try{Ke.onPostCommitFiberRoot(Ti,e)}catch{}r=!0}return r}finally{F=n,Oe.transition=t}}return!1}function Nu(e,t,n){t=Sn(n,t),t=Sc(e,t,1),e=Et(e,t,1),t=pe(),e!==null&&(gr(e,1,t),ke(e,t))}function X(e,t,n){if(e.tag===3)Nu(e,e,n);else for(;t!==null;){if(t.tag===3){Nu(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=Sn(n,e),e=_c(t,e,1),t=Et(t,e,1),e=pe(),t!==null&&(gr(t,1,e),ke(t,e));break}}t=t.return}}function Mp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=pe(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(se&n)===n&&(ne===4||ne===3&&(se&130023424)===se&&500>G()-sl?$t(e,0):ol|=n),ke(e,t)}function Wc(e,t){t===0&&(e.mode&1?(t=Cr,Cr<<=1,!(Cr&130023424)&&(Cr=4194304)):t=1);var n=pe();e=lt(e,t),e!==null&&(gr(e,t,n),ke(e,n))}function Bp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Wc(e,n)}function Fp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(v(314))}r!==null&&r.delete(t),Wc(e,n)}var Hc;Hc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||we.current)ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ve=!1,Cp(e,t,n);ve=!!(e.flags&131072)}else ve=!1,H&&t.flags&1048576&&Ya(t,hi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Yr(e,t),e=t.pendingProps;var i=vn(t,fe.current);mn(t,n),i=bs(null,t,r,e,i,n);var o=el();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xe(r)?(o=!0,di(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Ys(t),i.updater=zi,t.stateNode=i,i._reactInternals=t,es(t,r,e,n),t=rs(null,t,r,!0,o,n)):(t.tag=0,H&&o&&$s(t),de(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Yr(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=$p(r),e=Me(r,e),i){case 0:t=ns(null,t,r,e,n);break e;case 1:t=mu(null,t,r,e,n);break e;case 11:t=pu(null,t,r,e,n);break e;case 14:t=hu(null,t,r,Me(r.type,e),n);break e}throw Error(v(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Me(r,i),ns(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Me(r,i),mu(e,t,r,i,n);case 3:e:{if(Tc(t),e===null)throw Error(v(387));r=t.pendingProps,o=t.memoizedState,i=o.element,ec(e,t),gi(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Sn(Error(v(423)),t),t=yu(e,t,r,n,i);break e}else if(r!==i){i=Sn(Error(v(424)),t),t=yu(e,t,r,n,i);break e}else for(_e=_t(t.stateNode.containerInfo.firstChild),Ee=t,H=!0,Ue=null,n=Za(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(wn(),r===i){t=ut(e,t,n);break e}de(e,t,r,n)}t=t.child}return t;case 5:return tc(t),e===null&&Jo(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,qo(r,i)?s=null:o!==null&&qo(r,o)&&(t.flags|=32),Cc(e,t),de(e,t,s,n),t.child;case 6:return e===null&&Jo(t),null;case 13:return Rc(e,t,n);case 4:return Xs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=xn(t,null,r,n):de(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Me(r,i),pu(e,t,r,i,n);case 7:return de(e,t,t.pendingProps,n),t.child;case 8:return de(e,t,t.pendingProps.children,n),t.child;case 12:return de(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,$(mi,r._currentValue),r._currentValue=s,o!==null)if(We(o.value,s)){if(o.children===i.children&&!we.current){t=ut(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var u=l.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=it(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var y=c.pending;y===null?u.next=u:(u.next=y.next,y.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),Zo(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(v(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Zo(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}de(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,mn(t,n),i=Ae(i),r=r(i),t.flags|=1,de(e,t,r,n),t.child;case 14:return r=t.type,i=Me(r,t.pendingProps),i=Me(r.type,i),hu(e,t,r,i,n);case 15:return Ec(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Me(r,i),Yr(e,t),t.tag=1,xe(r)?(e=!0,di(t)):e=!1,mn(t,n),kc(t,r,i),es(t,r,i,n),rs(null,t,r,!0,e,n);case 19:return Pc(e,t,n);case 22:return Nc(e,t,n)}throw Error(v(156,t.tag))};function Qc(e,t){return va(e,t)}function Up(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Le(e,t,n,r){return new Up(e,t,n,r)}function cl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function $p(e){if(typeof e=="function")return cl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ps)return 11;if(e===js)return 14}return 2}function Tt(e,t){var n=e.alternate;return n===null?(n=Le(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Jr(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")cl(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case bt:return Vt(n.children,i,o,t);case Rs:s=8,i|=8;break;case Eo:return e=Le(12,n,t,i|2),e.elementType=Eo,e.lanes=o,e;case No:return e=Le(13,n,t,i),e.elementType=No,e.lanes=o,e;case Co:return e=Le(19,n,t,i),e.elementType=Co,e.lanes=o,e;case ta:return Mi(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case bu:s=10;break e;case ea:s=9;break e;case Ps:s=11;break e;case js:s=14;break e;case ht:s=16,r=null;break e}throw Error(v(130,e==null?e:typeof e,""))}return t=Le(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Vt(e,t,n,r){return e=Le(7,e,r,t),e.lanes=n,e}function Mi(e,t,n,r){return e=Le(22,e,r,t),e.elementType=ta,e.lanes=n,e.stateNode={isHidden:!1},e}function vo(e,t,n){return e=Le(6,e,null,t),e.lanes=n,e}function wo(e,t,n){return t=Le(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Vp(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Zi(0),this.expirationTimes=Zi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zi(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function fl(e,t,n,r,i,o,s,l,u){return e=new Vp(e,t,n,l,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Le(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ys(o),e}function Wp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Zt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function qc(e){if(!e)return jt;e=e._reactInternals;e:{if(Xt(e)!==e||e.tag!==1)throw Error(v(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(v(171))}if(e.tag===1){var n=e.type;if(xe(n))return qa(e,n,t)}return t}function Kc(e,t,n,r,i,o,s,l,u){return e=fl(n,r,!0,e,i,o,s,l,u),e.context=qc(null),n=e.current,r=pe(),i=Ct(n),o=it(r,i),o.callback=t??null,Et(n,o,i),e.current.lanes=i,gr(e,i,r),ke(e,r),e}function Bi(e,t,n,r){var i=t.current,o=pe(),s=Ct(i);return n=qc(n),t.context===null?t.context=n:t.pendingContext=n,t=it(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Et(i,t,s),e!==null&&(Ve(e,i,s,o),Qr(e,i,s)),s}function Ni(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Cu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function dl(e,t){Cu(e,t),(e=e.alternate)&&Cu(e,t)}function Hp(){return null}var Yc=typeof reportError=="function"?reportError:function(e){console.error(e)};function pl(e){this._internalRoot=e}Fi.prototype.render=pl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(v(409));Bi(e,t,null,null)};Fi.prototype.unmount=pl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Kt(function(){Bi(null,e,null,null)}),t[st]=null}};function Fi(e){this._internalRoot=e}Fi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Na();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yt.length&&t!==0&&t<yt[n].priority;n++);yt.splice(n,0,e),n===0&&Ta(e)}};function hl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ui(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Tu(){}function Qp(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=Ni(s);o.call(c)}}var s=Kc(t,r,e,0,null,!1,!1,"",Tu);return e._reactRootContainer=s,e[st]=s.current,lr(e.nodeType===8?e.parentNode:e),Kt(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var c=Ni(u);l.call(c)}}var u=fl(e,0,!1,null,null,!1,!1,"",Tu);return e._reactRootContainer=u,e[st]=u.current,lr(e.nodeType===8?e.parentNode:e),Kt(function(){Bi(t,u,n,r)}),u}function $i(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var u=Ni(s);l.call(u)}}Bi(t,s,e,i)}else s=Qp(n,t,e,i,r);return Ni(s)}_a=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=$n(t.pendingLanes);n!==0&&(As(t,n|1),ke(t,G()),!(B&6)&&(_n=G()+500,At()))}break;case 13:Kt(function(){var r=lt(e,1);if(r!==null){var i=pe();Ve(r,e,1,i)}}),dl(e,1)}};zs=function(e){if(e.tag===13){var t=lt(e,134217728);if(t!==null){var n=pe();Ve(t,e,134217728,n)}dl(e,134217728)}};Ea=function(e){if(e.tag===13){var t=Ct(e),n=lt(e,t);if(n!==null){var r=pe();Ve(n,e,t,r)}dl(e,t)}};Na=function(){return F};Ca=function(e,t){var n=F;try{return F=e,t()}finally{F=n}};Do=function(e,t,n){switch(t){case"input":if(Po(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Li(r);if(!i)throw Error(v(90));ra(r),Po(r,i)}}}break;case"textarea":oa(e,n);break;case"select":t=n.value,t!=null&&fn(e,!!n.multiple,t,!1)}};da=ll;pa=Kt;var qp={usingClientEntryPoint:!1,Events:[wr,rn,Li,ca,fa,ll]},Mn={findFiberByHostInstance:Bt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Kp={bundleType:Mn.bundleType,version:Mn.version,rendererPackageName:Mn.rendererPackageName,rendererConfig:Mn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:at.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ya(e),e===null?null:e.stateNode},findFiberByHostInstance:Mn.findFiberByHostInstance||Hp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Mr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Mr.isDisabled&&Mr.supportsFiber)try{Ti=Mr.inject(Kp),Ke=Mr}catch{}}Ce.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=qp;Ce.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!hl(t))throw Error(v(200));return Wp(e,t,null,n)};Ce.createRoot=function(e,t){if(!hl(e))throw Error(v(299));var n=!1,r="",i=Yc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=fl(e,1,!1,null,null,n,!1,r,i),e[st]=t.current,lr(e.nodeType===8?e.parentNode:e),new pl(t)};Ce.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(v(188)):(e=Object.keys(e).join(","),Error(v(268,e)));return e=ya(t),e=e===null?null:e.stateNode,e};Ce.flushSync=function(e){return Kt(e)};Ce.hydrate=function(e,t,n){if(!Ui(t))throw Error(v(200));return $i(null,e,t,!0,n)};Ce.hydrateRoot=function(e,t,n){if(!hl(e))throw Error(v(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=Yc;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Kc(t,null,e,1,n??null,i,!1,o,s),e[st]=t.current,lr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Fi(t)};Ce.render=function(e,t,n){if(!Ui(t))throw Error(v(200));return $i(null,e,t,!1,n)};Ce.unmountComponentAtNode=function(e){if(!Ui(e))throw Error(v(40));return e._reactRootContainer?(Kt(function(){$i(null,null,e,!1,function(){e._reactRootContainer=null,e[st]=null})}),!0):!1};Ce.unstable_batchedUpdates=ll;Ce.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ui(n))throw Error(v(200));if(e==null||e._reactInternals===void 0)throw Error(v(38));return $i(e,t,n,!1,r)};Ce.version="18.3.1-next-f1338f8080-20240426";function Xc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Xc)}catch(e){console.error(e)}}Xc(),Xu.exports=Ce;var Yp=Xu.exports,Gc,Ru=Yp;Gc=Ru.createRoot,Ru.hydrateRoot;const Ge=Object.create(null);Ge.open="0";Ge.close="1";Ge.ping="2";Ge.pong="3";Ge.message="4";Ge.upgrade="5";Ge.noop="6";const Zr=Object.create(null);Object.keys(Ge).forEach(e=>{Zr[Ge[e]]=e});const ms={type:"error",data:"parser error"},Jc=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",Zc=typeof ArrayBuffer=="function",bc=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,ml=({type:e,data:t},n,r)=>Jc&&t instanceof Blob?n?r(t):Pu(t,r):Zc&&(t instanceof ArrayBuffer||bc(t))?n?r(t):Pu(new Blob([t]),r):r(Ge[e]+(t||"")),Pu=(e,t)=>{const n=new FileReader;return n.onload=function(){const r=n.result.split(",")[1];t("b"+(r||""))},n.readAsDataURL(e)};function ju(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let xo;function Xp(e,t){if(Jc&&e.data instanceof Blob)return e.data.arrayBuffer().then(ju).then(t);if(Zc&&(e.data instanceof ArrayBuffer||bc(e.data)))return t(ju(e.data));ml(e,!1,n=>{xo||(xo=new TextEncoder),t(xo.encode(n))})}const Lu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Wn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Lu.length;e++)Wn[Lu.charCodeAt(e)]=e;const Gp=e=>{let t=e.length*.75,n=e.length,r,i=0,o,s,l,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const c=new ArrayBuffer(t),y=new Uint8Array(c);for(r=0;r<n;r+=4)o=Wn[e.charCodeAt(r)],s=Wn[e.charCodeAt(r+1)],l=Wn[e.charCodeAt(r+2)],u=Wn[e.charCodeAt(r+3)],y[i++]=o<<2|s>>4,y[i++]=(s&15)<<4|l>>2,y[i++]=(l&3)<<6|u&63;return c},Jp=typeof ArrayBuffer=="function",yl=(e,t)=>{if(typeof e!="string")return{type:"message",data:ef(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:Zp(e.substring(1),t)}:Zr[n]?e.length>1?{type:Zr[n],data:e.substring(1)}:{type:Zr[n]}:ms},Zp=(e,t)=>{if(Jp){const n=Gp(e);return ef(n,t)}else return{base64:!0,data:e}},ef=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},tf=String.fromCharCode(30),bp=(e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach((o,s)=>{ml(o,!1,l=>{r[s]=l,++i===n&&t(r.join(tf))})})},eh=(e,t)=>{const n=e.split(tf),r=[];for(let i=0;i<n.length;i++){const o=yl(n[i],t);if(r.push(o),o.type==="error")break}return r};function th(){return new TransformStream({transform(e,t){Xp(e,n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const o=new DataView(i.buffer);o.setUint8(0,126),o.setUint16(1,r)}else{i=new Uint8Array(9);const o=new DataView(i.buffer);o.setUint8(0,127),o.setBigUint64(1,BigInt(r))}e.data&&typeof e.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(n)})}})}let ko;function Br(e){return e.reduce((t,n)=>t+n.length,0)}function Fr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function nh(e,t){ko||(ko=new TextDecoder);const n=[];let r=0,i=-1,o=!1;return new TransformStream({transform(s,l){for(n.push(s);;){if(r===0){if(Br(n)<1)break;const u=Fr(n,1);o=(u[0]&128)===128,i=u[0]&127,i<126?r=3:i===126?r=1:r=2}else if(r===1){if(Br(n)<2)break;const u=Fr(n,2);i=new DataView(u.buffer,u.byteOffset,u.length).getUint16(0),r=3}else if(r===2){if(Br(n)<8)break;const u=Fr(n,8),c=new DataView(u.buffer,u.byteOffset,u.length),y=c.getUint32(0);if(y>Math.pow(2,53-32)-1){l.enqueue(ms);break}i=y*Math.pow(2,32)+c.getUint32(4),r=3}else{if(Br(n)<i)break;const u=Fr(n,i);l.enqueue(yl(o?u:ko.decode(u),t)),r=0}if(i===0||i>e){l.enqueue(ms);break}}}})}const nf=4;function b(e){if(e)return rh(e)}function rh(e){for(var t in b.prototype)e[t]=b.prototype[t];return e}b.prototype.on=b.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};b.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};b.prototype.off=b.prototype.removeListener=b.prototype.removeAllListeners=b.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var r,i=0;i<n.length;i++)if(r=n[i],r===t||r.fn===t){n.splice(i,1);break}return n.length===0&&delete this._callbacks["$"+e],this};b.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,i=n.length;r<i;++r)n[r].apply(this,t)}return this};b.prototype.emitReserved=b.prototype.emit;b.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};b.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Vi=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0))(),je=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),ih="arraybuffer";function rf(e,...t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const oh=je.setTimeout,sh=je.clearTimeout;function Wi(e,t){t.useNativeTimers?(e.setTimeoutFn=oh.bind(je),e.clearTimeoutFn=sh.bind(je)):(e.setTimeoutFn=je.setTimeout.bind(je),e.clearTimeoutFn=je.clearTimeout.bind(je))}const lh=1.33;function uh(e){return typeof e=="string"?ah(e):Math.ceil((e.byteLength||e.size)*lh)}function ah(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}function of(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function ch(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function fh(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let o=n[r].split("=");t[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return t}class dh extends Error{constructor(t,n,r){super(t),this.description=n,this.context=r,this.type="TransportError"}}class gl extends b{constructor(t){super(),this.writable=!1,Wi(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,r){return super.emitReserved("error",new dh(t,n,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=yl(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=ch(t);return n.length?"?"+n:""}}class ph extends gl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||n()})),this.writable||(r++,this.once("drain",function(){--r||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};eh(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,bp(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=of()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let sf=!1;try{sf=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const hh=sf;function mh(){}class yh extends ph{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let r=location.port;r||(r=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,n){const r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(i,o)=>{this.onError("xhr post error",i,o)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,r)=>{this.onError("xhr poll error",n,r)}),this.pollXhr=t}}class Xe extends b{constructor(t,n,r){super(),this.createRequest=t,Wi(this,r),this._opts=r,this._method=r.method||"GET",this._uri=n,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const n=rf(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&r.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var i;r.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=Xe.requestsCount++,Xe.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=mh,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Xe.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}Xe.requestsCount=0;Xe.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",Ou);else if(typeof addEventListener=="function"){const e="onpagehide"in je?"pagehide":"unload";addEventListener(e,Ou,!1)}}function Ou(){for(let e in Xe.requests)Xe.requests.hasOwnProperty(e)&&Xe.requests[e].abort()}const gh=function(){const e=lf({xdomain:!1});return e&&e.responseType!==null}();class vh extends yh{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=gh&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new Xe(lf,this.uri(),t)}}function lf(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||hh))return new XMLHttpRequest}catch{}if(!t)try{return new je[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const uf=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class wh extends gl{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,r=uf?{}:rf(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,r)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;ml(r,this.supportsBinary,o=>{try{this.doWrite(r,o)}catch{}i&&Vi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=of()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const So=je.WebSocket||je.MozWebSocket;class xh extends wh{createSocket(t,n,r){return uf?new So(t,n,r):n?new So(t,n):new So(t)}doWrite(t,n){this.ws.send(n)}}class kh extends gl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=nh(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(n).getReader(),i=th();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const o=()=>{r.read().then(({done:l,value:u})=>{l||(this.onPacket(u),o())}).catch(l=>{})};o();const s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;this._writer.write(r).then(()=>{i&&Vi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const Sh={websocket:xh,webtransport:kh,polling:vh},_h=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Eh=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ys(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");n!=-1&&r!=-1&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=_h.exec(e||""),o={},s=14;for(;s--;)o[Eh[s]]=i[s]||"";return n!=-1&&r!=-1&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=Nh(o,o.path),o.queryKey=Ch(o,o.query),o}function Nh(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Ch(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,i,o){i&&(n[i]=o)}),n}const gs=typeof addEventListener=="function"&&typeof removeEventListener=="function",br=[];gs&&addEventListener("offline",()=>{br.forEach(e=>e())},!1);class Rt extends b{constructor(t,n){if(super(),this.binaryType=ih,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const r=ys(t);n.hostname=r.host,n.secure=r.protocol==="https"||r.protocol==="wss",n.port=r.port,r.query&&(n.query=r.query)}else n.host&&(n.hostname=ys(n.host).host);Wi(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(r=>{const i=r.prototype.name;this.transports.push(i),this._transportsByName[i]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=fh(this.opts.query)),gs&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},br.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=nf,n.transport=t,this.id&&(n.sid=this.id);const r=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Rt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",Rt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let r=0;r<this.writeBuffer.length;r++){const i=this.writeBuffer[r].data;if(i&&(n+=uh(i)),r>0&&n>this._maxPayload)return this.writeBuffer.slice(0,r);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Vi(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,r){return this._sendPacket("message",t,n,r),this}send(t,n,r){return this._sendPacket("message",t,n,r),this}_sendPacket(t,n,r,i){if(typeof n=="function"&&(i=n,n=void 0),typeof r=="function"&&(i=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const o={type:t,data:n,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},r=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(Rt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),gs&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=br.indexOf(this._offlineEventListener);r!==-1&&br.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}Rt.protocol=nf;class Th extends Rt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),r=!1;Rt.priorWebsocketSuccess=!1;const i=()=>{r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",m=>{if(!r)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;Rt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(y(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const h=new Error("probe error");h.transport=n.name,this.emitReserved("upgradeError",h)}}))};function o(){r||(r=!0,y(),n.close(),n=null)}const s=m=>{const h=new Error("probe error: "+m);h.transport=n.name,o(),this.emitReserved("upgradeError",h)};function l(){s("transport closed")}function u(){s("socket closed")}function c(m){n&&m.name!==n.name&&o()}const y=()=>{n.removeListener("open",i),n.removeListener("error",s),n.removeListener("close",l),this.off("close",u),this.off("upgrading",c)};n.once("open",i),n.once("error",s),n.once("close",l),this.once("close",u),this.once("upgrading",c),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&n.push(t[r]);return n}}let Rh=class extends Th{constructor(t,n={}){const r=typeof t=="object"?t:n;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(i=>Sh[i]).filter(i=>!!i)),super(t,r)}};function Ph(e,t="",n){let r=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),r=ys(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(n&&n.port===r.port?"":":"+r.port),r}const jh=typeof ArrayBuffer=="function",Lh=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,af=Object.prototype.toString,Oh=typeof Blob=="function"||typeof Blob<"u"&&af.call(Blob)==="[object BlobConstructor]",Ah=typeof File=="function"||typeof File<"u"&&af.call(File)==="[object FileConstructor]";function vl(e){return jh&&(e instanceof ArrayBuffer||Lh(e))||Oh&&e instanceof Blob||Ah&&e instanceof File}function ei(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,r=e.length;n<r;n++)if(ei(e[n]))return!0;return!1}if(vl(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return ei(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&ei(e[n]))return!0;return!1}function zh(e){const t=[],n=e.data,r=e;return r.data=vs(n,t),r.attachments=t.length,{packet:r,buffers:t}}function vs(e,t){if(!e)return e;if(vl(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=vs(e[r],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=vs(e[r],t));return n}return e}function Ih(e,t){return e.data=ws(e.data,t),delete e.attachments,e}function ws(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=ws(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=ws(e[n],t));return e}const Dh=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Mh=5;var M;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(M||(M={}));class Bh{constructor(t){this.replacer=t}encode(t){return(t.type===M.EVENT||t.type===M.ACK)&&ei(t)?this.encodeAsBinary({type:t.type===M.EVENT?M.BINARY_EVENT:M.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===M.BINARY_EVENT||t.type===M.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=zh(t),r=this.encodeAsString(n.packet),i=n.buffers;return i.unshift(r),i}}function Au(e){return Object.prototype.toString.call(e)==="[object Object]"}class wl extends b{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const r=n.type===M.BINARY_EVENT;r||n.type===M.BINARY_ACK?(n.type=r?M.EVENT:M.ACK,this.reconstructor=new Fh(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(vl(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const r={type:Number(t.charAt(0))};if(M[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===M.BINARY_EVENT||r.type===M.BINARY_ACK){const o=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const s=t.substring(o,n);if(s!=Number(s)||t.charAt(n)!=="-")throw new Error("Illegal attachments");r.attachments=Number(s)}if(t.charAt(n+1)==="/"){const o=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););r.nsp=t.substring(o,n)}else r.nsp="/";const i=t.charAt(n+1);if(i!==""&&Number(i)==i){const o=n+1;for(;++n;){const s=t.charAt(n);if(s==null||Number(s)!=s){--n;break}if(n===t.length)break}r.id=Number(t.substring(o,n+1))}if(t.charAt(++n)){const o=this.tryParse(t.substr(n));if(wl.isPayloadValid(r.type,o))r.data=o;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case M.CONNECT:return Au(n);case M.DISCONNECT:return n===void 0;case M.CONNECT_ERROR:return typeof n=="string"||Au(n);case M.EVENT:case M.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&Dh.indexOf(n[0])===-1);case M.ACK:case M.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Fh{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=Ih(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Uh=Object.freeze(Object.defineProperty({__proto__:null,Decoder:wl,Encoder:Bh,get PacketType(){return M},protocol:Mh},Symbol.toStringTag,{value:"Module"}));function Fe(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const $h=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class cf extends b{constructor(t,n,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[Fe(t,"open",this.onopen.bind(this)),Fe(t,"packet",this.onpacket.bind(this)),Fe(t,"error",this.onerror.bind(this)),Fe(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var r,i,o;if($h.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const s={type:M.EVENT,data:n};if(s.options={},s.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const y=this.ids++,m=n.pop();this._registerAckCallback(y,m),s.id=y}const l=(i=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||i===void 0?void 0:i.writable,u=this.connected&&!(!((o=this.io.engine)===null||o===void 0)&&o._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(t,n){var r;const i=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(i===void 0){this.acks[t]=n;return}const o=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===t&&this.sendBuffer.splice(l,1);n.call(this,new Error("operation has timed out"))},i),s=(...l)=>{this.io.clearTimeoutFn(o),n.apply(this,l)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...n){return new Promise((r,i)=>{const o=(s,l)=>s?i(s):r(l);o.withError=!0,n.push(o),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...o)=>r!==this._queue[0]?void 0:(i!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(i)):(this._queue.shift(),n&&n(null,...o)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:M.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case M.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case M.EVENT:case M.BINARY_EVENT:this.onevent(t);break;case M.ACK:case M.BINARY_ACK:this.onack(t);break;case M.DISCONNECT:this.ondisconnect();break;case M.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const r of n)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let r=!1;return function(...i){r||(r=!0,n.packet({type:M.ACK,id:t,data:i}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:M.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const r of n)r.apply(this,t.data)}}}function Tn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Tn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};Tn.prototype.reset=function(){this.attempts=0};Tn.prototype.setMin=function(e){this.ms=e};Tn.prototype.setMax=function(e){this.max=e};Tn.prototype.setJitter=function(e){this.jitter=e};class xs extends b{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,Wi(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((r=n.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new Tn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const i=n.parser||Uh;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Rh(this.uri,this.opts);const n=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const i=Fe(n,"open",function(){r.onopen(),t&&t()}),o=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),t?t(l):this.maybeReconnectOnOpen()},s=Fe(n,"error",o);if(this._timeout!==!1){const l=this._timeout,u=this.setTimeoutFn(()=>{i(),o(new Error("timeout")),n.close()},l);this.opts.autoUnref&&u.unref(),this.subs.push(()=>{this.clearTimeoutFn(u)})}return this.subs.push(i),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(Fe(t,"ping",this.onping.bind(this)),Fe(t,"data",this.ondata.bind(this)),Fe(t,"error",this.onerror.bind(this)),Fe(t,"close",this.onclose.bind(this)),Fe(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){Vi(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new cf(this,t,n),this.nsps[t]=r),r}_destroy(t){const n=Object.keys(this.nsps);for(const r of n)if(this.nsps[r].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let r=0;r<n.length;r++)this.engine.write(n[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},n);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Bn={};function ti(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=Ph(e,t.path||"/socket.io"),r=n.source,i=n.id,o=n.path,s=Bn[i]&&o in Bn[i].nsps,l=t.forceNew||t["force new connection"]||t.multiplex===!1||s;let u;return l?u=new xs(r,t):(Bn[i]||(Bn[i]=new xs(r,t)),u=Bn[i]),n.query&&!t.query&&(t.query=n.queryKey),u.socket(n.path,t)}Object.assign(ti,{Manager:xs,Socket:cf,io:ti,connect:ti});const Vh=({state:e,isConnected:t})=>{const n=()=>{if(!t)return"avatar-disconnected";switch(e){case"listening":return"avatar-listening";case"speaking":return"avatar-speaking";case"thinking":return"avatar-thinking";default:return"avatar-idle"}};return d.jsxs("div",{className:`avatar-container ${n()}`,children:[d.jsx("div",{className:"avatar-circle",children:d.jsxs("div",{className:"avatar-inner",children:[d.jsx("div",{className:"avatar-ring ring-1"}),d.jsx("div",{className:"avatar-ring ring-2"}),d.jsx("div",{className:"avatar-ring ring-3"}),d.jsxs("div",{className:"avatar-icon",children:[e==="listening"&&d.jsx("svg",{className:"w-12 h-12 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z",clipRule:"evenodd"})}),e==="speaking"&&d.jsx("svg",{className:"w-12 h-12 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.814L4.846 13.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h1.846l3.537-3.314a1 1 0 011.617.814zM15 8a2 2 0 012 2v0a2 2 0 01-2 2 1 1 0 01-1-1V9a1 1 0 011-1z",clipRule:"evenodd"})}),e==="thinking"&&d.jsx("svg",{className:"w-12 h-12 text-yellow-400 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})}),e==="idle"&&d.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z",clipRule:"evenodd"})})]})]})}),d.jsx("div",{className:"avatar-status",children:d.jsx("span",{className:"text-sm font-medium text-white/80",children:t?e==="listening"?"Listening...":e==="speaking"?"Speaking...":e==="thinking"?"Thinking...":"Ready":"Disconnected"})})]})},Wh=({messages:e,isLoading:t})=>{const n=I.useRef(null),r=()=>{var o;(o=n.current)==null||o.scrollIntoView({behavior:"smooth"})};I.useEffect(()=>{r()},[e]);const i=o=>new Date(o).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return d.jsx("div",{className:"flex flex-col h-full",children:d.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 min-h-0",children:[e.length===0?d.jsx("div",{className:"flex items-center justify-center h-full",children:d.jsxs("div",{className:"text-center text-white/60",children:[d.jsx("svg",{className:"w-12 h-12 mx-auto mb-4 opacity-50",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z",clipRule:"evenodd"})}),d.jsx("p",{className:"text-sm",children:"Start a conversation"}),d.jsx("p",{className:"text-xs mt-1",children:"Type a message or use voice input"})]})}):e.map(o=>d.jsx("div",{className:`flex ${o.message_type==="user"?"justify-end":"justify-start"}`,children:d.jsxs("div",{className:`max-w-[80%] rounded-2xl px-4 py-3 ${o.message_type==="user"?"bg-blue-600 text-white":o.message_type==="assistant"?"bg-white/10 text-white border border-white/20":"bg-yellow-600/20 text-yellow-200 border border-yellow-600/30"}`,children:[d.jsx("div",{className:"text-sm leading-relaxed",children:o.content}),d.jsxs("div",{className:`text-xs mt-2 opacity-70 ${o.message_type==="user"?"text-blue-100":"text-white/60"}`,children:[i(o.created_at),o.audio_url&&d.jsx("span",{className:"ml-2",children:"🔊"})]})]})},o.id)),t&&d.jsx("div",{className:"flex justify-start",children:d.jsx("div",{className:"bg-white/10 text-white border border-white/20 rounded-2xl px-4 py-3",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsxs("div",{className:"flex space-x-1",children:[d.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce"}),d.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),d.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),d.jsx("span",{className:"text-sm text-white/80",children:"AI is thinking..."})]})})}),d.jsx("div",{ref:n})]})})};var Hh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Qh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),qh=(e,t)=>{const n=I.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,children:l,...u},c)=>I.createElement("svg",{ref:c,...Hh,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:`lucide lucide-${Qh(e)}`,...u},[...t.map(([y,m])=>I.createElement(y,m)),...(Array.isArray(l)?l:[l])||[]]));return n.displayName=`${e}`,n};var ct=qh;const zu=ct("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),Kh=ct("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),Yh=ct("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),Xh=ct("Move",[["polyline",{points:"5 9 2 12 5 15",key:"1r5uj5"}],["polyline",{points:"9 5 12 2 15 5",key:"5v383o"}],["polyline",{points:"15 19 12 22 9 19",key:"g7qi8m"}],["polyline",{points:"19 9 22 12 19 15",key:"tpp73q"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}]]),Gh=ct("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),Iu=ct("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ff=ct("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),df=ct("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),Du=ct("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Jh=({isRecording:e,isPlaying:t,isMuted:n,isProcessing:r=!1,systemActive:i=!0,onToggleMute:o})=>d.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[d.jsxs("div",{className:"flex items-center justify-center space-x-6",children:[d.jsxs("div",{className:"flex flex-col items-center",children:[d.jsx("div",{className:`w-8 h-8 rounded-full mb-2 flex items-center justify-center ${r?"bg-blue-400":e?"bg-green-400 animate-pulse":t?"bg-purple-400 animate-pulse":"bg-gray-400"}`,children:r&&d.jsx(zu,{className:"w-4 h-4 text-white animate-spin"})}),d.jsx("span",{className:"text-sm text-white/90 font-medium text-center",children:r?"Thinking...":e?"Listening...":t?"Speaking...":"Ready"})]}),d.jsxs("div",{className:"flex flex-col items-center",children:[d.jsx("button",{onClick:o,className:`p-4 rounded-full transition-all duration-300 ${n?"bg-gray-600 hover:bg-gray-700":"bg-purple-500 hover:bg-purple-600 shadow-lg shadow-purple-500/30"}`,children:n?d.jsx(df,{className:"w-6 h-6 text-white"}):d.jsx(ff,{className:"w-6 h-6 text-white"})}),d.jsx("span",{className:"text-xs text-white/70 font-medium mt-2",children:n?"Unmute":"Mute"})]})]}),i?r?d.jsx("div",{className:"bg-black/20 rounded-lg px-4 py-2 backdrop-blur-sm",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(zu,{className:"w-4 h-4 text-blue-400 animate-spin"}),d.jsx("span",{className:"text-sm text-white/90",children:"Thinking..."})]})}):null:d.jsx("div",{className:"bg-red-500/20 border border-red-500/30 rounded-lg px-4 py-2 backdrop-blur-sm",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("div",{className:"w-2 h-2 bg-red-400 rounded-full"}),d.jsx("span",{className:"text-sm text-red-400 font-medium",children:"System Paused"})]})}),d.jsx("div",{className:"text-center",children:d.jsx("p",{className:"text-sm text-white/70 max-w-md",children:i?"Voice-to-voice conversation active. Say your wake word to start talking!":"System is paused - use Control Centre to resume"})})]}),Zh=({isExpanded:e,onToggleExpanded:t,isMuted:n,onToggleMute:r,onRefreshChat:i,chatExpanded:o,onToggleChatExpanded:s,userName:l,onUserNameChange:u,systemActive:c,onToggleSystemActive:y,wakeWords:m=[],onAddWakeWord:h,onDeleteWakeWord:w,onToggleWakeWord:x})=>{const[k,U]=I.useState({x:20,y:20}),[f,a]=I.useState(!1),[p,g]=I.useState({x:0,y:0}),[_,C]=I.useState(""),N=I.useRef(null),T=O=>{if(!N.current)return;const j=N.current.getBoundingClientRect();g({x:O.clientX-j.left,y:O.clientY-j.top}),a(!0)};return I.useEffect(()=>{const O=ee=>{if(!f)return;const Je=ee.clientX-p.x,Ze=ee.clientY-p.y,zt=window.innerWidth-400,Rn=window.innerHeight-600;U({x:Math.max(0,Math.min(Je,zt)),y:Math.max(0,Math.min(Ze,Rn))})},j=()=>{a(!1)};return f&&(document.addEventListener("mousemove",O),document.addEventListener("mouseup",j)),()=>{document.removeEventListener("mousemove",O),document.removeEventListener("mouseup",j)}},[f,p]),e?d.jsxs("div",{ref:N,className:"fixed z-50 bg-black/80 backdrop-blur-md border border-white/20 rounded-xl w-96 max-h-[80vh] overflow-y-auto",style:{left:k.x,top:k.y},children:[d.jsxs("div",{className:"flex items-center justify-between p-6 pb-4 cursor-move",onMouseDown:T,children:[d.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center space-x-2",children:[d.jsx(Xh,{className:"w-4 h-4 text-white/60"}),d.jsx(Iu,{className:"w-5 h-5"}),d.jsx("span",{children:"Control Centre"})]}),d.jsx("button",{onClick:t,className:"text-white/60 hover:text-white transition-colors",children:d.jsx(Du,{className:"w-5 h-5"})})]}),d.jsxs("div",{className:"px-6 pb-6",children:[d.jsxs("div",{className:"mb-6",children:[d.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"User Preferences"}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{children:[d.jsx("label",{className:"text-xs text-white/60 mb-1 block",children:"What should I call you?"}),d.jsx("input",{type:"text",value:l,onChange:O=>u(O.target.value),placeholder:"Enter your preferred name...",className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"text-xs text-white/60 mb-2 block",children:"Voice System: OpenAI Realtime API"}),d.jsx("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4",children:d.jsx("p",{className:"text-blue-300 text-sm",children:"✅ No wake words needed! Just start talking and the AI will respond in real-time."})})]}),d.jsxs("div",{className:"hidden",children:[d.jsx("label",{className:"text-xs text-white/60 mb-2 block",children:"Wake Word Management (Disabled)"}),d.jsxs("div",{className:"flex space-x-2 mb-3",children:[d.jsx("input",{type:"text",value:_,onChange:O=>C(O.target.value),placeholder:"Enter new wake word...",className:"flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400",onKeyPress:O=>{O.key==="Enter"&&_.trim()&&h&&(h(_.trim()),C(""))}}),d.jsx("button",{onClick:()=>{_.trim()&&h&&(h(_.trim()),C(""))},disabled:!_.trim(),className:"px-3 py-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-sm",children:"Add"})]}),d.jsxs("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:[m.map(O=>d.jsxs("div",{className:"flex items-center justify-between p-2 bg-white/5 rounded-lg",children:[d.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[d.jsx("button",{onClick:()=>x==null?void 0:x(O.id),className:`w-4 h-4 rounded border-2 flex items-center justify-center ${O.enabled?"bg-green-500 border-green-500":"border-white/30"}`,children:O.enabled&&d.jsx("span",{className:"text-white text-xs",children:"✓"})}),d.jsx("span",{className:`text-sm ${O.enabled?"text-white":"text-white/60"}`,children:O.keyword})]}),d.jsx("button",{onClick:()=>w==null?void 0:w(O.id),className:"text-red-400 hover:text-red-300 p-1",children:d.jsx(Du,{className:"w-3 h-3"})})]},O.id)),m.length===0&&d.jsx("div",{className:"text-center text-white/60 text-sm py-2",children:"No wake words configured"})]})]})]})]}),d.jsxs("div",{className:"mb-6",children:[d.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"System Control"}),d.jsxs("button",{onClick:y,className:`w-full flex items-center justify-center space-x-2 p-4 rounded-lg transition-all font-medium ${c?"bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30":"bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30"}`,children:[d.jsx("div",{className:`w-3 h-3 rounded-full ${c?"bg-green-400 animate-pulse":"bg-red-400"}`}),d.jsx("span",{children:c?"System Active - Click to Pause":"System Paused - Click to Resume"})]}),d.jsx("p",{className:"text-xs text-white/60 mt-2 text-center",children:c?"AI is listening and ready to respond":"AI is paused - no processing or responses"})]}),d.jsxs("div",{className:"mb-6",children:[d.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"Chat Controls"}),d.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[d.jsxs("button",{onClick:r,className:`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all ${n?"bg-red-500/20 border border-red-500/30 text-red-400":"bg-purple-500/20 border border-purple-500/30 text-purple-400"}`,children:[n?d.jsx(df,{className:"w-4 h-4"}):d.jsx(ff,{className:"w-4 h-4"}),d.jsx("span",{className:"text-sm",children:n?"Unmute":"Mute"})]}),d.jsxs("button",{onClick:i,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-blue-500/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 transition-all",children:[d.jsx(Gh,{className:"w-4 h-4"}),d.jsx("span",{className:"text-sm",children:"Refresh"})]}),d.jsxs("button",{onClick:s,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 transition-all col-span-2",children:[o?d.jsx(Yh,{className:"w-4 h-4"}):d.jsx(Kh,{className:"w-4 h-4"}),d.jsx("span",{className:"text-sm",children:o?"Normal Size":"Expand Chat"})]})]})]}),d.jsxs("div",{className:"text-xs text-white/50 leading-relaxed",children:[d.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3",children:[d.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[d.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),d.jsx("span",{className:"text-blue-300 font-medium text-xs",children:"OpenWakeWord System"})]}),d.jsx("p",{className:"text-xs text-white/70",children:"Now using advanced AI-powered wake word detection with no API keys required."})]}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("p",{children:[d.jsx("strong",{className:"text-white/70",children:"Wake Words:"})," Say any enabled wake word to start voice recording."]}),d.jsxs("p",{children:[d.jsx("strong",{className:"text-white/70",children:"Auto-Stop:"})," Recording stops automatically after 1.5 seconds of silence."]}),d.jsxs("p",{children:[d.jsx("strong",{className:"text-white/70",children:"Sensitivity:"})," Higher values detect wake words more easily but may increase false positives."]}),d.jsxs("p",{children:[d.jsx("strong",{className:"text-white/70",children:"Available Models:"}),' "hey jarvis", "alexa", "hey mycroft", "hey rhasspy"']})]})]})]})]}):d.jsx("div",{className:"fixed z-50 cursor-move",style:{left:k.x,top:k.y},onMouseDown:T,children:d.jsx("button",{onClick:t,className:"bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 text-white/80 hover:bg-black/30 transition-all duration-300",children:d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx(Iu,{className:"w-4 h-4"}),d.jsx("span",{className:"text-sm",children:"Control Centre"})]})})})};class Mu{constructor(t){qi(this,"config");qi(this,"sessionPath");this.config=t,this.sessionPath=`projects/${t.projectId}/agent/sessions/${t.sessionId}`}async detectIntent(t){var n,r;try{const i=await fetch("/api/dialogflow/detect-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionPath:this.sessionPath,queryInput:{text:{text:t,languageCode:this.config.languageCode}}})});if(!i.ok)throw new Error(`DialogFlow API error: ${i.statusText}`);const o=await i.json();return{queryText:o.queryResult.queryText,fulfillmentText:o.queryResult.fulfillmentText,intent:{name:((n=o.queryResult.intent)==null?void 0:n.name)||"",displayName:((r=o.queryResult.intent)==null?void 0:r.displayName)||"",confidence:o.queryResult.intentDetectionConfidence||0},parameters:o.queryResult.parameters||{},contexts:o.queryResult.outputContexts||[],action:o.queryResult.action||""}}catch(i){return console.error("DialogFlow detection error:",i),null}}async canHandleQuickly(t){try{const n=await this.detectIntent(t);return n?n.intent.confidence>.8&&n.fulfillmentText.length>0&&this.isQuickResponseIntent(n.intent.displayName):!1}catch(n){return console.error("DialogFlow quick check error:",n),!1}}isQuickResponseIntent(t){return["Default Welcome Intent","Default Fallback Intent","greeting","goodbye","thanks","help","what_can_you_do","how_are_you","tell_joke","weather","time","date","small_talk","encouragement","support","compliment","motivation"].some(r=>t.toLowerCase().includes(r.toLowerCase()))}async getContextualResponse(t,n=[]){try{const r=this.addConversationContext(t,n);return await this.detectIntent(r)}catch(r){return console.error("DialogFlow contextual response error:",r),null}}addConversationContext(t,n){return n.length===0?t:`Context: ${n.slice(-3).join(" ")}. Current query: ${t}`}static generateSessionId(){return`session-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}updateSession(t){console.log("Updated session contexts:",t)}needsAIEnhancement(t){return t.intent.confidence<.7||t.fulfillmentText.length<10||t.action==="input.unknown"||t.intent.displayName.includes("Fallback")}getSuggestedFollowUps(t){const n={greeting:["How are you feeling today?","What would you like to talk about?","Is there anything I can help you with?"],support:["Would you like to talk more about that?","How can I best support you right now?","What would make you feel better?"],help:["What specific area would you like help with?","Can you tell me more about what you need?","Would you like some suggestions?"],motivation:["What goals are you working towards?","What challenges are you facing?","How can I encourage you today?"]},r=Object.keys(n).find(i=>t.intent.displayName.toLowerCase().includes(i));return r?n[r]:["Is there anything else I can help with?","How are you feeling about that?","Would you like to explore this topic more?"]}}const bh=()=>{const[e,t]=I.useState(null),[n,r]=I.useState(!1),[i,o]=I.useState(null),[s,l]=I.useState([]),[u,c]=I.useState(!1),[y,m]=I.useState(!1),[h,w]=I.useState(!1),[x,k]=I.useState(!1),[U,f]=I.useState("idle"),[a,p]=I.useState(!1),[g,_]=I.useState(""),[C,N]=I.useState("hey assistant"),[T,O]=I.useState(!0),[j,ee]=I.useState([]),[Je,Ze]=I.useState(!1),[zt,Rn]=I.useState(!1),[It,Pn]=I.useState(!1),S=I.useRef(null),L=I.useRef(null);I.useEffect(()=>{const R=ti("http://localhost:5002",{transports:["websocket"]});return R.on("connect",()=>{console.log("🔗 Connected to server"),r(!0),console.log("🎤 Starting OpenAI Realtime conversation..."),R.emit("start_realtime_conversation",{conversation_id:null})}),R.on("disconnect",()=>{console.log("🔌 Disconnected from server"),r(!1)}),R.on("realtime_connected",P=>{console.log("✅ OpenAI Realtime API connected:",P),r(!0)}),R.on("ai_greeting",P=>{console.log("🎤 AI Greeting:",P.message);const z={id:Date.now(),content:P.message,message_type:"assistant",created_at:new Date().toISOString()};l(ye=>[...ye,z]),f("speaking")}),R.on("transcript_received",P=>{console.log("📝 Transcript received:",P.transcript);const z={id:P.message_id,content:P.transcript,message_type:"user",created_at:new Date().toISOString()};l(ye=>[...ye,z])}),R.on("response_complete",()=>{console.log("✅ Response completed"),k(!1),f("idle")}),R.on("message_received",P=>{l(z=>[...z,P.message]),P.type==="assistant"?f("speaking"):P.type}),R.on("voice_response",P=>{if(k(!1),!h&&S.current){const z=new Blob([Uint8Array.from(atob(P.audio_data),Hi=>Hi.charCodeAt(0))],{type:"audio/mpeg"}),ye=URL.createObjectURL(z);S.current.src=ye,S.current.play(),m(!0)}}),R.on("realtime_connected",P=>{console.log("✅ OpenAI Realtime API connected:",P),r(!0)}),R.on("ai_greeting",P=>{console.log("🎤 AI Greeting:",P.message);const z={id:Date.now(),content:P.message,message_type:"assistant",created_at:new Date().toISOString()};l(ye=>[...ye,z]),f("speaking")}),R.on("transcript_received",P=>{console.log("📝 Transcript received:",P.transcript);const z={id:P.message_id,content:P.transcript,message_type:"user",created_at:new Date().toISOString()};l(ye=>[...ye,z])}),R.on("response_complete",()=>{console.log("✅ Response completed"),k(!1),f("idle")}),R.on("voice_transcribed",P=>{console.log("🎯 Voice transcribed:",P.text)}),R.on("command_executed",P=>{console.log("Command executed:",P)}),R.on("error",P=>{console.error("Socket error:",P)}),t(R),()=>{R.close()}},[]),I.useEffect(()=>{e&&n&&A()},[e,n]),I.useEffect(()=>{!e||!n||(console.log("🔧 OpenAI Realtime API is active - no wake words needed!"),Ze(!1),ee([]),console.log("✅ OpenAI Realtime API ready - just start talking!"))},[e,n]),I.useEffect(()=>{(()=>{try{const P={}.REACT_APP_GOOGLE_CLOUD_PROJECT_ID;if(P){const z=new Mu({projectId:P,sessionId:Mu.generateSessionId(),languageCode:"en-US"});L.current=z,console.log("DialogFlow service initialized")}else console.log("DialogFlow not configured - missing project ID")}catch(P){console.log("DialogFlow initialization failed:",P)}})()},[]);const A=async()=>{if(!i)try{const P=await(await fetch("http://localhost:5002/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:"AI Voice Chat",user_id:1})})).json();P.success&&(o(P.conversation),e==null||e.emit("join_conversation",{conversation_id:P.conversation.id,user_id:1}))}catch(R){console.error("Error creating conversation:",R)}},Q=async(R,P=!1)=>{if(!e||!i||!R.trim())return;if(!T){console.log("System is paused - message sending disabled");return}p(!0),f("thinking");let z=null;if(L.current)try{if(await L.current.canHandleQuickly(R)&&(z=await L.current.detectIntent(R),z&&z.fulfillmentText)){const Hi={id:Date.now(),content:R,message_type:"user",created_at:new Date().toISOString()};l(Qi=>[...Qi,Hi]);const xf={id:Date.now()+1,content:z.fulfillmentText,message_type:"assistant",created_at:new Date().toISOString()};l(Qi=>[...Qi,xf]),P&&!h&&e.emit("generate_voice_only",{text:z.fulfillmentText,conversation_id:i.id}),p(!1),f("idle");return}}catch(ye){console.log("DialogFlow not available, falling back to AI:",ye)}e.emit("send_message",{conversation_id:i.id,content:R,message_type:"user",generate_voice:P,dialogflow_context:z?{intent:z.intent,parameters:z.parameters,contexts:z.contexts}:null}),p(!1)},J=()=>{u&&(console.log("🛑 Stopping voice streaming..."),e&&i&&e.emit("stop_voice_streaming",{conversation_id:i.id}),c(!1),f("idle"),console.log("✅ Voice streaming stopped"))},ft=R=>{!e||!i||Q(R,!0)},be=()=>{m(!1),f("idle"),T&&Je&&console.log("🔄 Voice response finished, ready for next wake word...")},Gt=()=>{const R=!h;w(R),R&&y&&S.current?(S.current.pause(),S.current.currentTime=0,m(!1),f("idle"),console.log("🔇 Assistant muted and stopped speaking")):R||console.log("🔊 Assistant unmuted")},Ie=R=>{localStorage.setItem("wakeWords",JSON.stringify(R)),console.log("Wake words saved to localStorage:",R)},dt=async R=>{if(e)try{e.emit("stop_wake_word_detection");const P=R.filter(z=>z.enabled);if(P.length>0){const z=P.map(ye=>ye.keyword);e.emit("start_wake_word_detection",{wake_words:z,threshold:.3}),console.log("Wake word service restarted with:",z)}}catch(P){console.error("Failed to restart wake word service:",P)}},pf=async R=>{const P={id:`wake-word-${Date.now()}`,keyword:R.toLowerCase(),enabled:!1,sensitivity:.5},z=[...j,P];ee(z),Ie(z),await dt(z),console.log("Added wake word:",R)},hf=async R=>{const P=j.filter(z=>z.id!==R);ee(P),Ie(P),await dt(P),console.log("Deleted wake word:",R)},mf=async R=>{const P=j.map(z=>z.id===R?{...z,enabled:!z.enabled}:z);ee(P),Ie(P),await dt(P),console.log("Toggled wake word:",R)},yf=()=>{l([]),o(null),A()},gf=R=>{_(R),localStorage.setItem("userName",R)},vf=R=>{N(R),localStorage.setItem("preferredWakeWord",R)},wf=()=>{const R=!T;O(R),R?console.log("✅ System reactivated - AI ready to respond"):(console.log("⏸️ System paused - stopping all AI operations"),u&&J(),y&&S.current&&(S.current.pause(),S.current.currentTime=0,m(!1),f("idle")),k(!1))};return I.useEffect(()=>{const R=localStorage.getItem("userName"),P=localStorage.getItem("preferredWakeWord");R&&_(R),P&&N(P)},[]),d.jsxs("div",{className:"min-h-screen gradient-bg flex p-6 gap-6",children:[d.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center",children:[d.jsx("div",{className:"floating-panel w-full max-w-2xl mb-8",children:d.jsxs("div",{className:"glass-card",children:[d.jsxs("div",{className:"text-center mb-6",children:[d.jsx("h1",{className:"text-2xl font-bold text-white/90 mb-2",children:"AI Assistant"}),d.jsx("p",{className:"text-white/70 text-sm",children:"Click the microphone to start talking"})]}),d.jsx("div",{className:"flex items-center justify-center h-80 avatar-glow",children:d.jsx(Vh,{state:U,isConnected:n,isPlaying:y})})]})}),d.jsxs("div",{className:"glass-panel rounded-3xl p-6 mb-6",children:[d.jsx(Jh,{isRecording:u,isPlaying:y,isMuted:h,onToggleMute:Gt,isProcessing:x,systemActive:T}),n&&d.jsx("div",{className:"mt-4 text-center",children:d.jsxs("div",{className:"inline-flex items-center space-x-2 bg-blue-500/20 border border-blue-500/30 rounded-lg px-3 py-2",children:[d.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),d.jsx("span",{className:"text-blue-400 text-sm font-medium",children:"OpenAI Realtime API active - just start talking!"})]})})]}),d.jsx("div",{className:`connection-indicator ${n?"status-connected":"status-disconnected"}`,children:d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx("div",{className:`w-2 h-2 rounded-full ${n?"bg-green-400 animate-pulse":"bg-red-400"}`}),d.jsx("span",{className:"text-sm font-medium",children:n?"Connected":"Reconnecting..."})]})})]}),d.jsx("div",{className:`flex flex-col h-screen transition-all duration-300 ${It?"w-[600px]":"w-96"}`,children:d.jsxs("div",{className:"glass-panel rounded-3xl flex flex-col h-full max-h-screen overflow-hidden",children:[d.jsxs("div",{className:"p-6 border-b border-white/10 flex-shrink-0",children:[d.jsx("h2",{className:"text-xl font-semibold text-white/90",children:"Our Conversation"}),d.jsx("p",{className:"text-white/60 text-sm mt-1",children:"I'm here to listen and support you"})]}),d.jsx("div",{className:"flex-1 overflow-hidden min-h-0",children:d.jsx(Wh,{messages:s,isLoading:a})}),d.jsxs("div",{className:"p-6 border-t border-white/10 flex-shrink-0",children:[d.jsxs("div",{className:"text-center mb-4",children:[d.jsx("p",{className:"text-white/70 text-sm",children:"🎤 Voice-only conversation mode"}),d.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Use your wake word to start talking"})]}),d.jsxs("div",{className:"flex flex-wrap gap-2",children:[d.jsx("button",{onClick:()=>ft("I need some encouragement today"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Need Encouragement"}),d.jsx("button",{onClick:()=>ft("I'm feeling stressed and need support"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Feeling Stressed"}),d.jsx("button",{onClick:()=>ft("Tell me something positive"),disabled:a,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Something Positive"})]})]})]})}),d.jsx(Zh,{isExpanded:zt,onToggleExpanded:()=>Rn(!zt),isMuted:h,onToggleMute:Gt,onRefreshChat:yf,chatExpanded:It,onToggleChatExpanded:()=>Pn(!It),userName:g,onUserNameChange:gf,preferredWakeWord:C,onWakeWordChange:vf,systemActive:T,onToggleSystemActive:wf,wakeWords:j,onAddWakeWord:pf,onDeleteWakeWord:hf,onToggleWakeWord:mf}),d.jsx("audio",{ref:S,onEnded:be,style:{display:"none"}})]})};Gc(document.getElementById("root")).render(d.jsx(I.StrictMode,{children:d.jsx(bh,{})}));
