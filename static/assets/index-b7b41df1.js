var wf=Object.defineProperty;var xf=(e,t,n)=>t in e?wf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ki=(e,t,n)=>(xf(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var Uu={exports:{}},Pi={},$u={exports:{}},D={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vr=Symbol.for("react.element"),kf=Symbol.for("react.portal"),_f=Symbol.for("react.fragment"),Sf=Symbol.for("react.strict_mode"),Ef=Symbol.for("react.profiler"),Cf=Symbol.for("react.provider"),Nf=Symbol.for("react.context"),Tf=Symbol.for("react.forward_ref"),Rf=Symbol.for("react.suspense"),Pf=Symbol.for("react.memo"),jf=Symbol.for("react.lazy"),Sl=Symbol.iterator;function Lf(e){return e===null||typeof e!="object"?null:(e=Sl&&e[Sl]||e["@@iterator"],typeof e=="function"?e:null)}var Vu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Wu=Object.assign,Hu={};function Nn(e,t,n){this.props=e,this.context=t,this.refs=Hu,this.updater=n||Vu}Nn.prototype.isReactComponent={};Nn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Qu(){}Qu.prototype=Nn.prototype;function _s(e,t,n){this.props=e,this.context=t,this.refs=Hu,this.updater=n||Vu}var Ss=_s.prototype=new Qu;Ss.constructor=_s;Wu(Ss,Nn.prototype);Ss.isPureReactComponent=!0;var El=Array.isArray,qu=Object.prototype.hasOwnProperty,Es={current:null},Ku={key:!0,ref:!0,__self:!0,__source:!0};function Yu(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)qu.call(t,r)&&!Ku.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:vr,type:e,key:o,ref:s,props:i,_owner:Es.current}}function Of(e,t){return{$$typeof:vr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Cs(e){return typeof e=="object"&&e!==null&&e.$$typeof===vr}function Af(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cl=/\/+/g;function Yi(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Af(""+e.key):t.toString(36)}function Wr(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case vr:case kf:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Yi(s,0):r,El(i)?(n="",e!=null&&(n=e.replace(Cl,"$&/")+"/"),Wr(i,t,n,"",function(c){return c})):i!=null&&(Cs(i)&&(i=Of(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Cl,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",El(e))for(var l=0;l<e.length;l++){o=e[l];var u=r+Yi(o,l);s+=Wr(o,t,n,u,i)}else if(u=Lf(e),typeof u=="function")for(e=u.call(e),l=0;!(o=e.next()).done;)o=o.value,u=r+Yi(o,l++),s+=Wr(o,t,n,u,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Er(e,t,n){if(e==null)return e;var r=[],i=0;return Wr(e,r,"","",function(o){return t.call(n,o,i++)}),r}function If(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var me={current:null},Hr={transition:null},zf={ReactCurrentDispatcher:me,ReactCurrentBatchConfig:Hr,ReactCurrentOwner:Es};function Xu(){throw Error("act(...) is not supported in production builds of React.")}D.Children={map:Er,forEach:function(e,t,n){Er(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Er(e,function(){t++}),t},toArray:function(e){return Er(e,function(t){return t})||[]},only:function(e){if(!Cs(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};D.Component=Nn;D.Fragment=_f;D.Profiler=Ef;D.PureComponent=_s;D.StrictMode=Sf;D.Suspense=Rf;D.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=zf;D.act=Xu;D.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Wu({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Es.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)qu.call(t,u)&&!Ku.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&l!==void 0?l[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:vr,type:e.type,key:i,ref:o,props:r,_owner:s}};D.createContext=function(e){return e={$$typeof:Nf,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Cf,_context:e},e.Consumer=e};D.createElement=Yu;D.createFactory=function(e){var t=Yu.bind(null,e);return t.type=e,t};D.createRef=function(){return{current:null}};D.forwardRef=function(e){return{$$typeof:Tf,render:e}};D.isValidElement=Cs;D.lazy=function(e){return{$$typeof:jf,_payload:{_status:-1,_result:e},_init:If}};D.memo=function(e,t){return{$$typeof:Pf,type:e,compare:t===void 0?null:t}};D.startTransition=function(e){var t=Hr.transition;Hr.transition={};try{e()}finally{Hr.transition=t}};D.unstable_act=Xu;D.useCallback=function(e,t){return me.current.useCallback(e,t)};D.useContext=function(e){return me.current.useContext(e)};D.useDebugValue=function(){};D.useDeferredValue=function(e){return me.current.useDeferredValue(e)};D.useEffect=function(e,t){return me.current.useEffect(e,t)};D.useId=function(){return me.current.useId()};D.useImperativeHandle=function(e,t,n){return me.current.useImperativeHandle(e,t,n)};D.useInsertionEffect=function(e,t){return me.current.useInsertionEffect(e,t)};D.useLayoutEffect=function(e,t){return me.current.useLayoutEffect(e,t)};D.useMemo=function(e,t){return me.current.useMemo(e,t)};D.useReducer=function(e,t,n){return me.current.useReducer(e,t,n)};D.useRef=function(e){return me.current.useRef(e)};D.useState=function(e){return me.current.useState(e)};D.useSyncExternalStore=function(e,t,n){return me.current.useSyncExternalStore(e,t,n)};D.useTransition=function(){return me.current.useTransition()};D.version="18.3.1";$u.exports=D;var z=$u.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Df=z,Mf=Symbol.for("react.element"),Bf=Symbol.for("react.fragment"),Ff=Object.prototype.hasOwnProperty,Uf=Df.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,$f={key:!0,ref:!0,__self:!0,__source:!0};function Gu(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)Ff.call(t,r)&&!$f.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Mf,type:e,key:o,ref:s,props:i,_owner:Uf.current}}Pi.Fragment=Bf;Pi.jsx=Gu;Pi.jsxs=Gu;Uu.exports=Pi;var p=Uu.exports,Ju={exports:{}},Te={},Zu={exports:{}},bu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,O){var I=E.length;E.push(O);e:for(;0<I;){var Q=I-1>>>1,X=E[Q];if(0<i(X,O))E[Q]=O,E[I]=X,I=Q;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var O=E[0],I=E.pop();if(I!==O){E[0]=I;e:for(var Q=0,X=E.length,Jt=X>>>1;Q<Jt;){var qe=2*(Q+1)-1,zt=E[qe],De=qe+1,Zt=E[De];if(0>i(zt,I))De<X&&0>i(Zt,zt)?(E[Q]=Zt,E[De]=I,Q=De):(E[Q]=zt,E[qe]=I,Q=qe);else if(De<X&&0>i(Zt,I))E[Q]=Zt,E[De]=I,Q=De;else break e}}return O}function i(E,O){var I=E.sortIndex-O.sortIndex;return I!==0?I:E.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var u=[],c=[],y=1,m=null,h=3,x=!1,k=!1,w=!1,F=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,a=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function d(E){for(var O=n(c);O!==null;){if(O.callback===null)r(c);else if(O.startTime<=E)r(c),O.sortIndex=O.expirationTime,t(u,O);else break;O=n(c)}}function g(E){if(w=!1,d(E),!k)if(n(u)!==null)k=!0,ge(_);else{var O=n(c);O!==null&&dt(g,O.startTime-E)}}function _(E,O){k=!1,w&&(w=!1,f(P),P=-1),x=!0;var I=h;try{for(d(O),m=n(u);m!==null&&(!(m.expirationTime>O)||E&&!ue());){var Q=m.callback;if(typeof Q=="function"){m.callback=null,h=m.priorityLevel;var X=Q(m.expirationTime<=O);O=e.unstable_now(),typeof X=="function"?m.callback=X:m===n(u)&&r(u),d(O)}else r(u);m=n(u)}if(m!==null)var Jt=!0;else{var qe=n(c);qe!==null&&dt(g,qe.startTime-O),Jt=!1}return Jt}finally{m=null,h=I,x=!1}}var S=!1,T=null,P=-1,j=5,L=-1;function ue(){return!(e.unstable_now()-L<j)}function Qe(){if(T!==null){var E=e.unstable_now();L=E;var O=!0;try{O=T(!0,E)}finally{O?et():(S=!1,T=null)}}else S=!1}var et;if(typeof a=="function")et=function(){a(Qe)};else if(typeof MessageChannel<"u"){var ft=new MessageChannel,jn=ft.port2;ft.port1.onmessage=Qe,et=function(){jn.postMessage(null)}}else et=function(){F(Qe,0)};function ge(E){T=E,S||(S=!0,et())}function dt(E,O){P=F(function(){E(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){k||x||(k=!0,ge(_))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(E){switch(h){case 1:case 2:case 3:var O=3;break;default:O=h}var I=h;h=O;try{return E()}finally{h=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,O){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var I=h;h=E;try{return O()}finally{h=I}},e.unstable_scheduleCallback=function(E,O,I){var Q=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?Q+I:Q):I=Q,E){case 1:var X=-1;break;case 2:X=250;break;case 5:X=**********;break;case 4:X=1e4;break;default:X=5e3}return X=I+X,E={id:y++,callback:O,priorityLevel:E,startTime:I,expirationTime:X,sortIndex:-1},I>Q?(E.sortIndex=I,t(c,E),n(u)===null&&E===n(c)&&(w?(f(P),P=-1):w=!0,dt(g,I-Q))):(E.sortIndex=X,t(u,E),k||x||(k=!0,ge(_))),E},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(E){var O=h;return function(){var I=h;h=O;try{return E.apply(this,arguments)}finally{h=I}}}})(bu);Zu.exports=bu;var Vf=Zu.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Wf=z,Ne=Vf;function v(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ea=new Set,tr={};function Xt(e,t){wn(e,t),wn(e+"Capture",t)}function wn(e,t){for(tr[e]=t,e=0;e<t.length;e++)ea.add(t[e])}var st=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eo=Object.prototype.hasOwnProperty,Hf=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Nl={},Tl={};function Qf(e){return Eo.call(Tl,e)?!0:Eo.call(Nl,e)?!1:Hf.test(e)?Tl[e]=!0:(Nl[e]=!0,!1)}function qf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Kf(e,t,n,r){if(t===null||typeof t>"u"||qf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ye(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){le[e]=new ye(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];le[t]=new ye(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){le[e]=new ye(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){le[e]=new ye(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){le[e]=new ye(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){le[e]=new ye(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){le[e]=new ye(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){le[e]=new ye(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){le[e]=new ye(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ns=/[\-:]([a-z])/g;function Ts(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ns,Ts);le[t]=new ye(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ns,Ts);le[t]=new ye(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ns,Ts);le[t]=new ye(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){le[e]=new ye(e,1,!1,e.toLowerCase(),null,!1,!1)});le.xlinkHref=new ye("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){le[e]=new ye(e,1,!1,e.toLowerCase(),null,!0,!0)});function Rs(e,t,n,r){var i=le.hasOwnProperty(t)?le[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Kf(t,n,i,r)&&(n=null),r||i===null?Qf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var ct=Wf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Cr=Symbol.for("react.element"),en=Symbol.for("react.portal"),tn=Symbol.for("react.fragment"),Ps=Symbol.for("react.strict_mode"),Co=Symbol.for("react.profiler"),ta=Symbol.for("react.provider"),na=Symbol.for("react.context"),js=Symbol.for("react.forward_ref"),No=Symbol.for("react.suspense"),To=Symbol.for("react.suspense_list"),Ls=Symbol.for("react.memo"),ht=Symbol.for("react.lazy"),ra=Symbol.for("react.offscreen"),Rl=Symbol.iterator;function On(e){return e===null||typeof e!="object"?null:(e=Rl&&e[Rl]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,Xi;function $n(e){if(Xi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xi=t&&t[1]||""}return`
`+Xi+e}var Gi=!1;function Ji(e,t){if(!e||Gi)return"";Gi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var u=`
`+i[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=l);break}}}finally{Gi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?$n(e):""}function Yf(e){switch(e.tag){case 5:return $n(e.type);case 16:return $n("Lazy");case 13:return $n("Suspense");case 19:return $n("SuspenseList");case 0:case 2:case 15:return e=Ji(e.type,!1),e;case 11:return e=Ji(e.type.render,!1),e;case 1:return e=Ji(e.type,!0),e;default:return""}}function Ro(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case tn:return"Fragment";case en:return"Portal";case Co:return"Profiler";case Ps:return"StrictMode";case No:return"Suspense";case To:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case na:return(e.displayName||"Context")+".Consumer";case ta:return(e._context.displayName||"Context")+".Provider";case js:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ls:return t=e.displayName||null,t!==null?t:Ro(e.type)||"Memo";case ht:t=e._payload,e=e._init;try{return Ro(e(t))}catch{}}return null}function Xf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ro(t);case 8:return t===Ps?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Pt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ia(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Gf(e){var t=ia(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Nr(e){e._valueTracker||(e._valueTracker=Gf(e))}function oa(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ia(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function oi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Po(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Pl(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Pt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function sa(e,t){t=t.checked,t!=null&&Rs(e,"checked",t,!1)}function jo(e,t){sa(e,t);var n=Pt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Lo(e,t.type,n):t.hasOwnProperty("defaultValue")&&Lo(e,t.type,Pt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function jl(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Lo(e,t,n){(t!=="number"||oi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Vn=Array.isArray;function pn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Pt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Oo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(v(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ll(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(v(92));if(Vn(n)){if(1<n.length)throw Error(v(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Pt(n)}}function la(e,t){var n=Pt(t.value),r=Pt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ol(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function ua(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ao(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?ua(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Tr,aa=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Tr=Tr||document.createElement("div"),Tr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Tr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function nr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var qn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Jf=["Webkit","ms","Moz","O"];Object.keys(qn).forEach(function(e){Jf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),qn[t]=qn[e]})});function ca(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||qn.hasOwnProperty(e)&&qn[e]?(""+t).trim():t+"px"}function fa(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=ca(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Zf=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Io(e,t){if(t){if(Zf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(v(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(v(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(v(61))}if(t.style!=null&&typeof t.style!="object")throw Error(v(62))}}function zo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Do=null;function Os(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Mo=null,hn=null,mn=null;function Al(e){if(e=kr(e)){if(typeof Mo!="function")throw Error(v(280));var t=e.stateNode;t&&(t=Ii(t),Mo(e.stateNode,e.type,t))}}function da(e){hn?mn?mn.push(e):mn=[e]:hn=e}function pa(){if(hn){var e=hn,t=mn;if(mn=hn=null,Al(e),t)for(e=0;e<t.length;e++)Al(t[e])}}function ha(e,t){return e(t)}function ma(){}var Zi=!1;function ya(e,t,n){if(Zi)return e(t,n);Zi=!0;try{return ha(e,t,n)}finally{Zi=!1,(hn!==null||mn!==null)&&(ma(),pa())}}function rr(e,t){var n=e.stateNode;if(n===null)return null;var r=Ii(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(v(231,t,typeof n));return n}var Bo=!1;if(st)try{var An={};Object.defineProperty(An,"passive",{get:function(){Bo=!0}}),window.addEventListener("test",An,An),window.removeEventListener("test",An,An)}catch{Bo=!1}function bf(e,t,n,r,i,o,s,l,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(y){this.onError(y)}}var Kn=!1,si=null,li=!1,Fo=null,ed={onError:function(e){Kn=!0,si=e}};function td(e,t,n,r,i,o,s,l,u){Kn=!1,si=null,bf.apply(ed,arguments)}function nd(e,t,n,r,i,o,s,l,u){if(td.apply(this,arguments),Kn){if(Kn){var c=si;Kn=!1,si=null}else throw Error(v(198));li||(li=!0,Fo=c)}}function Gt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function ga(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Il(e){if(Gt(e)!==e)throw Error(v(188))}function rd(e){var t=e.alternate;if(!t){if(t=Gt(e),t===null)throw Error(v(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return Il(i),e;if(o===r)return Il(i),t;o=o.sibling}throw Error(v(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(v(189))}}if(n.alternate!==r)throw Error(v(190))}if(n.tag!==3)throw Error(v(188));return n.stateNode.current===n?e:t}function va(e){return e=rd(e),e!==null?wa(e):null}function wa(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=wa(e);if(t!==null)return t;e=e.sibling}return null}var xa=Ne.unstable_scheduleCallback,zl=Ne.unstable_cancelCallback,id=Ne.unstable_shouldYield,od=Ne.unstable_requestPaint,J=Ne.unstable_now,sd=Ne.unstable_getCurrentPriorityLevel,As=Ne.unstable_ImmediatePriority,ka=Ne.unstable_UserBlockingPriority,ui=Ne.unstable_NormalPriority,ld=Ne.unstable_LowPriority,_a=Ne.unstable_IdlePriority,ji=null,Ge=null;function ud(e){if(Ge&&typeof Ge.onCommitFiberRoot=="function")try{Ge.onCommitFiberRoot(ji,e,void 0,(e.current.flags&128)===128)}catch{}}var Ve=Math.clz32?Math.clz32:fd,ad=Math.log,cd=Math.LN2;function fd(e){return e>>>=0,e===0?32:31-(ad(e)/cd|0)|0}var Rr=64,Pr=4194304;function Wn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ai(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=Wn(l):(o&=s,o!==0&&(r=Wn(o)))}else s=n&~i,s!==0?r=Wn(s):o!==0&&(r=Wn(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ve(t),i=1<<n,r|=e[n],t&=~i;return r}function dd(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function pd(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ve(o),l=1<<s,u=i[s];u===-1?(!(l&n)||l&r)&&(i[s]=dd(l,t)):u<=t&&(e.expiredLanes|=l),o&=~l}}function Uo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Sa(){var e=Rr;return Rr<<=1,!(Rr&4194240)&&(Rr=64),e}function bi(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function wr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ve(t),e[t]=n}function hd(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ve(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Is(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ve(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var U=0;function Ea(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ca,zs,Na,Ta,Ra,$o=!1,jr=[],xt=null,kt=null,_t=null,ir=new Map,or=new Map,yt=[],md="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dl(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":kt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":or.delete(t.pointerId)}}function In(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=kr(t),t!==null&&zs(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function yd(e,t,n,r,i){switch(t){case"focusin":return xt=In(xt,e,t,n,r,i),!0;case"dragenter":return kt=In(kt,e,t,n,r,i),!0;case"mouseover":return _t=In(_t,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return ir.set(o,In(ir.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,or.set(o,In(or.get(o)||null,e,t,n,r,i)),!0}return!1}function Pa(e){var t=Ft(e.target);if(t!==null){var n=Gt(t);if(n!==null){if(t=n.tag,t===13){if(t=ga(n),t!==null){e.blockedOn=t,Ra(e.priority,function(){Na(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Vo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Do=r,n.target.dispatchEvent(r),Do=null}else return t=kr(n),t!==null&&zs(t),e.blockedOn=n,!1;t.shift()}return!0}function Ml(e,t,n){Qr(e)&&n.delete(t)}function gd(){$o=!1,xt!==null&&Qr(xt)&&(xt=null),kt!==null&&Qr(kt)&&(kt=null),_t!==null&&Qr(_t)&&(_t=null),ir.forEach(Ml),or.forEach(Ml)}function zn(e,t){e.blockedOn===t&&(e.blockedOn=null,$o||($o=!0,Ne.unstable_scheduleCallback(Ne.unstable_NormalPriority,gd)))}function sr(e){function t(i){return zn(i,e)}if(0<jr.length){zn(jr[0],e);for(var n=1;n<jr.length;n++){var r=jr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(xt!==null&&zn(xt,e),kt!==null&&zn(kt,e),_t!==null&&zn(_t,e),ir.forEach(t),or.forEach(t),n=0;n<yt.length;n++)r=yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<yt.length&&(n=yt[0],n.blockedOn===null);)Pa(n),n.blockedOn===null&&yt.shift()}var yn=ct.ReactCurrentBatchConfig,ci=!0;function vd(e,t,n,r){var i=U,o=yn.transition;yn.transition=null;try{U=1,Ds(e,t,n,r)}finally{U=i,yn.transition=o}}function wd(e,t,n,r){var i=U,o=yn.transition;yn.transition=null;try{U=4,Ds(e,t,n,r)}finally{U=i,yn.transition=o}}function Ds(e,t,n,r){if(ci){var i=Vo(e,t,n,r);if(i===null)ao(e,t,r,fi,n),Dl(e,r);else if(yd(i,e,t,n,r))r.stopPropagation();else if(Dl(e,r),t&4&&-1<md.indexOf(e)){for(;i!==null;){var o=kr(i);if(o!==null&&Ca(o),o=Vo(e,t,n,r),o===null&&ao(e,t,r,fi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else ao(e,t,r,null,n)}}var fi=null;function Vo(e,t,n,r){if(fi=null,e=Os(r),e=Ft(e),e!==null)if(t=Gt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=ga(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return fi=e,null}function ja(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(sd()){case As:return 1;case ka:return 4;case ui:case ld:return 16;case _a:return 536870912;default:return 16}default:return 16}}var vt=null,Ms=null,qr=null;function La(){if(qr)return qr;var e,t=Ms,n=t.length,r,i="value"in vt?vt.value:vt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return qr=i.slice(e,1<r?1-r:void 0)}function Kr(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Lr(){return!0}function Bl(){return!1}function Re(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Lr:Bl,this.isPropagationStopped=Bl,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Lr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Lr)},persist:function(){},isPersistent:Lr}),t}var Tn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bs=Re(Tn),xr=Y({},Tn,{view:0,detail:0}),xd=Re(xr),eo,to,Dn,Li=Y({},xr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fs,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Dn&&(Dn&&e.type==="mousemove"?(eo=e.screenX-Dn.screenX,to=e.screenY-Dn.screenY):to=eo=0,Dn=e),eo)},movementY:function(e){return"movementY"in e?e.movementY:to}}),Fl=Re(Li),kd=Y({},Li,{dataTransfer:0}),_d=Re(kd),Sd=Y({},xr,{relatedTarget:0}),no=Re(Sd),Ed=Y({},Tn,{animationName:0,elapsedTime:0,pseudoElement:0}),Cd=Re(Ed),Nd=Y({},Tn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Td=Re(Nd),Rd=Y({},Tn,{data:0}),Ul=Re(Rd),Pd={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jd={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ld={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Od(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ld[e])?!!t[e]:!1}function Fs(){return Od}var Ad=Y({},xr,{key:function(e){if(e.key){var t=Pd[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Kr(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jd[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fs,charCode:function(e){return e.type==="keypress"?Kr(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Kr(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Id=Re(Ad),zd=Y({},Li,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),$l=Re(zd),Dd=Y({},xr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fs}),Md=Re(Dd),Bd=Y({},Tn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Fd=Re(Bd),Ud=Y({},Li,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$d=Re(Ud),Vd=[9,13,27,32],Us=st&&"CompositionEvent"in window,Yn=null;st&&"documentMode"in document&&(Yn=document.documentMode);var Wd=st&&"TextEvent"in window&&!Yn,Oa=st&&(!Us||Yn&&8<Yn&&11>=Yn),Vl=String.fromCharCode(32),Wl=!1;function Aa(e,t){switch(e){case"keyup":return Vd.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ia(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var nn=!1;function Hd(e,t){switch(e){case"compositionend":return Ia(t);case"keypress":return t.which!==32?null:(Wl=!0,Vl);case"textInput":return e=t.data,e===Vl&&Wl?null:e;default:return null}}function Qd(e,t){if(nn)return e==="compositionend"||!Us&&Aa(e,t)?(e=La(),qr=Ms=vt=null,nn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Oa&&t.locale!=="ko"?null:t.data;default:return null}}var qd={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hl(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!qd[e.type]:t==="textarea"}function za(e,t,n,r){da(r),t=di(t,"onChange"),0<t.length&&(n=new Bs("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Xn=null,lr=null;function Kd(e){qa(e,0)}function Oi(e){var t=sn(e);if(oa(t))return e}function Yd(e,t){if(e==="change")return t}var Da=!1;if(st){var ro;if(st){var io="oninput"in document;if(!io){var Ql=document.createElement("div");Ql.setAttribute("oninput","return;"),io=typeof Ql.oninput=="function"}ro=io}else ro=!1;Da=ro&&(!document.documentMode||9<document.documentMode)}function ql(){Xn&&(Xn.detachEvent("onpropertychange",Ma),lr=Xn=null)}function Ma(e){if(e.propertyName==="value"&&Oi(lr)){var t=[];za(t,lr,e,Os(e)),ya(Kd,t)}}function Xd(e,t,n){e==="focusin"?(ql(),Xn=t,lr=n,Xn.attachEvent("onpropertychange",Ma)):e==="focusout"&&ql()}function Gd(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Oi(lr)}function Jd(e,t){if(e==="click")return Oi(t)}function Zd(e,t){if(e==="input"||e==="change")return Oi(t)}function bd(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var He=typeof Object.is=="function"?Object.is:bd;function ur(e,t){if(He(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Eo.call(t,i)||!He(e[i],t[i]))return!1}return!0}function Kl(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Yl(e,t){var n=Kl(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Kl(n)}}function Ba(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ba(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Fa(){for(var e=window,t=oi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=oi(e.document)}return t}function $s(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function ep(e){var t=Fa(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ba(n.ownerDocument.documentElement,n)){if(r!==null&&$s(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Yl(n,o);var s=Yl(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var tp=st&&"documentMode"in document&&11>=document.documentMode,rn=null,Wo=null,Gn=null,Ho=!1;function Xl(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ho||rn==null||rn!==oi(r)||(r=rn,"selectionStart"in r&&$s(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gn&&ur(Gn,r)||(Gn=r,r=di(Wo,"onSelect"),0<r.length&&(t=new Bs("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rn)))}function Or(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var on={animationend:Or("Animation","AnimationEnd"),animationiteration:Or("Animation","AnimationIteration"),animationstart:Or("Animation","AnimationStart"),transitionend:Or("Transition","TransitionEnd")},oo={},Ua={};st&&(Ua=document.createElement("div").style,"AnimationEvent"in window||(delete on.animationend.animation,delete on.animationiteration.animation,delete on.animationstart.animation),"TransitionEvent"in window||delete on.transitionend.transition);function Ai(e){if(oo[e])return oo[e];if(!on[e])return e;var t=on[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ua)return oo[e]=t[n];return e}var $a=Ai("animationend"),Va=Ai("animationiteration"),Wa=Ai("animationstart"),Ha=Ai("transitionend"),Qa=new Map,Gl="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lt(e,t){Qa.set(e,t),Xt(t,[e])}for(var so=0;so<Gl.length;so++){var lo=Gl[so],np=lo.toLowerCase(),rp=lo[0].toUpperCase()+lo.slice(1);Lt(np,"on"+rp)}Lt($a,"onAnimationEnd");Lt(Va,"onAnimationIteration");Lt(Wa,"onAnimationStart");Lt("dblclick","onDoubleClick");Lt("focusin","onFocus");Lt("focusout","onBlur");Lt(Ha,"onTransitionEnd");wn("onMouseEnter",["mouseout","mouseover"]);wn("onMouseLeave",["mouseout","mouseover"]);wn("onPointerEnter",["pointerout","pointerover"]);wn("onPointerLeave",["pointerout","pointerover"]);Xt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Xt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Xt("onBeforeInput",["compositionend","keypress","textInput","paste"]);Xt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Xt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Hn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ip=new Set("cancel close invalid load scroll toggle".split(" ").concat(Hn));function Jl(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,nd(r,t,void 0,e),e.currentTarget=null}function qa(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],u=l.instance,c=l.currentTarget;if(l=l.listener,u!==o&&i.isPropagationStopped())break e;Jl(i,l,c),o=u}else for(s=0;s<r.length;s++){if(l=r[s],u=l.instance,c=l.currentTarget,l=l.listener,u!==o&&i.isPropagationStopped())break e;Jl(i,l,c),o=u}}}if(li)throw e=Fo,li=!1,Fo=null,e}function V(e,t){var n=t[Xo];n===void 0&&(n=t[Xo]=new Set);var r=e+"__bubble";n.has(r)||(Ka(t,e,2,!1),n.add(r))}function uo(e,t,n){var r=0;t&&(r|=4),Ka(n,e,r,t)}var Ar="_reactListening"+Math.random().toString(36).slice(2);function ar(e){if(!e[Ar]){e[Ar]=!0,ea.forEach(function(n){n!=="selectionchange"&&(ip.has(n)||uo(n,!1,e),uo(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ar]||(t[Ar]=!0,uo("selectionchange",!1,t))}}function Ka(e,t,n,r){switch(ja(t)){case 1:var i=vd;break;case 4:i=wd;break;default:i=Ds}n=i.bind(null,t,n,e),i=void 0,!Bo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function ao(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;s=s.return}for(;l!==null;){if(s=Ft(l),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}l=l.parentNode}}r=r.return}ya(function(){var c=o,y=Os(n),m=[];e:{var h=Qa.get(e);if(h!==void 0){var x=Bs,k=e;switch(e){case"keypress":if(Kr(n)===0)break e;case"keydown":case"keyup":x=Id;break;case"focusin":k="focus",x=no;break;case"focusout":k="blur",x=no;break;case"beforeblur":case"afterblur":x=no;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=Fl;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=_d;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=Md;break;case $a:case Va:case Wa:x=Cd;break;case Ha:x=Fd;break;case"scroll":x=xd;break;case"wheel":x=$d;break;case"copy":case"cut":case"paste":x=Td;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=$l}var w=(t&4)!==0,F=!w&&e==="scroll",f=w?h!==null?h+"Capture":null:h;w=[];for(var a=c,d;a!==null;){d=a;var g=d.stateNode;if(d.tag===5&&g!==null&&(d=g,f!==null&&(g=rr(a,f),g!=null&&w.push(cr(a,g,d)))),F)break;a=a.return}0<w.length&&(h=new x(h,k,null,n,y),m.push({event:h,listeners:w}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",h&&n!==Do&&(k=n.relatedTarget||n.fromElement)&&(Ft(k)||k[lt]))break e;if((x||h)&&(h=y.window===y?y:(h=y.ownerDocument)?h.defaultView||h.parentWindow:window,x?(k=n.relatedTarget||n.toElement,x=c,k=k?Ft(k):null,k!==null&&(F=Gt(k),k!==F||k.tag!==5&&k.tag!==6)&&(k=null)):(x=null,k=c),x!==k)){if(w=Fl,g="onMouseLeave",f="onMouseEnter",a="mouse",(e==="pointerout"||e==="pointerover")&&(w=$l,g="onPointerLeave",f="onPointerEnter",a="pointer"),F=x==null?h:sn(x),d=k==null?h:sn(k),h=new w(g,a+"leave",x,n,y),h.target=F,h.relatedTarget=d,g=null,Ft(y)===c&&(w=new w(f,a+"enter",k,n,y),w.target=d,w.relatedTarget=F,g=w),F=g,x&&k)t:{for(w=x,f=k,a=0,d=w;d;d=bt(d))a++;for(d=0,g=f;g;g=bt(g))d++;for(;0<a-d;)w=bt(w),a--;for(;0<d-a;)f=bt(f),d--;for(;a--;){if(w===f||f!==null&&w===f.alternate)break t;w=bt(w),f=bt(f)}w=null}else w=null;x!==null&&Zl(m,h,x,w,!1),k!==null&&F!==null&&Zl(m,F,k,w,!0)}}e:{if(h=c?sn(c):window,x=h.nodeName&&h.nodeName.toLowerCase(),x==="select"||x==="input"&&h.type==="file")var _=Yd;else if(Hl(h))if(Da)_=Zd;else{_=Gd;var S=Xd}else(x=h.nodeName)&&x.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(_=Jd);if(_&&(_=_(e,c))){za(m,_,n,y);break e}S&&S(e,h,c),e==="focusout"&&(S=h._wrapperState)&&S.controlled&&h.type==="number"&&Lo(h,"number",h.value)}switch(S=c?sn(c):window,e){case"focusin":(Hl(S)||S.contentEditable==="true")&&(rn=S,Wo=c,Gn=null);break;case"focusout":Gn=Wo=rn=null;break;case"mousedown":Ho=!0;break;case"contextmenu":case"mouseup":case"dragend":Ho=!1,Xl(m,n,y);break;case"selectionchange":if(tp)break;case"keydown":case"keyup":Xl(m,n,y)}var T;if(Us)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else nn?Aa(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Oa&&n.locale!=="ko"&&(nn||P!=="onCompositionStart"?P==="onCompositionEnd"&&nn&&(T=La()):(vt=y,Ms="value"in vt?vt.value:vt.textContent,nn=!0)),S=di(c,P),0<S.length&&(P=new Ul(P,e,null,n,y),m.push({event:P,listeners:S}),T?P.data=T:(T=Ia(n),T!==null&&(P.data=T)))),(T=Wd?Hd(e,n):Qd(e,n))&&(c=di(c,"onBeforeInput"),0<c.length&&(y=new Ul("onBeforeInput","beforeinput",null,n,y),m.push({event:y,listeners:c}),y.data=T))}qa(m,t)})}function cr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function di(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=rr(e,n),o!=null&&r.unshift(cr(e,o,i)),o=rr(e,t),o!=null&&r.push(cr(e,o,i))),e=e.return}return r}function bt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Zl(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,u=l.alternate,c=l.stateNode;if(u!==null&&u===r)break;l.tag===5&&c!==null&&(l=c,i?(u=rr(n,o),u!=null&&s.unshift(cr(n,u,l))):i||(u=rr(n,o),u!=null&&s.push(cr(n,u,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var op=/\r\n?/g,sp=/\u0000|\uFFFD/g;function bl(e){return(typeof e=="string"?e:""+e).replace(op,`
`).replace(sp,"")}function Ir(e,t,n){if(t=bl(t),bl(e)!==t&&n)throw Error(v(425))}function pi(){}var Qo=null,qo=null;function Ko(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Yo=typeof setTimeout=="function"?setTimeout:void 0,lp=typeof clearTimeout=="function"?clearTimeout:void 0,eu=typeof Promise=="function"?Promise:void 0,up=typeof queueMicrotask=="function"?queueMicrotask:typeof eu<"u"?function(e){return eu.resolve(null).then(e).catch(ap)}:Yo;function ap(e){setTimeout(function(){throw e})}function co(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),sr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);sr(t)}function St(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function tu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Rn=Math.random().toString(36).slice(2),Xe="__reactFiber$"+Rn,fr="__reactProps$"+Rn,lt="__reactContainer$"+Rn,Xo="__reactEvents$"+Rn,cp="__reactListeners$"+Rn,fp="__reactHandles$"+Rn;function Ft(e){var t=e[Xe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[lt]||n[Xe]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=tu(e);e!==null;){if(n=e[Xe])return n;e=tu(e)}return t}e=n,n=e.parentNode}return null}function kr(e){return e=e[Xe]||e[lt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function sn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(v(33))}function Ii(e){return e[fr]||null}var Go=[],ln=-1;function Ot(e){return{current:e}}function W(e){0>ln||(e.current=Go[ln],Go[ln]=null,ln--)}function $(e,t){ln++,Go[ln]=e.current,e.current=t}var jt={},de=Ot(jt),xe=Ot(!1),Ht=jt;function xn(e,t){var n=e.type.contextTypes;if(!n)return jt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function ke(e){return e=e.childContextTypes,e!=null}function hi(){W(xe),W(de)}function nu(e,t,n){if(de.current!==jt)throw Error(v(168));$(de,t),$(xe,n)}function Ya(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(v(108,Xf(e)||"Unknown",i));return Y({},n,r)}function mi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||jt,Ht=de.current,$(de,e),$(xe,xe.current),!0}function ru(e,t,n){var r=e.stateNode;if(!r)throw Error(v(169));n?(e=Ya(e,t,Ht),r.__reactInternalMemoizedMergedChildContext=e,W(xe),W(de),$(de,e)):W(xe),$(xe,n)}var nt=null,zi=!1,fo=!1;function Xa(e){nt===null?nt=[e]:nt.push(e)}function dp(e){zi=!0,Xa(e)}function At(){if(!fo&&nt!==null){fo=!0;var e=0,t=U;try{var n=nt;for(U=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}nt=null,zi=!1}catch(i){throw nt!==null&&(nt=nt.slice(e+1)),xa(As,At),i}finally{U=t,fo=!1}}return null}var un=[],an=0,yi=null,gi=0,Pe=[],je=0,Qt=null,rt=1,it="";function Mt(e,t){un[an++]=gi,un[an++]=yi,yi=e,gi=t}function Ga(e,t,n){Pe[je++]=rt,Pe[je++]=it,Pe[je++]=Qt,Qt=e;var r=rt;e=it;var i=32-Ve(r)-1;r&=~(1<<i),n+=1;var o=32-Ve(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,rt=1<<32-Ve(t)+i|n<<i|r,it=o+e}else rt=1<<o|n<<i|r,it=e}function Vs(e){e.return!==null&&(Mt(e,1),Ga(e,1,0))}function Ws(e){for(;e===yi;)yi=un[--an],un[an]=null,gi=un[--an],un[an]=null;for(;e===Qt;)Qt=Pe[--je],Pe[je]=null,it=Pe[--je],Pe[je]=null,rt=Pe[--je],Pe[je]=null}var Ce=null,Ee=null,H=!1,$e=null;function Ja(e,t){var n=Oe(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function iu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ce=e,Ee=St(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ce=e,Ee=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Qt!==null?{id:rt,overflow:it}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Oe(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ce=e,Ee=null,!0):!1;default:return!1}}function Jo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Zo(e){if(H){var t=Ee;if(t){var n=t;if(!iu(e,t)){if(Jo(e))throw Error(v(418));t=St(n.nextSibling);var r=Ce;t&&iu(e,t)?Ja(r,n):(e.flags=e.flags&-4097|2,H=!1,Ce=e)}}else{if(Jo(e))throw Error(v(418));e.flags=e.flags&-4097|2,H=!1,Ce=e}}}function ou(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ce=e}function zr(e){if(e!==Ce)return!1;if(!H)return ou(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ko(e.type,e.memoizedProps)),t&&(t=Ee)){if(Jo(e))throw Za(),Error(v(418));for(;t;)Ja(e,t),t=St(t.nextSibling)}if(ou(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(v(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ee=St(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ee=null}}else Ee=Ce?St(e.stateNode.nextSibling):null;return!0}function Za(){for(var e=Ee;e;)e=St(e.nextSibling)}function kn(){Ee=Ce=null,H=!1}function Hs(e){$e===null?$e=[e]:$e.push(e)}var pp=ct.ReactCurrentBatchConfig;function Mn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(v(309));var r=n.stateNode}if(!r)throw Error(v(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(v(284));if(!n._owner)throw Error(v(290,e))}return e}function Dr(e,t){throw e=Object.prototype.toString.call(t),Error(v(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function su(e){var t=e._init;return t(e._payload)}function ba(e){function t(f,a){if(e){var d=f.deletions;d===null?(f.deletions=[a],f.flags|=16):d.push(a)}}function n(f,a){if(!e)return null;for(;a!==null;)t(f,a),a=a.sibling;return null}function r(f,a){for(f=new Map;a!==null;)a.key!==null?f.set(a.key,a):f.set(a.index,a),a=a.sibling;return f}function i(f,a){return f=Tt(f,a),f.index=0,f.sibling=null,f}function o(f,a,d){return f.index=d,e?(d=f.alternate,d!==null?(d=d.index,d<a?(f.flags|=2,a):d):(f.flags|=2,a)):(f.flags|=1048576,a)}function s(f){return e&&f.alternate===null&&(f.flags|=2),f}function l(f,a,d,g){return a===null||a.tag!==6?(a=wo(d,f.mode,g),a.return=f,a):(a=i(a,d),a.return=f,a)}function u(f,a,d,g){var _=d.type;return _===tn?y(f,a,d.props.children,g,d.key):a!==null&&(a.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===ht&&su(_)===a.type)?(g=i(a,d.props),g.ref=Mn(f,a,d),g.return=f,g):(g=ei(d.type,d.key,d.props,null,f.mode,g),g.ref=Mn(f,a,d),g.return=f,g)}function c(f,a,d,g){return a===null||a.tag!==4||a.stateNode.containerInfo!==d.containerInfo||a.stateNode.implementation!==d.implementation?(a=xo(d,f.mode,g),a.return=f,a):(a=i(a,d.children||[]),a.return=f,a)}function y(f,a,d,g,_){return a===null||a.tag!==7?(a=Wt(d,f.mode,g,_),a.return=f,a):(a=i(a,d),a.return=f,a)}function m(f,a,d){if(typeof a=="string"&&a!==""||typeof a=="number")return a=wo(""+a,f.mode,d),a.return=f,a;if(typeof a=="object"&&a!==null){switch(a.$$typeof){case Cr:return d=ei(a.type,a.key,a.props,null,f.mode,d),d.ref=Mn(f,null,a),d.return=f,d;case en:return a=xo(a,f.mode,d),a.return=f,a;case ht:var g=a._init;return m(f,g(a._payload),d)}if(Vn(a)||On(a))return a=Wt(a,f.mode,d,null),a.return=f,a;Dr(f,a)}return null}function h(f,a,d,g){var _=a!==null?a.key:null;if(typeof d=="string"&&d!==""||typeof d=="number")return _!==null?null:l(f,a,""+d,g);if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Cr:return d.key===_?u(f,a,d,g):null;case en:return d.key===_?c(f,a,d,g):null;case ht:return _=d._init,h(f,a,_(d._payload),g)}if(Vn(d)||On(d))return _!==null?null:y(f,a,d,g,null);Dr(f,d)}return null}function x(f,a,d,g,_){if(typeof g=="string"&&g!==""||typeof g=="number")return f=f.get(d)||null,l(a,f,""+g,_);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Cr:return f=f.get(g.key===null?d:g.key)||null,u(a,f,g,_);case en:return f=f.get(g.key===null?d:g.key)||null,c(a,f,g,_);case ht:var S=g._init;return x(f,a,d,S(g._payload),_)}if(Vn(g)||On(g))return f=f.get(d)||null,y(a,f,g,_,null);Dr(a,g)}return null}function k(f,a,d,g){for(var _=null,S=null,T=a,P=a=0,j=null;T!==null&&P<d.length;P++){T.index>P?(j=T,T=null):j=T.sibling;var L=h(f,T,d[P],g);if(L===null){T===null&&(T=j);break}e&&T&&L.alternate===null&&t(f,T),a=o(L,a,P),S===null?_=L:S.sibling=L,S=L,T=j}if(P===d.length)return n(f,T),H&&Mt(f,P),_;if(T===null){for(;P<d.length;P++)T=m(f,d[P],g),T!==null&&(a=o(T,a,P),S===null?_=T:S.sibling=T,S=T);return H&&Mt(f,P),_}for(T=r(f,T);P<d.length;P++)j=x(T,f,P,d[P],g),j!==null&&(e&&j.alternate!==null&&T.delete(j.key===null?P:j.key),a=o(j,a,P),S===null?_=j:S.sibling=j,S=j);return e&&T.forEach(function(ue){return t(f,ue)}),H&&Mt(f,P),_}function w(f,a,d,g){var _=On(d);if(typeof _!="function")throw Error(v(150));if(d=_.call(d),d==null)throw Error(v(151));for(var S=_=null,T=a,P=a=0,j=null,L=d.next();T!==null&&!L.done;P++,L=d.next()){T.index>P?(j=T,T=null):j=T.sibling;var ue=h(f,T,L.value,g);if(ue===null){T===null&&(T=j);break}e&&T&&ue.alternate===null&&t(f,T),a=o(ue,a,P),S===null?_=ue:S.sibling=ue,S=ue,T=j}if(L.done)return n(f,T),H&&Mt(f,P),_;if(T===null){for(;!L.done;P++,L=d.next())L=m(f,L.value,g),L!==null&&(a=o(L,a,P),S===null?_=L:S.sibling=L,S=L);return H&&Mt(f,P),_}for(T=r(f,T);!L.done;P++,L=d.next())L=x(T,f,P,L.value,g),L!==null&&(e&&L.alternate!==null&&T.delete(L.key===null?P:L.key),a=o(L,a,P),S===null?_=L:S.sibling=L,S=L);return e&&T.forEach(function(Qe){return t(f,Qe)}),H&&Mt(f,P),_}function F(f,a,d,g){if(typeof d=="object"&&d!==null&&d.type===tn&&d.key===null&&(d=d.props.children),typeof d=="object"&&d!==null){switch(d.$$typeof){case Cr:e:{for(var _=d.key,S=a;S!==null;){if(S.key===_){if(_=d.type,_===tn){if(S.tag===7){n(f,S.sibling),a=i(S,d.props.children),a.return=f,f=a;break e}}else if(S.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===ht&&su(_)===S.type){n(f,S.sibling),a=i(S,d.props),a.ref=Mn(f,S,d),a.return=f,f=a;break e}n(f,S);break}else t(f,S);S=S.sibling}d.type===tn?(a=Wt(d.props.children,f.mode,g,d.key),a.return=f,f=a):(g=ei(d.type,d.key,d.props,null,f.mode,g),g.ref=Mn(f,a,d),g.return=f,f=g)}return s(f);case en:e:{for(S=d.key;a!==null;){if(a.key===S)if(a.tag===4&&a.stateNode.containerInfo===d.containerInfo&&a.stateNode.implementation===d.implementation){n(f,a.sibling),a=i(a,d.children||[]),a.return=f,f=a;break e}else{n(f,a);break}else t(f,a);a=a.sibling}a=xo(d,f.mode,g),a.return=f,f=a}return s(f);case ht:return S=d._init,F(f,a,S(d._payload),g)}if(Vn(d))return k(f,a,d,g);if(On(d))return w(f,a,d,g);Dr(f,d)}return typeof d=="string"&&d!==""||typeof d=="number"?(d=""+d,a!==null&&a.tag===6?(n(f,a.sibling),a=i(a,d),a.return=f,f=a):(n(f,a),a=wo(d,f.mode,g),a.return=f,f=a),s(f)):n(f,a)}return F}var _n=ba(!0),ec=ba(!1),vi=Ot(null),wi=null,cn=null,Qs=null;function qs(){Qs=cn=wi=null}function Ks(e){var t=vi.current;W(vi),e._currentValue=t}function bo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function gn(e,t){wi=e,Qs=cn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(we=!0),e.firstContext=null)}function Ie(e){var t=e._currentValue;if(Qs!==e)if(e={context:e,memoizedValue:t,next:null},cn===null){if(wi===null)throw Error(v(308));cn=e,wi.dependencies={lanes:0,firstContext:e}}else cn=cn.next=e;return t}var Ut=null;function Ys(e){Ut===null?Ut=[e]:Ut.push(e)}function tc(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,Ys(t)):(n.next=i.next,i.next=n),t.interleaved=n,ut(e,r)}function ut(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var mt=!1;function Xs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function nc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ot(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Et(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,ut(e,n)}return i=r.interleaved,i===null?(t.next=t,Ys(r)):(t.next=i.next,i.next=t),r.interleaved=t,ut(e,n)}function Yr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Is(e,n)}}function lu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function xi(e,t,n,r){var i=e.updateQueue;mt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var u=l,c=u.next;u.next=null,s===null?o=c:s.next=c,s=u;var y=e.alternate;y!==null&&(y=y.updateQueue,l=y.lastBaseUpdate,l!==s&&(l===null?y.firstBaseUpdate=c:l.next=c,y.lastBaseUpdate=u))}if(o!==null){var m=i.baseState;s=0,y=c=u=null,l=o;do{var h=l.lane,x=l.eventTime;if((r&h)===h){y!==null&&(y=y.next={eventTime:x,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var k=e,w=l;switch(h=t,x=n,w.tag){case 1:if(k=w.payload,typeof k=="function"){m=k.call(x,m,h);break e}m=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=w.payload,h=typeof k=="function"?k.call(x,m,h):k,h==null)break e;m=Y({},m,h);break e;case 2:mt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else x={eventTime:x,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},y===null?(c=y=x,u=m):y=y.next=x,s|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(y===null&&(u=m),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=y,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);Kt|=s,e.lanes=s,e.memoizedState=m}}function uu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(v(191,i));i.call(r)}}}var _r={},Je=Ot(_r),dr=Ot(_r),pr=Ot(_r);function $t(e){if(e===_r)throw Error(v(174));return e}function Gs(e,t){switch($(pr,t),$(dr,e),$(Je,_r),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ao(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ao(t,e)}W(Je),$(Je,t)}function Sn(){W(Je),W(dr),W(pr)}function rc(e){$t(pr.current);var t=$t(Je.current),n=Ao(t,e.type);t!==n&&($(dr,e),$(Je,n))}function Js(e){dr.current===e&&(W(Je),W(dr))}var q=Ot(0);function ki(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var po=[];function Zs(){for(var e=0;e<po.length;e++)po[e]._workInProgressVersionPrimary=null;po.length=0}var Xr=ct.ReactCurrentDispatcher,ho=ct.ReactCurrentBatchConfig,qt=0,K=null,ee=null,re=null,_i=!1,Jn=!1,hr=0,hp=0;function ae(){throw Error(v(321))}function bs(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!He(e[n],t[n]))return!1;return!0}function el(e,t,n,r,i,o){if(qt=o,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xr.current=e===null||e.memoizedState===null?vp:wp,e=n(r,i),Jn){o=0;do{if(Jn=!1,hr=0,25<=o)throw Error(v(301));o+=1,re=ee=null,t.updateQueue=null,Xr.current=xp,e=n(r,i)}while(Jn)}if(Xr.current=Si,t=ee!==null&&ee.next!==null,qt=0,re=ee=K=null,_i=!1,t)throw Error(v(300));return e}function tl(){var e=hr!==0;return hr=0,e}function Ye(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return re===null?K.memoizedState=re=e:re=re.next=e,re}function ze(){if(ee===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=ee.next;var t=re===null?K.memoizedState:re.next;if(t!==null)re=t,ee=e;else{if(e===null)throw Error(v(310));ee=e,e={memoizedState:ee.memoizedState,baseState:ee.baseState,baseQueue:ee.baseQueue,queue:ee.queue,next:null},re===null?K.memoizedState=re=e:re=re.next=e}return re}function mr(e,t){return typeof t=="function"?t(e):t}function mo(e){var t=ze(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=ee,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,u=null,c=o;do{var y=c.lane;if((qt&y)===y)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var m={lane:y,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(l=u=m,s=r):u=u.next=m,K.lanes|=y,Kt|=y}c=c.next}while(c!==null&&c!==o);u===null?s=r:u.next=l,He(r,t.memoizedState)||(we=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,K.lanes|=o,Kt|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function yo(e){var t=ze(),n=t.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);He(o,t.memoizedState)||(we=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function ic(){}function oc(e,t){var n=K,r=ze(),i=t(),o=!He(r.memoizedState,i);if(o&&(r.memoizedState=i,we=!0),r=r.queue,nl(uc.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||re!==null&&re.memoizedState.tag&1){if(n.flags|=2048,yr(9,lc.bind(null,n,r,i,t),void 0,null),ie===null)throw Error(v(349));qt&30||sc(n,t,i)}return i}function sc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function lc(e,t,n,r){t.value=n,t.getSnapshot=r,ac(t)&&cc(e)}function uc(e,t,n){return n(function(){ac(t)&&cc(e)})}function ac(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!He(e,n)}catch{return!0}}function cc(e){var t=ut(e,1);t!==null&&We(t,e,1,-1)}function au(e){var t=Ye();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:mr,lastRenderedState:e},t.queue=e,e=e.dispatch=gp.bind(null,K,e),[t.memoizedState,e]}function yr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function fc(){return ze().memoizedState}function Gr(e,t,n,r){var i=Ye();K.flags|=e,i.memoizedState=yr(1|t,n,void 0,r===void 0?null:r)}function Di(e,t,n,r){var i=ze();r=r===void 0?null:r;var o=void 0;if(ee!==null){var s=ee.memoizedState;if(o=s.destroy,r!==null&&bs(r,s.deps)){i.memoizedState=yr(t,n,o,r);return}}K.flags|=e,i.memoizedState=yr(1|t,n,o,r)}function cu(e,t){return Gr(8390656,8,e,t)}function nl(e,t){return Di(2048,8,e,t)}function dc(e,t){return Di(4,2,e,t)}function pc(e,t){return Di(4,4,e,t)}function hc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function mc(e,t,n){return n=n!=null?n.concat([e]):null,Di(4,4,hc.bind(null,t,e),n)}function rl(){}function yc(e,t){var n=ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function gc(e,t){var n=ze();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&bs(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vc(e,t,n){return qt&21?(He(n,t)||(n=Sa(),K.lanes|=n,Kt|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,we=!0),e.memoizedState=n)}function mp(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var r=ho.transition;ho.transition={};try{e(!1),t()}finally{U=n,ho.transition=r}}function wc(){return ze().memoizedState}function yp(e,t,n){var r=Nt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},xc(e))kc(t,n);else if(n=tc(e,t,n,r),n!==null){var i=he();We(n,e,r,i),_c(n,t,r)}}function gp(e,t,n){var r=Nt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(xc(e))kc(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,He(l,s)){var u=t.interleaved;u===null?(i.next=i,Ys(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=tc(e,t,i,r),n!==null&&(i=he(),We(n,e,r,i),_c(n,t,r))}}function xc(e){var t=e.alternate;return e===K||t!==null&&t===K}function kc(e,t){Jn=_i=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function _c(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Is(e,n)}}var Si={readContext:Ie,useCallback:ae,useContext:ae,useEffect:ae,useImperativeHandle:ae,useInsertionEffect:ae,useLayoutEffect:ae,useMemo:ae,useReducer:ae,useRef:ae,useState:ae,useDebugValue:ae,useDeferredValue:ae,useTransition:ae,useMutableSource:ae,useSyncExternalStore:ae,useId:ae,unstable_isNewReconciler:!1},vp={readContext:Ie,useCallback:function(e,t){return Ye().memoizedState=[e,t===void 0?null:t],e},useContext:Ie,useEffect:cu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gr(4194308,4,hc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gr(4,2,e,t)},useMemo:function(e,t){var n=Ye();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ye();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=yp.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=Ye();return e={current:e},t.memoizedState=e},useState:au,useDebugValue:rl,useDeferredValue:function(e){return Ye().memoizedState=e},useTransition:function(){var e=au(!1),t=e[0];return e=mp.bind(null,e[1]),Ye().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,i=Ye();if(H){if(n===void 0)throw Error(v(407));n=n()}else{if(n=t(),ie===null)throw Error(v(349));qt&30||sc(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,cu(uc.bind(null,r,o,e),[e]),r.flags|=2048,yr(9,lc.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=Ye(),t=ie.identifierPrefix;if(H){var n=it,r=rt;n=(r&~(1<<32-Ve(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=hr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=hp++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},wp={readContext:Ie,useCallback:yc,useContext:Ie,useEffect:nl,useImperativeHandle:mc,useInsertionEffect:dc,useLayoutEffect:pc,useMemo:gc,useReducer:mo,useRef:fc,useState:function(){return mo(mr)},useDebugValue:rl,useDeferredValue:function(e){var t=ze();return vc(t,ee.memoizedState,e)},useTransition:function(){var e=mo(mr)[0],t=ze().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:oc,useId:wc,unstable_isNewReconciler:!1},xp={readContext:Ie,useCallback:yc,useContext:Ie,useEffect:nl,useImperativeHandle:mc,useInsertionEffect:dc,useLayoutEffect:pc,useMemo:gc,useReducer:yo,useRef:fc,useState:function(){return yo(mr)},useDebugValue:rl,useDeferredValue:function(e){var t=ze();return ee===null?t.memoizedState=e:vc(t,ee.memoizedState,e)},useTransition:function(){var e=yo(mr)[0],t=ze().memoizedState;return[e,t]},useMutableSource:ic,useSyncExternalStore:oc,useId:wc,unstable_isNewReconciler:!1};function Be(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function es(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Mi={isMounted:function(e){return(e=e._reactInternals)?Gt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=he(),i=Nt(e),o=ot(r,i);o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(We(t,e,i,r),Yr(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=he(),i=Nt(e),o=ot(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Et(e,o,i),t!==null&&(We(t,e,i,r),Yr(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=he(),r=Nt(e),i=ot(n,r);i.tag=2,t!=null&&(i.callback=t),t=Et(e,i,r),t!==null&&(We(t,e,r,n),Yr(t,e,r))}};function fu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!ur(n,r)||!ur(i,o):!0}function Sc(e,t,n){var r=!1,i=jt,o=t.contextType;return typeof o=="object"&&o!==null?o=Ie(o):(i=ke(t)?Ht:de.current,r=t.contextTypes,o=(r=r!=null)?xn(e,i):jt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Mi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function du(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Mi.enqueueReplaceState(t,t.state,null)}function ts(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Xs(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=Ie(o):(o=ke(t)?Ht:de.current,i.context=xn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(es(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Mi.enqueueReplaceState(i,i.state,null),xi(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function En(e,t){try{var n="",r=t;do n+=Yf(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function go(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ns(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var kp=typeof WeakMap=="function"?WeakMap:Map;function Ec(e,t,n){n=ot(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ci||(Ci=!0,ds=r),ns(e,t)},n}function Cc(e,t,n){n=ot(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ns(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){ns(e,t),typeof r!="function"&&(Ct===null?Ct=new Set([this]):Ct.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function pu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new kp;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=zp.bind(null,e,t,n),t.then(e,e))}function hu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function mu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ot(-1,1),t.tag=2,Et(n,t,1))),n.lanes|=1),e)}var _p=ct.ReactCurrentOwner,we=!1;function pe(e,t,n,r){t.child=e===null?ec(t,null,n,r):_n(t,e.child,n,r)}function yu(e,t,n,r,i){n=n.render;var o=t.ref;return gn(t,i),r=el(e,t,n,r,o,i),n=tl(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,at(e,t,i)):(H&&n&&Vs(t),t.flags|=1,pe(e,t,r,i),t.child)}function gu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!fl(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Nc(e,t,o,r,i)):(e=ei(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:ur,n(s,r)&&e.ref===t.ref)return at(e,t,i)}return t.flags|=1,e=Tt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Nc(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(ur(o,r)&&e.ref===t.ref)if(we=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(we=!0);else return t.lanes=e.lanes,at(e,t,i)}return rs(e,t,n,r,i)}function Tc(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(dn,Se),Se|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(dn,Se),Se|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,$(dn,Se),Se|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,$(dn,Se),Se|=r;return pe(e,t,i,n),t.child}function Rc(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function rs(e,t,n,r,i){var o=ke(n)?Ht:de.current;return o=xn(t,o),gn(t,i),n=el(e,t,n,r,o,i),r=tl(),e!==null&&!we?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,at(e,t,i)):(H&&r&&Vs(t),t.flags|=1,pe(e,t,n,i),t.child)}function vu(e,t,n,r,i){if(ke(n)){var o=!0;mi(t)}else o=!1;if(gn(t,i),t.stateNode===null)Jr(e,t),Sc(t,n,r),ts(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ie(c):(c=ke(n)?Ht:de.current,c=xn(t,c));var y=n.getDerivedStateFromProps,m=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function";m||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||u!==c)&&du(t,s,r,c),mt=!1;var h=t.memoizedState;s.state=h,xi(t,r,s,i),u=t.memoizedState,l!==r||h!==u||xe.current||mt?(typeof y=="function"&&(es(t,n,y,r),u=t.memoizedState),(l=mt||fu(t,n,l,r,h,u,c))?(m||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,nc(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Be(t.type,l),s.props=c,m=t.pendingProps,h=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=Ie(u):(u=ke(n)?Ht:de.current,u=xn(t,u));var x=n.getDerivedStateFromProps;(y=typeof x=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==m||h!==u)&&du(t,s,r,u),mt=!1,h=t.memoizedState,s.state=h,xi(t,r,s,i);var k=t.memoizedState;l!==m||h!==k||xe.current||mt?(typeof x=="function"&&(es(t,n,x,r),k=t.memoizedState),(c=mt||fu(t,n,c,r,h,k,u)||!1)?(y||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,k,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,k,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),s.props=r,s.state=k,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return is(e,t,n,r,o,i)}function is(e,t,n,r,i,o){Rc(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&ru(t,n,!1),at(e,t,o);r=t.stateNode,_p.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=_n(t,e.child,null,o),t.child=_n(t,null,l,o)):pe(e,t,l,o),t.memoizedState=r.state,i&&ru(t,n,!0),t.child}function Pc(e){var t=e.stateNode;t.pendingContext?nu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&nu(e,t.context,!1),Gs(e,t.containerInfo)}function wu(e,t,n,r,i){return kn(),Hs(i),t.flags|=256,pe(e,t,n,r),t.child}var os={dehydrated:null,treeContext:null,retryLane:0};function ss(e){return{baseLanes:e,cachePool:null,transitions:null}}function jc(e,t,n){var r=t.pendingProps,i=q.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),$(q,i&1),e===null)return Zo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ui(s,r,0,null),e=Wt(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=ss(n),t.memoizedState=os,e):il(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Sp(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=Tt(i,u),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Tt(l,o):(o=Wt(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?ss(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=os,r}return o=e.child,e=o.sibling,r=Tt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function il(e,t){return t=Ui({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Mr(e,t,n,r){return r!==null&&Hs(r),_n(t,e.child,null,n),e=il(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Sp(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=go(Error(v(422))),Mr(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ui({mode:"visible",children:r.children},i,0,null),o=Wt(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&_n(t,e.child,null,s),t.child.memoizedState=ss(s),t.memoizedState=os,o);if(!(t.mode&1))return Mr(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(v(419)),r=go(o,r,void 0),Mr(e,t,s,r)}if(l=(s&e.childLanes)!==0,we||l){if(r=ie,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,ut(e,i),We(r,e,i,-1))}return cl(),r=go(Error(v(421))),Mr(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=Dp.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ee=St(i.nextSibling),Ce=t,H=!0,$e=null,e!==null&&(Pe[je++]=rt,Pe[je++]=it,Pe[je++]=Qt,rt=e.id,it=e.overflow,Qt=t),t=il(t,r.children),t.flags|=4096,t)}function xu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),bo(e.return,t,n)}function vo(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Lc(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(pe(e,t,r.children,n),r=q.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xu(e,n,t);else if(e.tag===19)xu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if($(q,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&ki(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),vo(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&ki(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}vo(t,!0,n,null,o);break;case"together":vo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Jr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function at(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Kt|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(v(153));if(t.child!==null){for(e=t.child,n=Tt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Tt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ep(e,t,n){switch(t.tag){case 3:Pc(t),kn();break;case 5:rc(t);break;case 1:ke(t.type)&&mi(t);break;case 4:Gs(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;$(vi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?($(q,q.current&1),t.flags|=128,null):n&t.child.childLanes?jc(e,t,n):($(q,q.current&1),e=at(e,t,n),e!==null?e.sibling:null);$(q,q.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Lc(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),$(q,q.current),r)break;return null;case 22:case 23:return t.lanes=0,Tc(e,t,n)}return at(e,t,n)}var Oc,ls,Ac,Ic;Oc=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ls=function(){};Ac=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,$t(Je.current);var o=null;switch(n){case"input":i=Po(e,i),r=Po(e,r),o=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),o=[];break;case"textarea":i=Oo(e,i),r=Oo(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=pi)}Io(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var l=i[c];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(tr.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(l=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(u!=null||l!=null))if(c==="style")if(l){for(s in l)!l.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&l[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,l=l?l.__html:void 0,u!=null&&l!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(tr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&V("scroll",e),o||l===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Ic=function(e,t,n,r){n!==r&&(t.flags|=4)};function Bn(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Cp(e,t,n){var r=t.pendingProps;switch(Ws(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return ke(t.type)&&hi(),ce(t),null;case 3:return r=t.stateNode,Sn(),W(xe),W(de),Zs(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(zr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,$e!==null&&(ms($e),$e=null))),ls(e,t),ce(t),null;case 5:Js(t);var i=$t(pr.current);if(n=t.type,e!==null&&t.stateNode!=null)Ac(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(v(166));return ce(t),null}if(e=$t(Je.current),zr(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[Xe]=t,r[fr]=o,e=(t.mode&1)!==0,n){case"dialog":V("cancel",r),V("close",r);break;case"iframe":case"object":case"embed":V("load",r);break;case"video":case"audio":for(i=0;i<Hn.length;i++)V(Hn[i],r);break;case"source":V("error",r);break;case"img":case"image":case"link":V("error",r),V("load",r);break;case"details":V("toggle",r);break;case"input":Pl(r,o),V("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},V("invalid",r);break;case"textarea":Ll(r,o),V("invalid",r)}Io(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&Ir(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&Ir(r.textContent,l,e),i=["children",""+l]):tr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&V("scroll",r)}switch(n){case"input":Nr(r),jl(r,o,!0);break;case"textarea":Nr(r),Ol(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=pi)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=ua(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Xe]=t,e[fr]=r,Oc(e,t,!1,!1),t.stateNode=e;e:{switch(s=zo(n,r),n){case"dialog":V("cancel",e),V("close",e),i=r;break;case"iframe":case"object":case"embed":V("load",e),i=r;break;case"video":case"audio":for(i=0;i<Hn.length;i++)V(Hn[i],e);i=r;break;case"source":V("error",e),i=r;break;case"img":case"image":case"link":V("error",e),V("load",e),i=r;break;case"details":V("toggle",e),i=r;break;case"input":Pl(e,r),i=Po(e,r),V("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),V("invalid",e);break;case"textarea":Ll(e,r),i=Oo(e,r),V("invalid",e);break;default:i=r}Io(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var u=l[o];o==="style"?fa(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&aa(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&nr(e,u):typeof u=="number"&&nr(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(tr.hasOwnProperty(o)?u!=null&&o==="onScroll"&&V("scroll",e):u!=null&&Rs(e,o,u,s))}switch(n){case"input":Nr(e),jl(e,r,!1);break;case"textarea":Nr(e),Ol(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Pt(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?pn(e,!!r.multiple,o,!1):r.defaultValue!=null&&pn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=pi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)Ic(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(v(166));if(n=$t(pr.current),$t(Je.current),zr(t)){if(r=t.stateNode,n=t.memoizedProps,r[Xe]=t,(o=r.nodeValue!==n)&&(e=Ce,e!==null))switch(e.tag){case 3:Ir(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ir(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Xe]=t,t.stateNode=r}return ce(t),null;case 13:if(W(q),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ee!==null&&t.mode&1&&!(t.flags&128))Za(),kn(),t.flags|=98560,o=!1;else if(o=zr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(v(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(v(317));o[Xe]=t}else kn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),o=!1}else $e!==null&&(ms($e),$e=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||q.current&1?te===0&&(te=3):cl())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return Sn(),ls(e,t),e===null&&ar(t.stateNode.containerInfo),ce(t),null;case 10:return Ks(t.type._context),ce(t),null;case 17:return ke(t.type)&&hi(),ce(t),null;case 19:if(W(q),o=t.memoizedState,o===null)return ce(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)Bn(o,!1);else{if(te!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=ki(e),s!==null){for(t.flags|=128,Bn(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return $(q,q.current&1|2),t.child}e=e.sibling}o.tail!==null&&J()>Cn&&(t.flags|=128,r=!0,Bn(o,!1),t.lanes=4194304)}else{if(!r)if(e=ki(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Bn(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return ce(t),null}else 2*J()-o.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,Bn(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=J(),t.sibling=null,n=q.current,$(q,r?n&1|2:n&1),t):(ce(t),null);case 22:case 23:return al(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Se&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(v(156,t.tag))}function Np(e,t){switch(Ws(t),t.tag){case 1:return ke(t.type)&&hi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Sn(),W(xe),W(de),Zs(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Js(t),null;case 13:if(W(q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(v(340));kn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return W(q),null;case 4:return Sn(),null;case 10:return Ks(t.type._context),null;case 22:case 23:return al(),null;case 24:return null;default:return null}}var Br=!1,fe=!1,Tp=typeof WeakSet=="function"?WeakSet:Set,C=null;function fn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){G(e,t,r)}else n.current=null}function us(e,t,n){try{n()}catch(r){G(e,t,r)}}var ku=!1;function Rp(e,t){if(Qo=ci,e=Fa(),$s(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,u=-1,c=0,y=0,m=e,h=null;t:for(;;){for(var x;m!==n||i!==0&&m.nodeType!==3||(l=s+i),m!==o||r!==0&&m.nodeType!==3||(u=s+r),m.nodeType===3&&(s+=m.nodeValue.length),(x=m.firstChild)!==null;)h=m,m=x;for(;;){if(m===e)break t;if(h===n&&++c===i&&(l=s),h===o&&++y===r&&(u=s),(x=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=x}n=l===-1||u===-1?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(qo={focusedElem:e,selectionRange:n},ci=!1,C=t;C!==null;)if(t=C,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,C=e;else for(;C!==null;){t=C;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var w=k.memoizedProps,F=k.memoizedState,f=t.stateNode,a=f.getSnapshotBeforeUpdate(t.elementType===t.type?w:Be(t.type,w),F);f.__reactInternalSnapshotBeforeUpdate=a}break;case 3:var d=t.stateNode.containerInfo;d.nodeType===1?d.textContent="":d.nodeType===9&&d.documentElement&&d.removeChild(d.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(v(163))}}catch(g){G(t,t.return,g)}if(e=t.sibling,e!==null){e.return=t.return,C=e;break}C=t.return}return k=ku,ku=!1,k}function Zn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&us(t,n,o)}i=i.next}while(i!==r)}}function Bi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function zc(e){var t=e.alternate;t!==null&&(e.alternate=null,zc(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Xe],delete t[fr],delete t[Xo],delete t[cp],delete t[fp])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Dc(e){return e.tag===5||e.tag===3||e.tag===4}function _u(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Dc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function cs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=pi));else if(r!==4&&(e=e.child,e!==null))for(cs(e,t,n),e=e.sibling;e!==null;)cs(e,t,n),e=e.sibling}function fs(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fs(e,t,n),e=e.sibling;e!==null;)fs(e,t,n),e=e.sibling}var oe=null,Fe=!1;function pt(e,t,n){for(n=n.child;n!==null;)Mc(e,t,n),n=n.sibling}function Mc(e,t,n){if(Ge&&typeof Ge.onCommitFiberUnmount=="function")try{Ge.onCommitFiberUnmount(ji,n)}catch{}switch(n.tag){case 5:fe||fn(n,t);case 6:var r=oe,i=Fe;oe=null,pt(e,t,n),oe=r,Fe=i,oe!==null&&(Fe?(e=oe,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):oe.removeChild(n.stateNode));break;case 18:oe!==null&&(Fe?(e=oe,n=n.stateNode,e.nodeType===8?co(e.parentNode,n):e.nodeType===1&&co(e,n),sr(e)):co(oe,n.stateNode));break;case 4:r=oe,i=Fe,oe=n.stateNode.containerInfo,Fe=!0,pt(e,t,n),oe=r,Fe=i;break;case 0:case 11:case 14:case 15:if(!fe&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&us(n,t,s),i=i.next}while(i!==r)}pt(e,t,n);break;case 1:if(!fe&&(fn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){G(n,t,l)}pt(e,t,n);break;case 21:pt(e,t,n);break;case 22:n.mode&1?(fe=(r=fe)||n.memoizedState!==null,pt(e,t,n),fe=r):pt(e,t,n);break;default:pt(e,t,n)}}function Su(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Tp),t.forEach(function(r){var i=Mp.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Me(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:oe=l.stateNode,Fe=!1;break e;case 3:oe=l.stateNode.containerInfo,Fe=!0;break e;case 4:oe=l.stateNode.containerInfo,Fe=!0;break e}l=l.return}if(oe===null)throw Error(v(160));Mc(o,s,i),oe=null,Fe=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(c){G(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Bc(t,e),t=t.sibling}function Bc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Me(t,e),Ke(e),r&4){try{Zn(3,e,e.return),Bi(3,e)}catch(w){G(e,e.return,w)}try{Zn(5,e,e.return)}catch(w){G(e,e.return,w)}}break;case 1:Me(t,e),Ke(e),r&512&&n!==null&&fn(n,n.return);break;case 5:if(Me(t,e),Ke(e),r&512&&n!==null&&fn(n,n.return),e.flags&32){var i=e.stateNode;try{nr(i,"")}catch(w){G(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&sa(i,o),zo(l,s);var c=zo(l,o);for(s=0;s<u.length;s+=2){var y=u[s],m=u[s+1];y==="style"?fa(i,m):y==="dangerouslySetInnerHTML"?aa(i,m):y==="children"?nr(i,m):Rs(i,y,m,c)}switch(l){case"input":jo(i,o);break;case"textarea":la(i,o);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var x=o.value;x!=null?pn(i,!!o.multiple,x,!1):h!==!!o.multiple&&(o.defaultValue!=null?pn(i,!!o.multiple,o.defaultValue,!0):pn(i,!!o.multiple,o.multiple?[]:"",!1))}i[fr]=o}catch(w){G(e,e.return,w)}}break;case 6:if(Me(t,e),Ke(e),r&4){if(e.stateNode===null)throw Error(v(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(w){G(e,e.return,w)}}break;case 3:if(Me(t,e),Ke(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{sr(t.containerInfo)}catch(w){G(e,e.return,w)}break;case 4:Me(t,e),Ke(e);break;case 13:Me(t,e),Ke(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(ll=J())),r&4&&Su(e);break;case 22:if(y=n!==null&&n.memoizedState!==null,e.mode&1?(fe=(c=fe)||y,Me(t,e),fe=c):Me(t,e),Ke(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!y&&e.mode&1)for(C=e,y=e.child;y!==null;){for(m=C=y;C!==null;){switch(h=C,x=h.child,h.tag){case 0:case 11:case 14:case 15:Zn(4,h,h.return);break;case 1:fn(h,h.return);var k=h.stateNode;if(typeof k.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(w){G(r,n,w)}}break;case 5:fn(h,h.return);break;case 22:if(h.memoizedState!==null){Cu(m);continue}}x!==null?(x.return=h,C=x):Cu(m)}y=y.sibling}e:for(y=null,m=e;;){if(m.tag===5){if(y===null){y=m;try{i=m.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=m.stateNode,u=m.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,l.style.display=ca("display",s))}catch(w){G(e,e.return,w)}}}else if(m.tag===6){if(y===null)try{m.stateNode.nodeValue=c?"":m.memoizedProps}catch(w){G(e,e.return,w)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;y===m&&(y=null),m=m.return}y===m&&(y=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Me(t,e),Ke(e),r&4&&Su(e);break;case 21:break;default:Me(t,e),Ke(e)}}function Ke(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Dc(n)){var r=n;break e}n=n.return}throw Error(v(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(nr(i,""),r.flags&=-33);var o=_u(e);fs(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=_u(e);cs(e,l,s);break;default:throw Error(v(161))}}catch(u){G(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Pp(e,t,n){C=e,Fc(e)}function Fc(e,t,n){for(var r=(e.mode&1)!==0;C!==null;){var i=C,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||Br;if(!s){var l=i.alternate,u=l!==null&&l.memoizedState!==null||fe;l=Br;var c=fe;if(Br=s,(fe=u)&&!c)for(C=i;C!==null;)s=C,u=s.child,s.tag===22&&s.memoizedState!==null?Nu(i):u!==null?(u.return=s,C=u):Nu(i);for(;o!==null;)C=o,Fc(o),o=o.sibling;C=i,Br=l,fe=c}Eu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,C=o):Eu(e)}}function Eu(e){for(;C!==null;){var t=C;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:fe||Bi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!fe)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Be(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&uu(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}uu(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var y=c.memoizedState;if(y!==null){var m=y.dehydrated;m!==null&&sr(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(v(163))}fe||t.flags&512&&as(t)}catch(h){G(t,t.return,h)}}if(t===e){C=null;break}if(n=t.sibling,n!==null){n.return=t.return,C=n;break}C=t.return}}function Cu(e){for(;C!==null;){var t=C;if(t===e){C=null;break}var n=t.sibling;if(n!==null){n.return=t.return,C=n;break}C=t.return}}function Nu(e){for(;C!==null;){var t=C;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Bi(4,t)}catch(u){G(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){G(t,i,u)}}var o=t.return;try{as(t)}catch(u){G(t,o,u)}break;case 5:var s=t.return;try{as(t)}catch(u){G(t,s,u)}}}catch(u){G(t,t.return,u)}if(t===e){C=null;break}var l=t.sibling;if(l!==null){l.return=t.return,C=l;break}C=t.return}}var jp=Math.ceil,Ei=ct.ReactCurrentDispatcher,ol=ct.ReactCurrentOwner,Ae=ct.ReactCurrentBatchConfig,B=0,ie=null,Z=null,se=0,Se=0,dn=Ot(0),te=0,gr=null,Kt=0,Fi=0,sl=0,bn=null,ve=null,ll=0,Cn=1/0,tt=null,Ci=!1,ds=null,Ct=null,Fr=!1,wt=null,Ni=0,er=0,ps=null,Zr=-1,br=0;function he(){return B&6?J():Zr!==-1?Zr:Zr=J()}function Nt(e){return e.mode&1?B&2&&se!==0?se&-se:pp.transition!==null?(br===0&&(br=Sa()),br):(e=U,e!==0||(e=window.event,e=e===void 0?16:ja(e.type)),e):1}function We(e,t,n,r){if(50<er)throw er=0,ps=null,Error(v(185));wr(e,n,r),(!(B&2)||e!==ie)&&(e===ie&&(!(B&2)&&(Fi|=n),te===4&&gt(e,se)),_e(e,r),n===1&&B===0&&!(t.mode&1)&&(Cn=J()+500,zi&&At()))}function _e(e,t){var n=e.callbackNode;pd(e,t);var r=ai(e,e===ie?se:0);if(r===0)n!==null&&zl(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&zl(n),t===1)e.tag===0?dp(Tu.bind(null,e)):Xa(Tu.bind(null,e)),up(function(){!(B&6)&&At()}),n=null;else{switch(Ea(r)){case 1:n=As;break;case 4:n=ka;break;case 16:n=ui;break;case 536870912:n=_a;break;default:n=ui}n=Kc(n,Uc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Uc(e,t){if(Zr=-1,br=0,B&6)throw Error(v(327));var n=e.callbackNode;if(vn()&&e.callbackNode!==n)return null;var r=ai(e,e===ie?se:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ti(e,r);else{t=r;var i=B;B|=2;var o=Vc();(ie!==e||se!==t)&&(tt=null,Cn=J()+500,Vt(e,t));do try{Ap();break}catch(l){$c(e,l)}while(1);qs(),Ei.current=o,B=i,Z!==null?t=0:(ie=null,se=0,t=te)}if(t!==0){if(t===2&&(i=Uo(e),i!==0&&(r=i,t=hs(e,i))),t===1)throw n=gr,Vt(e,0),gt(e,r),_e(e,J()),n;if(t===6)gt(e,r);else{if(i=e.current.alternate,!(r&30)&&!Lp(i)&&(t=Ti(e,r),t===2&&(o=Uo(e),o!==0&&(r=o,t=hs(e,o))),t===1))throw n=gr,Vt(e,0),gt(e,r),_e(e,J()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(v(345));case 2:Bt(e,ve,tt);break;case 3:if(gt(e,r),(r&130023424)===r&&(t=ll+500-J(),10<t)){if(ai(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){he(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Yo(Bt.bind(null,e,ve,tt),t);break}Bt(e,ve,tt);break;case 4:if(gt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ve(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=J()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*jp(r/1960))-r,10<r){e.timeoutHandle=Yo(Bt.bind(null,e,ve,tt),r);break}Bt(e,ve,tt);break;case 5:Bt(e,ve,tt);break;default:throw Error(v(329))}}}return _e(e,J()),e.callbackNode===n?Uc.bind(null,e):null}function hs(e,t){var n=bn;return e.current.memoizedState.isDehydrated&&(Vt(e,t).flags|=256),e=Ti(e,t),e!==2&&(t=ve,ve=n,t!==null&&ms(t)),e}function ms(e){ve===null?ve=e:ve.push.apply(ve,e)}function Lp(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!He(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gt(e,t){for(t&=~sl,t&=~Fi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ve(t),r=1<<n;e[n]=-1,t&=~r}}function Tu(e){if(B&6)throw Error(v(327));vn();var t=ai(e,0);if(!(t&1))return _e(e,J()),null;var n=Ti(e,t);if(e.tag!==0&&n===2){var r=Uo(e);r!==0&&(t=r,n=hs(e,r))}if(n===1)throw n=gr,Vt(e,0),gt(e,t),_e(e,J()),n;if(n===6)throw Error(v(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bt(e,ve,tt),_e(e,J()),null}function ul(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(Cn=J()+500,zi&&At())}}function Yt(e){wt!==null&&wt.tag===0&&!(B&6)&&vn();var t=B;B|=1;var n=Ae.transition,r=U;try{if(Ae.transition=null,U=1,e)return e()}finally{U=r,Ae.transition=n,B=t,!(B&6)&&At()}}function al(){Se=dn.current,W(dn)}function Vt(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,lp(n)),Z!==null)for(n=Z.return;n!==null;){var r=n;switch(Ws(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&hi();break;case 3:Sn(),W(xe),W(de),Zs();break;case 5:Js(r);break;case 4:Sn();break;case 13:W(q);break;case 19:W(q);break;case 10:Ks(r.type._context);break;case 22:case 23:al()}n=n.return}if(ie=e,Z=e=Tt(e.current,null),se=Se=t,te=0,gr=null,sl=Fi=Kt=0,ve=bn=null,Ut!==null){for(t=0;t<Ut.length;t++)if(n=Ut[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}Ut=null}return e}function $c(e,t){do{var n=Z;try{if(qs(),Xr.current=Si,_i){for(var r=K.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}_i=!1}if(qt=0,re=ee=K=null,Jn=!1,hr=0,ol.current=null,n===null||n.return===null){te=1,gr=t,Z=null;break}e:{var o=e,s=n.return,l=n,u=t;if(t=se,l.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,y=l,m=y.tag;if(!(y.mode&1)&&(m===0||m===11||m===15)){var h=y.alternate;h?(y.updateQueue=h.updateQueue,y.memoizedState=h.memoizedState,y.lanes=h.lanes):(y.updateQueue=null,y.memoizedState=null)}var x=hu(s);if(x!==null){x.flags&=-257,mu(x,s,l,o,t),x.mode&1&&pu(o,c,t),t=x,u=c;var k=t.updateQueue;if(k===null){var w=new Set;w.add(u),t.updateQueue=w}else k.add(u);break e}else{if(!(t&1)){pu(o,c,t),cl();break e}u=Error(v(426))}}else if(H&&l.mode&1){var F=hu(s);if(F!==null){!(F.flags&65536)&&(F.flags|=256),mu(F,s,l,o,t),Hs(En(u,l));break e}}o=u=En(u,l),te!==4&&(te=2),bn===null?bn=[o]:bn.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var f=Ec(o,u,t);lu(o,f);break e;case 1:l=u;var a=o.type,d=o.stateNode;if(!(o.flags&128)&&(typeof a.getDerivedStateFromError=="function"||d!==null&&typeof d.componentDidCatch=="function"&&(Ct===null||!Ct.has(d)))){o.flags|=65536,t&=-t,o.lanes|=t;var g=Cc(o,l,t);lu(o,g);break e}}o=o.return}while(o!==null)}Hc(n)}catch(_){t=_,Z===n&&n!==null&&(Z=n=n.return);continue}break}while(1)}function Vc(){var e=Ei.current;return Ei.current=Si,e===null?Si:e}function cl(){(te===0||te===3||te===2)&&(te=4),ie===null||!(Kt&268435455)&&!(Fi&268435455)||gt(ie,se)}function Ti(e,t){var n=B;B|=2;var r=Vc();(ie!==e||se!==t)&&(tt=null,Vt(e,t));do try{Op();break}catch(i){$c(e,i)}while(1);if(qs(),B=n,Ei.current=r,Z!==null)throw Error(v(261));return ie=null,se=0,te}function Op(){for(;Z!==null;)Wc(Z)}function Ap(){for(;Z!==null&&!id();)Wc(Z)}function Wc(e){var t=qc(e.alternate,e,Se);e.memoizedProps=e.pendingProps,t===null?Hc(e):Z=t,ol.current=null}function Hc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Np(n,t),n!==null){n.flags&=32767,Z=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{te=6,Z=null;return}}else if(n=Cp(n,t,Se),n!==null){Z=n;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);te===0&&(te=5)}function Bt(e,t,n){var r=U,i=Ae.transition;try{Ae.transition=null,U=1,Ip(e,t,n,r)}finally{Ae.transition=i,U=r}return null}function Ip(e,t,n,r){do vn();while(wt!==null);if(B&6)throw Error(v(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(v(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(hd(e,o),e===ie&&(Z=ie=null,se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Fr||(Fr=!0,Kc(ui,function(){return vn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ae.transition,Ae.transition=null;var s=U;U=1;var l=B;B|=4,ol.current=null,Rp(e,n),Bc(n,e),ep(qo),ci=!!Qo,qo=Qo=null,e.current=n,Pp(n),od(),B=l,U=s,Ae.transition=o}else e.current=n;if(Fr&&(Fr=!1,wt=e,Ni=i),o=e.pendingLanes,o===0&&(Ct=null),ud(n.stateNode),_e(e,J()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Ci)throw Ci=!1,e=ds,ds=null,e;return Ni&1&&e.tag!==0&&vn(),o=e.pendingLanes,o&1?e===ps?er++:(er=0,ps=e):er=0,At(),null}function vn(){if(wt!==null){var e=Ea(Ni),t=Ae.transition,n=U;try{if(Ae.transition=null,U=16>e?16:e,wt===null)var r=!1;else{if(e=wt,wt=null,Ni=0,B&6)throw Error(v(331));var i=B;for(B|=4,C=e.current;C!==null;){var o=C,s=o.child;if(C.flags&16){var l=o.deletions;if(l!==null){for(var u=0;u<l.length;u++){var c=l[u];for(C=c;C!==null;){var y=C;switch(y.tag){case 0:case 11:case 15:Zn(8,y,o)}var m=y.child;if(m!==null)m.return=y,C=m;else for(;C!==null;){y=C;var h=y.sibling,x=y.return;if(zc(y),y===c){C=null;break}if(h!==null){h.return=x,C=h;break}C=x}}}var k=o.alternate;if(k!==null){var w=k.child;if(w!==null){k.child=null;do{var F=w.sibling;w.sibling=null,w=F}while(w!==null)}}C=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,C=s;else e:for(;C!==null;){if(o=C,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Zn(9,o,o.return)}var f=o.sibling;if(f!==null){f.return=o.return,C=f;break e}C=o.return}}var a=e.current;for(C=a;C!==null;){s=C;var d=s.child;if(s.subtreeFlags&2064&&d!==null)d.return=s,C=d;else e:for(s=a;C!==null;){if(l=C,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Bi(9,l)}}catch(_){G(l,l.return,_)}if(l===s){C=null;break e}var g=l.sibling;if(g!==null){g.return=l.return,C=g;break e}C=l.return}}if(B=i,At(),Ge&&typeof Ge.onPostCommitFiberRoot=="function")try{Ge.onPostCommitFiberRoot(ji,e)}catch{}r=!0}return r}finally{U=n,Ae.transition=t}}return!1}function Ru(e,t,n){t=En(n,t),t=Ec(e,t,1),e=Et(e,t,1),t=he(),e!==null&&(wr(e,1,t),_e(e,t))}function G(e,t,n){if(e.tag===3)Ru(e,e,n);else for(;t!==null;){if(t.tag===3){Ru(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ct===null||!Ct.has(r))){e=En(n,e),e=Cc(t,e,1),t=Et(t,e,1),e=he(),t!==null&&(wr(t,1,e),_e(t,e));break}}t=t.return}}function zp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=he(),e.pingedLanes|=e.suspendedLanes&n,ie===e&&(se&n)===n&&(te===4||te===3&&(se&130023424)===se&&500>J()-ll?Vt(e,0):sl|=n),_e(e,t)}function Qc(e,t){t===0&&(e.mode&1?(t=Pr,Pr<<=1,!(Pr&130023424)&&(Pr=4194304)):t=1);var n=he();e=ut(e,t),e!==null&&(wr(e,t,n),_e(e,n))}function Dp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qc(e,n)}function Mp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(v(314))}r!==null&&r.delete(t),Qc(e,n)}var qc;qc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||xe.current)we=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return we=!1,Ep(e,t,n);we=!!(e.flags&131072)}else we=!1,H&&t.flags&1048576&&Ga(t,gi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Jr(e,t),e=t.pendingProps;var i=xn(t,de.current);gn(t,n),i=el(null,t,r,e,i,n);var o=tl();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,ke(r)?(o=!0,mi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,Xs(t),i.updater=Mi,t.stateNode=i,i._reactInternals=t,ts(t,r,e,n),t=is(null,t,r,!0,o,n)):(t.tag=0,H&&o&&Vs(t),pe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Jr(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=Fp(r),e=Be(r,e),i){case 0:t=rs(null,t,r,e,n);break e;case 1:t=vu(null,t,r,e,n);break e;case 11:t=yu(null,t,r,e,n);break e;case 14:t=gu(null,t,r,Be(r.type,e),n);break e}throw Error(v(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),rs(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),vu(e,t,r,i,n);case 3:e:{if(Pc(t),e===null)throw Error(v(387));r=t.pendingProps,o=t.memoizedState,i=o.element,nc(e,t),xi(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=En(Error(v(423)),t),t=wu(e,t,r,n,i);break e}else if(r!==i){i=En(Error(v(424)),t),t=wu(e,t,r,n,i);break e}else for(Ee=St(t.stateNode.containerInfo.firstChild),Ce=t,H=!0,$e=null,n=ec(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(kn(),r===i){t=at(e,t,n);break e}pe(e,t,r,n)}t=t.child}return t;case 5:return rc(t),e===null&&Zo(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Ko(r,i)?s=null:o!==null&&Ko(r,o)&&(t.flags|=32),Rc(e,t),pe(e,t,s,n),t.child;case 6:return e===null&&Zo(t),null;case 13:return jc(e,t,n);case 4:return Gs(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=_n(t,null,r,n):pe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),yu(e,t,r,i,n);case 7:return pe(e,t,t.pendingProps,n),t.child;case 8:return pe(e,t,t.pendingProps.children,n),t.child;case 12:return pe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,$(vi,r._currentValue),r._currentValue=s,o!==null)if(He(o.value,s)){if(o.children===i.children&&!xe.current){t=at(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var u=l.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=ot(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var y=c.pending;y===null?u.next=u:(u.next=y.next,y.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),bo(o.return,n,t),l.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(v(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),bo(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}pe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,gn(t,n),i=Ie(i),r=r(i),t.flags|=1,pe(e,t,r,n),t.child;case 14:return r=t.type,i=Be(r,t.pendingProps),i=Be(r.type,i),gu(e,t,r,i,n);case 15:return Nc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Be(r,i),Jr(e,t),t.tag=1,ke(r)?(e=!0,mi(t)):e=!1,gn(t,n),Sc(t,r,i),ts(t,r,i,n),is(null,t,r,!0,e,n);case 19:return Lc(e,t,n);case 22:return Tc(e,t,n)}throw Error(v(156,t.tag))};function Kc(e,t){return xa(e,t)}function Bp(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Oe(e,t,n,r){return new Bp(e,t,n,r)}function fl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Fp(e){if(typeof e=="function")return fl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===js)return 11;if(e===Ls)return 14}return 2}function Tt(e,t){var n=e.alternate;return n===null?(n=Oe(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ei(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")fl(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case tn:return Wt(n.children,i,o,t);case Ps:s=8,i|=8;break;case Co:return e=Oe(12,n,t,i|2),e.elementType=Co,e.lanes=o,e;case No:return e=Oe(13,n,t,i),e.elementType=No,e.lanes=o,e;case To:return e=Oe(19,n,t,i),e.elementType=To,e.lanes=o,e;case ra:return Ui(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ta:s=10;break e;case na:s=9;break e;case js:s=11;break e;case Ls:s=14;break e;case ht:s=16,r=null;break e}throw Error(v(130,e==null?e:typeof e,""))}return t=Oe(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function Wt(e,t,n,r){return e=Oe(7,e,r,t),e.lanes=n,e}function Ui(e,t,n,r){return e=Oe(22,e,r,t),e.elementType=ra,e.lanes=n,e.stateNode={isHidden:!1},e}function wo(e,t,n){return e=Oe(6,e,null,t),e.lanes=n,e}function xo(e,t,n){return t=Oe(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Up(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=bi(0),this.expirationTimes=bi(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=bi(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function dl(e,t,n,r,i,o,s,l,u){return e=new Up(e,t,n,l,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Oe(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Xs(o),e}function $p(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:en,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Yc(e){if(!e)return jt;e=e._reactInternals;e:{if(Gt(e)!==e||e.tag!==1)throw Error(v(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(ke(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(v(171))}if(e.tag===1){var n=e.type;if(ke(n))return Ya(e,n,t)}return t}function Xc(e,t,n,r,i,o,s,l,u){return e=dl(n,r,!0,e,i,o,s,l,u),e.context=Yc(null),n=e.current,r=he(),i=Nt(n),o=ot(r,i),o.callback=t??null,Et(n,o,i),e.current.lanes=i,wr(e,i,r),_e(e,r),e}function $i(e,t,n,r){var i=t.current,o=he(),s=Nt(i);return n=Yc(n),t.context===null?t.context=n:t.pendingContext=n,t=ot(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Et(i,t,s),e!==null&&(We(e,i,s,o),Yr(e,i,s)),s}function Ri(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Pu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function pl(e,t){Pu(e,t),(e=e.alternate)&&Pu(e,t)}function Vp(){return null}var Gc=typeof reportError=="function"?reportError:function(e){console.error(e)};function hl(e){this._internalRoot=e}Vi.prototype.render=hl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(v(409));$i(e,t,null,null)};Vi.prototype.unmount=hl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Yt(function(){$i(null,e,null,null)}),t[lt]=null}};function Vi(e){this._internalRoot=e}Vi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ta();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yt.length&&t!==0&&t<yt[n].priority;n++);yt.splice(n,0,e),n===0&&Pa(e)}};function ml(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Wi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function ju(){}function Wp(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=Ri(s);o.call(c)}}var s=Xc(t,r,e,0,null,!1,!1,"",ju);return e._reactRootContainer=s,e[lt]=s.current,ar(e.nodeType===8?e.parentNode:e),Yt(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var c=Ri(u);l.call(c)}}var u=dl(e,0,!1,null,null,!1,!1,"",ju);return e._reactRootContainer=u,e[lt]=u.current,ar(e.nodeType===8?e.parentNode:e),Yt(function(){$i(t,u,n,r)}),u}function Hi(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var u=Ri(s);l.call(u)}}$i(t,s,e,i)}else s=Wp(n,t,e,i,r);return Ri(s)}Ca=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Wn(t.pendingLanes);n!==0&&(Is(t,n|1),_e(t,J()),!(B&6)&&(Cn=J()+500,At()))}break;case 13:Yt(function(){var r=ut(e,1);if(r!==null){var i=he();We(r,e,1,i)}}),pl(e,1)}};zs=function(e){if(e.tag===13){var t=ut(e,134217728);if(t!==null){var n=he();We(t,e,134217728,n)}pl(e,134217728)}};Na=function(e){if(e.tag===13){var t=Nt(e),n=ut(e,t);if(n!==null){var r=he();We(n,e,t,r)}pl(e,t)}};Ta=function(){return U};Ra=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Mo=function(e,t,n){switch(t){case"input":if(jo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Ii(r);if(!i)throw Error(v(90));oa(r),jo(r,i)}}}break;case"textarea":la(e,n);break;case"select":t=n.value,t!=null&&pn(e,!!n.multiple,t,!1)}};ha=ul;ma=Yt;var Hp={usingClientEntryPoint:!1,Events:[kr,sn,Ii,da,pa,ul]},Fn={findFiberByHostInstance:Ft,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Qp={bundleType:Fn.bundleType,version:Fn.version,rendererPackageName:Fn.rendererPackageName,rendererConfig:Fn.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:ct.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=va(e),e===null?null:e.stateNode},findFiberByHostInstance:Fn.findFiberByHostInstance||Vp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ur=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ur.isDisabled&&Ur.supportsFiber)try{ji=Ur.inject(Qp),Ge=Ur}catch{}}Te.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hp;Te.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ml(t))throw Error(v(200));return $p(e,t,null,n)};Te.createRoot=function(e,t){if(!ml(e))throw Error(v(299));var n=!1,r="",i=Gc;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=dl(e,1,!1,null,null,n,!1,r,i),e[lt]=t.current,ar(e.nodeType===8?e.parentNode:e),new hl(t)};Te.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(v(188)):(e=Object.keys(e).join(","),Error(v(268,e)));return e=va(t),e=e===null?null:e.stateNode,e};Te.flushSync=function(e){return Yt(e)};Te.hydrate=function(e,t,n){if(!Wi(t))throw Error(v(200));return Hi(null,e,t,!0,n)};Te.hydrateRoot=function(e,t,n){if(!ml(e))throw Error(v(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=Gc;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Xc(t,null,e,1,n??null,i,!1,o,s),e[lt]=t.current,ar(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Vi(t)};Te.render=function(e,t,n){if(!Wi(t))throw Error(v(200));return Hi(null,e,t,!1,n)};Te.unmountComponentAtNode=function(e){if(!Wi(e))throw Error(v(40));return e._reactRootContainer?(Yt(function(){Hi(null,null,e,!1,function(){e._reactRootContainer=null,e[lt]=null})}),!0):!1};Te.unstable_batchedUpdates=ul;Te.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Wi(n))throw Error(v(200));if(e==null||e._reactInternals===void 0)throw Error(v(38));return Hi(e,t,n,!1,r)};Te.version="18.3.1-next-f1338f8080-20240426";function Jc(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Jc)}catch(e){console.error(e)}}Jc(),Ju.exports=Te;var qp=Ju.exports,Zc,Lu=qp;Zc=Lu.createRoot,Lu.hydrateRoot;const be=Object.create(null);be.open="0";be.close="1";be.ping="2";be.pong="3";be.message="4";be.upgrade="5";be.noop="6";const ti=Object.create(null);Object.keys(be).forEach(e=>{ti[be[e]]=e});const ys={type:"error",data:"parser error"},bc=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",ef=typeof ArrayBuffer=="function",tf=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,yl=({type:e,data:t},n,r)=>bc&&t instanceof Blob?n?r(t):Ou(t,r):ef&&(t instanceof ArrayBuffer||tf(t))?n?r(t):Ou(new Blob([t]),r):r(be[e]+(t||"")),Ou=(e,t)=>{const n=new FileReader;return n.onload=function(){const r=n.result.split(",")[1];t("b"+(r||""))},n.readAsDataURL(e)};function Au(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let ko;function Kp(e,t){if(bc&&e.data instanceof Blob)return e.data.arrayBuffer().then(Au).then(t);if(ef&&(e.data instanceof ArrayBuffer||tf(e.data)))return t(Au(e.data));yl(e,!1,n=>{ko||(ko=new TextEncoder),t(ko.encode(n))})}const Iu="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qn=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let e=0;e<Iu.length;e++)Qn[Iu.charCodeAt(e)]=e;const Yp=e=>{let t=e.length*.75,n=e.length,r,i=0,o,s,l,u;e[e.length-1]==="="&&(t--,e[e.length-2]==="="&&t--);const c=new ArrayBuffer(t),y=new Uint8Array(c);for(r=0;r<n;r+=4)o=Qn[e.charCodeAt(r)],s=Qn[e.charCodeAt(r+1)],l=Qn[e.charCodeAt(r+2)],u=Qn[e.charCodeAt(r+3)],y[i++]=o<<2|s>>4,y[i++]=(s&15)<<4|l>>2,y[i++]=(l&3)<<6|u&63;return c},Xp=typeof ArrayBuffer=="function",gl=(e,t)=>{if(typeof e!="string")return{type:"message",data:nf(e,t)};const n=e.charAt(0);return n==="b"?{type:"message",data:Gp(e.substring(1),t)}:ti[n]?e.length>1?{type:ti[n],data:e.substring(1)}:{type:ti[n]}:ys},Gp=(e,t)=>{if(Xp){const n=Yp(e);return nf(n,t)}else return{base64:!0,data:e}},nf=(e,t)=>{switch(t){case"blob":return e instanceof Blob?e:new Blob([e]);case"arraybuffer":default:return e instanceof ArrayBuffer?e:e.buffer}},rf=String.fromCharCode(30),Jp=(e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach((o,s)=>{yl(o,!1,l=>{r[s]=l,++i===n&&t(r.join(rf))})})},Zp=(e,t)=>{const n=e.split(rf),r=[];for(let i=0;i<n.length;i++){const o=gl(n[i],t);if(r.push(o),o.type==="error")break}return r};function bp(){return new TransformStream({transform(e,t){Kp(e,n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const o=new DataView(i.buffer);o.setUint8(0,126),o.setUint16(1,r)}else{i=new Uint8Array(9);const o=new DataView(i.buffer);o.setUint8(0,127),o.setBigUint64(1,BigInt(r))}e.data&&typeof e.data!="string"&&(i[0]|=128),t.enqueue(i),t.enqueue(n)})}})}let _o;function $r(e){return e.reduce((t,n)=>t+n.length,0)}function Vr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function eh(e,t){_o||(_o=new TextDecoder);const n=[];let r=0,i=-1,o=!1;return new TransformStream({transform(s,l){for(n.push(s);;){if(r===0){if($r(n)<1)break;const u=Vr(n,1);o=(u[0]&128)===128,i=u[0]&127,i<126?r=3:i===126?r=1:r=2}else if(r===1){if($r(n)<2)break;const u=Vr(n,2);i=new DataView(u.buffer,u.byteOffset,u.length).getUint16(0),r=3}else if(r===2){if($r(n)<8)break;const u=Vr(n,8),c=new DataView(u.buffer,u.byteOffset,u.length),y=c.getUint32(0);if(y>Math.pow(2,53-32)-1){l.enqueue(ys);break}i=y*Math.pow(2,32)+c.getUint32(4),r=3}else{if($r(n)<i)break;const u=Vr(n,i);l.enqueue(gl(o?u:_o.decode(u),t)),r=0}if(i===0||i>e){l.enqueue(ys);break}}}})}const of=4;function b(e){if(e)return th(e)}function th(e){for(var t in b.prototype)e[t]=b.prototype[t];return e}b.prototype.on=b.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this};b.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this};b.prototype.off=b.prototype.removeListener=b.prototype.removeAllListeners=b.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var n=this._callbacks["$"+e];if(!n)return this;if(arguments.length==1)return delete this._callbacks["$"+e],this;for(var r,i=0;i<n.length;i++)if(r=n[i],r===t||r.fn===t){n.splice(i,1);break}return n.length===0&&delete this._callbacks["$"+e],this};b.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){n=n.slice(0);for(var r=0,i=n.length;r<i;++r)n[r].apply(this,t)}return this};b.prototype.emitReserved=b.prototype.emit;b.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]};b.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Qi=(()=>typeof Promise=="function"&&typeof Promise.resolve=="function"?t=>Promise.resolve().then(t):(t,n)=>n(t,0))(),Le=(()=>typeof self<"u"?self:typeof window<"u"?window:Function("return this")())(),nh="arraybuffer";function sf(e,...t){return t.reduce((n,r)=>(e.hasOwnProperty(r)&&(n[r]=e[r]),n),{})}const rh=Le.setTimeout,ih=Le.clearTimeout;function qi(e,t){t.useNativeTimers?(e.setTimeoutFn=rh.bind(Le),e.clearTimeoutFn=ih.bind(Le)):(e.setTimeoutFn=Le.setTimeout.bind(Le),e.clearTimeoutFn=Le.clearTimeout.bind(Le))}const oh=1.33;function sh(e){return typeof e=="string"?lh(e):Math.ceil((e.byteLength||e.size)*oh)}function lh(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}function lf(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function uh(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}function ah(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let o=n[r].split("=");t[decodeURIComponent(o[0])]=decodeURIComponent(o[1])}return t}class ch extends Error{constructor(t,n,r){super(t),this.description=n,this.context=r,this.type="TransportError"}}class vl extends b{constructor(t){super(),this.writable=!1,qi(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,n,r){return super.emitReserved("error",new ch(t,n,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(t){this.readyState==="open"&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){const n=gl(t,this.socket.binaryType);this.onPacket(n)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,n={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(n)}_hostname(){const t=this.opts.hostname;return t.indexOf(":")===-1?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(t){const n=uh(t);return n.length?"?"+n:""}}class fh extends vl{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";const n=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||n()})),this.writable||(r++,this.once("drain",function(){--r||n()}))}else n()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){const n=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};Zp(t,this.socket.binaryType).forEach(n),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const t=()=>{this.write([{type:"close"}])};this.readyState==="open"?t():this.once("open",t)}write(t){this.writable=!1,Jp(t,n=>{this.doWrite(n,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const t=this.opts.secure?"https":"http",n=this.query||{};return this.opts.timestampRequests!==!1&&(n[this.opts.timestampParam]=lf()),!this.supportsBinary&&!n.sid&&(n.b64=1),this.createUri(t,n)}}let uf=!1;try{uf=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const dh=uf;function ph(){}class hh extends fh{constructor(t){if(super(t),typeof location<"u"){const n=location.protocol==="https:";let r=location.port;r||(r=n?"443":"80"),this.xd=typeof location<"u"&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,n){const r=this.request({method:"POST",data:t});r.on("success",n),r.on("error",(i,o)=>{this.onError("xhr post error",i,o)})}doPoll(){const t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(n,r)=>{this.onError("xhr poll error",n,r)}),this.pollXhr=t}}class Ze extends b{constructor(t,n,r){super(),this.createRequest=t,qi(this,r),this._opts=r,this._method=r.method||"GET",this._uri=n,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var t;const n=sf(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let i in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(i)&&r.setRequestHeader(i,this._opts.extraHeaders[i])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(t=this._opts.cookieJar)===null||t===void 0||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var i;r.readyState===3&&((i=this._opts.cookieJar)===null||i===void 0||i.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(i){this.setTimeoutFn(()=>{this._onError(i)},0);return}typeof document<"u"&&(this._index=Ze.requestsCount++,Ze.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=ph,t)try{this._xhr.abort()}catch{}typeof document<"u"&&delete Ze.requests[this._index],this._xhr=null}}_onLoad(){const t=this._xhr.responseText;t!==null&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}Ze.requestsCount=0;Ze.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",zu);else if(typeof addEventListener=="function"){const e="onpagehide"in Le?"pagehide":"unload";addEventListener(e,zu,!1)}}function zu(){for(let e in Ze.requests)Ze.requests.hasOwnProperty(e)&&Ze.requests[e].abort()}const mh=function(){const e=af({xdomain:!1});return e&&e.responseType!==null}();class yh extends hh{constructor(t){super(t);const n=t&&t.forceBase64;this.supportsBinary=mh&&!n}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new Ze(af,this.uri(),t)}}function af(e){const t=e.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!t||dh))return new XMLHttpRequest}catch{}if(!t)try{return new Le[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const cf=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class gh extends vl{get name(){return"websocket"}doOpen(){const t=this.uri(),n=this.opts.protocols,r=cf?{}:sf(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,n,r)}catch(i){return this.emitReserved("error",i)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;yl(r,this.supportsBinary,o=>{try{this.doWrite(r,o)}catch{}i&&Qi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const t=this.opts.secure?"wss":"ws",n=this.query||{};return this.opts.timestampRequests&&(n[this.opts.timestampParam]=lf()),this.supportsBinary||(n.b64=1),this.createUri(t,n)}}const So=Le.WebSocket||Le.MozWebSocket;class vh extends gh{createSocket(t,n,r){return cf?new So(t,n,r):n?new So(t,n):new So(t)}doWrite(t,n){this.ws.send(n)}}class wh extends vl{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{const n=eh(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(n).getReader(),i=bp();i.readable.pipeTo(t.writable),this._writer=i.writable.getWriter();const o=()=>{r.read().then(({done:l,value:u})=>{l||(this.onPacket(u),o())}).catch(l=>{})};o();const s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let n=0;n<t.length;n++){const r=t[n],i=n===t.length-1;this._writer.write(r).then(()=>{i&&Qi(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;(t=this._transport)===null||t===void 0||t.close()}}const xh={websocket:vh,webtransport:wh,polling:yh},kh=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,_h=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function gs(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");n!=-1&&r!=-1&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=kh.exec(e||""),o={},s=14;for(;s--;)o[_h[s]]=i[s]||"";return n!=-1&&r!=-1&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=Sh(o,o.path),o.queryKey=Eh(o,o.query),o}function Sh(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");return(t.slice(0,1)=="/"||t.length===0)&&r.splice(0,1),t.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Eh(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,i,o){i&&(n[i]=o)}),n}const vs=typeof addEventListener=="function"&&typeof removeEventListener=="function",ni=[];vs&&addEventListener("offline",()=>{ni.forEach(e=>e())},!1);class Rt extends b{constructor(t,n){if(super(),this.binaryType=nh,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&typeof t=="object"&&(n=t,t=null),t){const r=gs(t);n.hostname=r.host,n.secure=r.protocol==="https"||r.protocol==="wss",n.port=r.port,r.query&&(n.query=r.query)}else n.host&&(n.hostname=gs(n.host).host);qi(this,n),this.secure=n.secure!=null?n.secure:typeof location<"u"&&location.protocol==="https:",n.hostname&&!n.port&&(n.port=this.secure?"443":"80"),this.hostname=n.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=n.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},n.transports.forEach(r=>{const i=r.prototype.name;this.transports.push(i),this._transportsByName[i]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},n),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=ah(this.opts.query)),vs&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ni.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){const n=Object.assign({},this.opts.query);n.EIO=of,n.transport=t,this.id&&(n.sid=this.id);const r=Object.assign({},this.opts,{query:n,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const t=this.opts.rememberUpgrade&&Rt.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const n=this.createTransport(t);n.open(),this.setTransport(n)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",n=>this._onClose("transport close",n))}onOpen(){this.readyState="open",Rt.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(t){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const n=new Error("server error");n.code=t.data,this._onError(n);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data);break}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let n=1;for(let r=0;r<this.writeBuffer.length;r++){const i=this.writeBuffer[r].data;if(i&&(n+=sh(i)),r>0&&n>this._maxPayload)return this.writeBuffer.slice(0,r);n+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,Qi(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,n,r){return this._sendPacket("message",t,n,r),this}send(t,n,r){return this._sendPacket("message",t,n,r),this}_sendPacket(t,n,r,i){if(typeof n=="function"&&(i=n,n=void 0),typeof r=="function"&&(i=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const o={type:t,data:n,options:r};this.emitReserved("packetCreate",o),this.writeBuffer.push(o),i&&this.once("flush",i),this.flush()}close(){const t=()=>{this._onClose("forced close"),this.transport.close()},n=()=>{this.off("upgrade",n),this.off("upgradeError",n),t()},r=()=>{this.once("upgrade",n),this.once("upgradeError",n)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(Rt.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,n){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),vs&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=ni.indexOf(this._offlineEventListener);r!==-1&&ni.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,n),this.writeBuffer=[],this._prevBufferLen=0}}}Rt.protocol=of;class Ch extends Rt{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let n=this.createTransport(t),r=!1;Rt.priorWebsocketSuccess=!1;const i=()=>{r||(n.send([{type:"ping",data:"probe"}]),n.once("packet",m=>{if(!r)if(m.type==="pong"&&m.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",n),!n)return;Rt.priorWebsocketSuccess=n.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(y(),this.setTransport(n),n.send([{type:"upgrade"}]),this.emitReserved("upgrade",n),n=null,this.upgrading=!1,this.flush())})}else{const h=new Error("probe error");h.transport=n.name,this.emitReserved("upgradeError",h)}}))};function o(){r||(r=!0,y(),n.close(),n=null)}const s=m=>{const h=new Error("probe error: "+m);h.transport=n.name,o(),this.emitReserved("upgradeError",h)};function l(){s("transport closed")}function u(){s("socket closed")}function c(m){n&&m.name!==n.name&&o()}const y=()=>{n.removeListener("open",i),n.removeListener("error",s),n.removeListener("close",l),this.off("close",u),this.off("upgrading",c)};n.once("open",i),n.once("error",s),n.once("close",l),this.once("close",u),this.once("upgrading",c),this._upgrades.indexOf("webtransport")!==-1&&t!=="webtransport"?this.setTimeoutFn(()=>{r||n.open()},200):n.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){const n=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&n.push(t[r]);return n}}let Nh=class extends Ch{constructor(t,n={}){const r=typeof t=="object"?t:n;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(i=>xh[i]).filter(i=>!!i)),super(t,r)}};function Th(e,t="",n){let r=e;n=n||typeof location<"u"&&location,e==null&&(e=n.protocol+"//"+n.host),typeof e=="string"&&(e.charAt(0)==="/"&&(e.charAt(1)==="/"?e=n.protocol+e:e=n.host+e),/^(https?|wss?):\/\//.test(e)||(typeof n<"u"?e=n.protocol+"//"+e:e="https://"+e),r=gs(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const o=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+o+":"+r.port+t,r.href=r.protocol+"://"+o+(n&&n.port===r.port?"":":"+r.port),r}const Rh=typeof ArrayBuffer=="function",Ph=e=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,ff=Object.prototype.toString,jh=typeof Blob=="function"||typeof Blob<"u"&&ff.call(Blob)==="[object BlobConstructor]",Lh=typeof File=="function"||typeof File<"u"&&ff.call(File)==="[object FileConstructor]";function wl(e){return Rh&&(e instanceof ArrayBuffer||Ph(e))||jh&&e instanceof Blob||Lh&&e instanceof File}function ri(e,t){if(!e||typeof e!="object")return!1;if(Array.isArray(e)){for(let n=0,r=e.length;n<r;n++)if(ri(e[n]))return!0;return!1}if(wl(e))return!0;if(e.toJSON&&typeof e.toJSON=="function"&&arguments.length===1)return ri(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&ri(e[n]))return!0;return!1}function Oh(e){const t=[],n=e.data,r=e;return r.data=ws(n,t),r.attachments=t.length,{packet:r,buffers:t}}function ws(e,t){if(!e)return e;if(wl(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}else if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=ws(e[r],t);return n}else if(typeof e=="object"&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=ws(e[r],t));return n}return e}function Ah(e,t){return e.data=xs(e.data,t),delete e.attachments,e}function xs(e,t){if(!e)return e;if(e&&e._placeholder===!0){if(typeof e.num=="number"&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}else if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=xs(e[n],t);else if(typeof e=="object")for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=xs(e[n],t));return e}const Ih=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],zh=5;var M;(function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"})(M||(M={}));class Dh{constructor(t){this.replacer=t}encode(t){return(t.type===M.EVENT||t.type===M.ACK)&&ri(t)?this.encodeAsBinary({type:t.type===M.EVENT?M.BINARY_EVENT:M.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let n=""+t.type;return(t.type===M.BINARY_EVENT||t.type===M.BINARY_ACK)&&(n+=t.attachments+"-"),t.nsp&&t.nsp!=="/"&&(n+=t.nsp+","),t.id!=null&&(n+=t.id),t.data!=null&&(n+=JSON.stringify(t.data,this.replacer)),n}encodeAsBinary(t){const n=Oh(t),r=this.encodeAsString(n.packet),i=n.buffers;return i.unshift(r),i}}function Du(e){return Object.prototype.toString.call(e)==="[object Object]"}class xl extends b{constructor(t){super(),this.reviver=t}add(t){let n;if(typeof t=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");n=this.decodeString(t);const r=n.type===M.BINARY_EVENT;r||n.type===M.BINARY_ACK?(n.type=r?M.EVENT:M.ACK,this.reconstructor=new Mh(n),n.attachments===0&&super.emitReserved("decoded",n)):super.emitReserved("decoded",n)}else if(wl(t)||t.base64)if(this.reconstructor)n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,super.emitReserved("decoded",n));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+t)}decodeString(t){let n=0;const r={type:Number(t.charAt(0))};if(M[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===M.BINARY_EVENT||r.type===M.BINARY_ACK){const o=n+1;for(;t.charAt(++n)!=="-"&&n!=t.length;);const s=t.substring(o,n);if(s!=Number(s)||t.charAt(n)!=="-")throw new Error("Illegal attachments");r.attachments=Number(s)}if(t.charAt(n+1)==="/"){const o=n+1;for(;++n&&!(t.charAt(n)===","||n===t.length););r.nsp=t.substring(o,n)}else r.nsp="/";const i=t.charAt(n+1);if(i!==""&&Number(i)==i){const o=n+1;for(;++n;){const s=t.charAt(n);if(s==null||Number(s)!=s){--n;break}if(n===t.length)break}r.id=Number(t.substring(o,n+1))}if(t.charAt(++n)){const o=this.tryParse(t.substr(n));if(xl.isPayloadValid(r.type,o))r.data=o;else throw new Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch{return!1}}static isPayloadValid(t,n){switch(t){case M.CONNECT:return Du(n);case M.DISCONNECT:return n===void 0;case M.CONNECT_ERROR:return typeof n=="string"||Du(n);case M.EVENT:case M.BINARY_EVENT:return Array.isArray(n)&&(typeof n[0]=="number"||typeof n[0]=="string"&&Ih.indexOf(n[0])===-1);case M.ACK:case M.BINARY_ACK:return Array.isArray(n)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Mh{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){const n=Ah(this.reconPack,this.buffers);return this.finishedReconstruction(),n}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Bh=Object.freeze(Object.defineProperty({__proto__:null,Decoder:xl,Encoder:Dh,get PacketType(){return M},protocol:zh},Symbol.toStringTag,{value:"Module"}));function Ue(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Fh=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class df extends b{constructor(t,n,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=n,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const t=this.io;this.subs=[Ue(t,"open",this.onopen.bind(this)),Ue(t,"packet",this.onpacket.bind(this)),Ue(t,"error",this.onerror.bind(this)),Ue(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...n){var r,i,o;if(Fh.hasOwnProperty(t))throw new Error('"'+t.toString()+'" is a reserved event name');if(n.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(n),this;const s={type:M.EVENT,data:n};if(s.options={},s.options.compress=this.flags.compress!==!1,typeof n[n.length-1]=="function"){const y=this.ids++,m=n.pop();this._registerAckCallback(y,m),s.id=y}const l=(i=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||i===void 0?void 0:i.writable,u=this.connected&&!(!((o=this.io.engine)===null||o===void 0)&&o._hasPingExpired());return this.flags.volatile&&!l||(u?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(t,n){var r;const i=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(i===void 0){this.acks[t]=n;return}const o=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let l=0;l<this.sendBuffer.length;l++)this.sendBuffer[l].id===t&&this.sendBuffer.splice(l,1);n.call(this,new Error("operation has timed out"))},i),s=(...l)=>{this.io.clearTimeoutFn(o),n.apply(this,l)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...n){return new Promise((r,i)=>{const o=(s,l)=>s?i(s):r(l);o.withError=!0,n.push(o),this.emit(t,...n)})}_addToQueue(t){let n;typeof t[t.length-1]=="function"&&(n=t.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((i,...o)=>r!==this._queue[0]?void 0:(i!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),n&&n(i)):(this._queue.shift(),n&&n(null,...o)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||this._queue.length===0)return;const n=this._queue[0];n.pending&&!t||(n.pending=!0,n.tryCount++,this.flags=n.flags,this.emit.apply(this,n.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){typeof this.auth=="function"?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:M.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,n){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,n),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(r=>String(r.id)===t)){const r=this.acks[t];delete this.acks[t],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(t){if(t.nsp===this.nsp)switch(t.type){case M.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case M.EVENT:case M.BINARY_EVENT:this.onevent(t);break;case M.ACK:case M.BINARY_ACK:this.onack(t);break;case M.DISCONNECT:this.ondisconnect();break;case M.CONNECT_ERROR:this.destroy();const r=new Error(t.data.message);r.data=t.data.data,this.emitReserved("connect_error",r);break}}onevent(t){const n=t.data||[];t.id!=null&&n.push(this.ack(t.id)),this.connected?this.emitEvent(n):this.receiveBuffer.push(Object.freeze(n))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length){const n=this._anyListeners.slice();for(const r of n)r.apply(this,t)}super.emit.apply(this,t),this._pid&&t.length&&typeof t[t.length-1]=="string"&&(this._lastOffset=t[t.length-1])}ack(t){const n=this;let r=!1;return function(...i){r||(r=!0,n.packet({type:M.ACK,id:t,data:i}))}}onack(t){const n=this.acks[t.id];typeof n=="function"&&(delete this.acks[t.id],n.withError&&t.data.unshift(null),n.apply(this,t.data))}onconnect(t,n){this.id=t,this.recovered=n&&this._pid===n,this._pid=n,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:M.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){const n=this._anyListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){const n=this._anyOutgoingListeners;for(let r=0;r<n.length;r++)if(t===n[r])return n.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const n=this._anyOutgoingListeners.slice();for(const r of n)r.apply(this,t.data)}}}function Pn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Pn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=Math.floor(t*10)&1?e+n:e-n}return Math.min(e,this.max)|0};Pn.prototype.reset=function(){this.attempts=0};Pn.prototype.setMin=function(e){this.ms=e};Pn.prototype.setMax=function(e){this.max=e};Pn.prototype.setJitter=function(e){this.jitter=e};class ks extends b{constructor(t,n){var r;super(),this.nsps={},this.subs=[],t&&typeof t=="object"&&(n=t,t=void 0),n=n||{},n.path=n.path||"/socket.io",this.opts=n,qi(this,n),this.reconnection(n.reconnection!==!1),this.reconnectionAttempts(n.reconnectionAttempts||1/0),this.reconnectionDelay(n.reconnectionDelay||1e3),this.reconnectionDelayMax(n.reconnectionDelayMax||5e3),this.randomizationFactor((r=n.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new Pn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(n.timeout==null?2e4:n.timeout),this._readyState="closed",this.uri=t;const i=n.parser||Bh;this.encoder=new i.Encoder,this.decoder=new i.Decoder,this._autoConnect=n.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return t===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var n;return t===void 0?this._reconnectionDelay:(this._reconnectionDelay=t,(n=this.backoff)===null||n===void 0||n.setMin(t),this)}randomizationFactor(t){var n;return t===void 0?this._randomizationFactor:(this._randomizationFactor=t,(n=this.backoff)===null||n===void 0||n.setJitter(t),this)}reconnectionDelayMax(t){var n;return t===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,(n=this.backoff)===null||n===void 0||n.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new Nh(this.uri,this.opts);const n=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const i=Ue(n,"open",function(){r.onopen(),t&&t()}),o=l=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",l),t?t(l):this.maybeReconnectOnOpen()},s=Ue(n,"error",o);if(this._timeout!==!1){const l=this._timeout,u=this.setTimeoutFn(()=>{i(),o(new Error("timeout")),n.close()},l);this.opts.autoUnref&&u.unref(),this.subs.push(()=>{this.clearTimeoutFn(u)})}return this.subs.push(i),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const t=this.engine;this.subs.push(Ue(t,"ping",this.onping.bind(this)),Ue(t,"data",this.ondata.bind(this)),Ue(t,"error",this.onerror.bind(this)),Ue(t,"close",this.onclose.bind(this)),Ue(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(n){this.onclose("parse error",n)}}ondecoded(t){Qi(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,n){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new df(this,t,n),this.nsps[t]=r),r}_destroy(t){const n=Object.keys(this.nsps);for(const r of n)if(this.nsps[r].active)return;this._close()}_packet(t){const n=this.encoder.encode(t);for(let r=0;r<n.length;r++)this.engine.write(n[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,n){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,n),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const n=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{t.skipReconnect||(this.emitReserved("reconnect_attempt",t.backoff.attempts),!t.skipReconnect&&t.open(i=>{i?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",i)):t.onreconnect()}))},n);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}const Un={};function ii(e,t){typeof e=="object"&&(t=e,e=void 0),t=t||{};const n=Th(e,t.path||"/socket.io"),r=n.source,i=n.id,o=n.path,s=Un[i]&&o in Un[i].nsps,l=t.forceNew||t["force new connection"]||t.multiplex===!1||s;let u;return l?u=new ks(r,t):(Un[i]||(Un[i]=new ks(r,t)),u=Un[i]),n.query&&!t.query&&(t.query=n.queryKey),u.socket(n.path,t)}Object.assign(ii,{Manager:ks,Socket:df,io:ii,connect:ii});const Uh=({state:e,isConnected:t})=>{const n=()=>{if(!t)return"avatar-disconnected";switch(e){case"listening":return"avatar-listening";case"speaking":return"avatar-speaking";case"thinking":return"avatar-thinking";default:return"avatar-idle"}};return p.jsxs("div",{className:`avatar-container ${n()}`,children:[p.jsx("div",{className:"avatar-circle",children:p.jsxs("div",{className:"avatar-inner",children:[p.jsx("div",{className:"avatar-ring ring-1"}),p.jsx("div",{className:"avatar-ring ring-2"}),p.jsx("div",{className:"avatar-ring ring-3"}),p.jsxs("div",{className:"avatar-icon",children:[e==="listening"&&p.jsx("svg",{className:"w-12 h-12 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z",clipRule:"evenodd"})}),e==="speaking"&&p.jsx("svg",{className:"w-12 h-12 text-green-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.814L4.846 13.5H3a1 1 0 01-1-1v-5a1 1 0 011-1h1.846l3.537-3.314a1 1 0 011.617.814zM15 8a2 2 0 012 2v0a2 2 0 01-2 2 1 1 0 01-1-1V9a1 1 0 011-1z",clipRule:"evenodd"})}),e==="thinking"&&p.jsx("svg",{className:"w-12 h-12 text-yellow-400 animate-spin",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"})}),e==="idle"&&p.jsx("svg",{className:"w-12 h-12 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z",clipRule:"evenodd"})})]})]})}),p.jsx("div",{className:"avatar-status",children:p.jsx("span",{className:"text-sm font-medium text-white/80",children:t?e==="listening"?"Listening...":e==="speaking"?"Speaking...":e==="thinking"?"Thinking...":"Ready":"Disconnected"})})]})},$h=({messages:e,isLoading:t})=>{const n=z.useRef(null),r=()=>{var o;(o=n.current)==null||o.scrollIntoView({behavior:"smooth"})};z.useEffect(()=>{r()},[e]);const i=o=>new Date(o).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return p.jsx("div",{className:"flex flex-col h-full",children:p.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4 min-h-0",children:[e.length===0?p.jsx("div",{className:"flex items-center justify-center h-full",children:p.jsxs("div",{className:"text-center text-white/60",children:[p.jsx("svg",{className:"w-12 h-12 mx-auto mb-4 opacity-50",fill:"currentColor",viewBox:"0 0 20 20",children:p.jsx("path",{fillRule:"evenodd",d:"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z",clipRule:"evenodd"})}),p.jsx("p",{className:"text-sm",children:"Start a conversation"}),p.jsx("p",{className:"text-xs mt-1",children:"Type a message or use voice input"})]})}):e.map(o=>p.jsx("div",{className:`flex ${o.message_type==="user"?"justify-end":"justify-start"}`,children:p.jsxs("div",{className:`max-w-[80%] rounded-2xl px-4 py-3 ${o.message_type==="user"?"bg-blue-600 text-white":o.message_type==="assistant"?"bg-white/10 text-white border border-white/20":"bg-yellow-600/20 text-yellow-200 border border-yellow-600/30"}`,children:[p.jsx("div",{className:"text-sm leading-relaxed",children:o.content}),p.jsxs("div",{className:`text-xs mt-2 opacity-70 ${o.message_type==="user"?"text-blue-100":"text-white/60"}`,children:[i(o.created_at),o.audio_url&&p.jsx("span",{className:"ml-2",children:"🔊"})]})]})},o.id)),t&&p.jsx("div",{className:"flex justify-start",children:p.jsx("div",{className:"bg-white/10 text-white border border-white/20 rounded-2xl px-4 py-3",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsxs("div",{className:"flex space-x-1",children:[p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce"}),p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),p.jsx("div",{className:"w-2 h-2 bg-white/60 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),p.jsx("span",{className:"text-sm text-white/80",children:"AI is thinking..."})]})})}),p.jsx("div",{ref:n})]})})};var Vh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Wh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Hh=(e,t)=>{const n=z.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:o=2,absoluteStrokeWidth:s,children:l,...u},c)=>z.createElement("svg",{ref:c,...Vh,width:i,height:i,stroke:r,strokeWidth:s?Number(o)*24/Number(i):o,className:`lucide lucide-${Wh(e)}`,...u},[...t.map(([y,m])=>z.createElement(y,m)),...(Array.isArray(l)?l:[l])||[]]));return n.displayName=`${e}`,n};var It=Hh;const Qh=It("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),qh=It("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]),Kh=It("Move",[["polyline",{points:"5 9 2 12 5 15",key:"1r5uj5"}],["polyline",{points:"9 5 12 2 15 5",key:"5v383o"}],["polyline",{points:"15 19 12 22 9 19",key:"g7qi8m"}],["polyline",{points:"19 9 22 12 19 15",key:"tpp73q"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}]]),Yh=It("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),Mu=It("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Xh=It("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),Gh=It("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]),Bu=It("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Jh=({isExpanded:e,onToggleExpanded:t,isMuted:n,onToggleMute:r,onRefreshChat:i,chatExpanded:o,onToggleChatExpanded:s,userName:l,onUserNameChange:u,systemActive:c,onToggleSystemActive:y,wakeWords:m=[],onAddWakeWord:h,onDeleteWakeWord:x,onToggleWakeWord:k})=>{const[w,F]=z.useState({x:20,y:20}),[f,a]=z.useState(!1),[d,g]=z.useState({x:0,y:0}),[_,S]=z.useState(""),T=z.useRef(null),P=j=>{if(!T.current)return;const L=T.current.getBoundingClientRect();g({x:j.clientX-L.left,y:j.clientY-L.top}),a(!0)};return z.useEffect(()=>{const j=ue=>{if(!f)return;const Qe=ue.clientX-d.x,et=ue.clientY-d.y,ft=window.innerWidth-400,jn=window.innerHeight-600;F({x:Math.max(0,Math.min(Qe,ft)),y:Math.max(0,Math.min(et,jn))})},L=()=>{a(!1)};return f&&(document.addEventListener("mousemove",j),document.addEventListener("mouseup",L)),()=>{document.removeEventListener("mousemove",j),document.removeEventListener("mouseup",L)}},[f,d]),e?p.jsxs("div",{ref:T,className:"fixed z-50 bg-black/80 backdrop-blur-md border border-white/20 rounded-xl w-96 max-h-[80vh] overflow-y-auto",style:{left:w.x,top:w.y},children:[p.jsxs("div",{className:"flex items-center justify-between p-6 pb-4 cursor-move",onMouseDown:P,children:[p.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center space-x-2",children:[p.jsx(Kh,{className:"w-4 h-4 text-white/60"}),p.jsx(Mu,{className:"w-5 h-5"}),p.jsx("span",{children:"Control Centre"})]}),p.jsx("button",{onClick:t,className:"text-white/60 hover:text-white transition-colors",children:p.jsx(Bu,{className:"w-5 h-5"})})]}),p.jsxs("div",{className:"px-6 pb-6",children:[p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"User Preferences"}),p.jsxs("div",{className:"space-y-3",children:[p.jsxs("div",{children:[p.jsx("label",{className:"text-xs text-white/60 mb-1 block",children:"What should I call you?"}),p.jsx("input",{type:"text",value:l,onChange:j=>u(j.target.value),placeholder:"Enter your preferred name...",className:"w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400"})]}),p.jsxs("div",{children:[p.jsx("label",{className:"text-xs text-white/60 mb-2 block",children:"Voice System: OpenAI Realtime API"}),p.jsx("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4",children:p.jsx("p",{className:"text-blue-300 text-sm",children:"✅ No wake words needed! Just start talking and the AI will respond in real-time."})})]}),p.jsxs("div",{className:"hidden",children:[p.jsx("label",{className:"text-xs text-white/60 mb-2 block",children:"Wake Word Management (Disabled)"}),p.jsxs("div",{className:"flex space-x-2 mb-3",children:[p.jsx("input",{type:"text",value:_,onChange:j=>S(j.target.value),placeholder:"Enter new wake word...",className:"flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400",onKeyPress:j=>{j.key==="Enter"&&_.trim()&&h&&(h(_.trim()),S(""))}}),p.jsx("button",{onClick:()=>{_.trim()&&h&&(h(_.trim()),S(""))},disabled:!_.trim(),className:"px-3 py-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-sm",children:"Add"})]}),p.jsxs("div",{className:"space-y-2 max-h-32 overflow-y-auto",children:[m.map(j=>p.jsxs("div",{className:"flex items-center justify-between p-2 bg-white/5 rounded-lg",children:[p.jsxs("div",{className:"flex items-center space-x-2 flex-1",children:[p.jsx("button",{onClick:()=>k==null?void 0:k(j.id),className:`w-4 h-4 rounded border-2 flex items-center justify-center ${j.enabled?"bg-green-500 border-green-500":"border-white/30"}`,children:j.enabled&&p.jsx("span",{className:"text-white text-xs",children:"✓"})}),p.jsx("span",{className:`text-sm ${j.enabled?"text-white":"text-white/60"}`,children:j.keyword})]}),p.jsx("button",{onClick:()=>x==null?void 0:x(j.id),className:"text-red-400 hover:text-red-300 p-1",children:p.jsx(Bu,{className:"w-3 h-3"})})]},j.id)),m.length===0&&p.jsx("div",{className:"text-center text-white/60 text-sm py-2",children:"No wake words configured"})]})]})]})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"System Control"}),p.jsxs("button",{onClick:y,className:`w-full flex items-center justify-center space-x-2 p-4 rounded-lg transition-all font-medium ${c?"bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30":"bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30"}`,children:[p.jsx("div",{className:`w-3 h-3 rounded-full ${c?"bg-green-400 animate-pulse":"bg-red-400"}`}),p.jsx("span",{children:c?"System Active - Click to Pause":"System Paused - Click to Resume"})]}),p.jsx("p",{className:"text-xs text-white/60 mt-2 text-center",children:c?"AI is listening and ready to respond":"AI is paused - no processing or responses"})]}),p.jsxs("div",{className:"mb-6",children:[p.jsx("h3",{className:"text-sm font-medium text-white/80 mb-3",children:"Chat Controls"}),p.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[p.jsxs("button",{onClick:r,className:`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all ${n?"bg-red-500/20 border border-red-500/30 text-red-400":"bg-purple-500/20 border border-purple-500/30 text-purple-400"}`,children:[n?p.jsx(Gh,{className:"w-4 h-4"}):p.jsx(Xh,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:n?"Unmute":"Mute"})]}),p.jsxs("button",{onClick:i,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-blue-500/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 transition-all",children:[p.jsx(Yh,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:"Refresh"})]}),p.jsxs("button",{onClick:s,className:"flex items-center justify-center space-x-2 p-3 rounded-lg bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 transition-all col-span-2",children:[o?p.jsx(qh,{className:"w-4 h-4"}):p.jsx(Qh,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:o?"Normal Size":"Expand Chat"})]})]})]}),p.jsxs("div",{className:"text-xs text-white/50 leading-relaxed",children:[p.jsxs("div",{className:"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3",children:[p.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[p.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),p.jsx("span",{className:"text-blue-300 font-medium text-xs",children:"OpenWakeWord System"})]}),p.jsx("p",{className:"text-xs text-white/70",children:"Now using advanced AI-powered wake word detection with no API keys required."})]}),p.jsxs("div",{className:"space-y-2",children:[p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Wake Words:"})," Say any enabled wake word to start voice recording."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Auto-Stop:"})," Recording stops automatically after 1.5 seconds of silence."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Sensitivity:"})," Higher values detect wake words more easily but may increase false positives."]}),p.jsxs("p",{children:[p.jsx("strong",{className:"text-white/70",children:"Available Models:"}),' "hey jarvis", "alexa", "hey mycroft", "hey rhasspy"']})]})]})]})]}):p.jsx("div",{className:"fixed z-50 cursor-move",style:{left:w.x,top:w.y},onMouseDown:P,children:p.jsx("button",{onClick:t,className:"bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 text-white/80 hover:bg-black/30 transition-all duration-300",children:p.jsxs("div",{className:"flex items-center space-x-2",children:[p.jsx(Mu,{className:"w-4 h-4"}),p.jsx("span",{className:"text-sm",children:"Control Centre"})]})})})};class Fu{constructor(t){Ki(this,"config");Ki(this,"sessionPath");this.config=t,this.sessionPath=`projects/${t.projectId}/agent/sessions/${t.sessionId}`}async detectIntent(t){var n,r;try{const i=await fetch("/api/dialogflow/detect-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({sessionPath:this.sessionPath,queryInput:{text:{text:t,languageCode:this.config.languageCode}}})});if(!i.ok)throw new Error(`DialogFlow API error: ${i.statusText}`);const o=await i.json();return{queryText:o.queryResult.queryText,fulfillmentText:o.queryResult.fulfillmentText,intent:{name:((n=o.queryResult.intent)==null?void 0:n.name)||"",displayName:((r=o.queryResult.intent)==null?void 0:r.displayName)||"",confidence:o.queryResult.intentDetectionConfidence||0},parameters:o.queryResult.parameters||{},contexts:o.queryResult.outputContexts||[],action:o.queryResult.action||""}}catch(i){return console.error("DialogFlow detection error:",i),null}}async canHandleQuickly(t){try{const n=await this.detectIntent(t);return n?n.intent.confidence>.8&&n.fulfillmentText.length>0&&this.isQuickResponseIntent(n.intent.displayName):!1}catch(n){return console.error("DialogFlow quick check error:",n),!1}}isQuickResponseIntent(t){return["Default Welcome Intent","Default Fallback Intent","greeting","goodbye","thanks","help","what_can_you_do","how_are_you","tell_joke","weather","time","date","small_talk","encouragement","support","compliment","motivation"].some(r=>t.toLowerCase().includes(r.toLowerCase()))}async getContextualResponse(t,n=[]){try{const r=this.addConversationContext(t,n);return await this.detectIntent(r)}catch(r){return console.error("DialogFlow contextual response error:",r),null}}addConversationContext(t,n){return n.length===0?t:`Context: ${n.slice(-3).join(" ")}. Current query: ${t}`}static generateSessionId(){return`session-${Date.now()}-${Math.random().toString(36).substr(2,9)}`}updateSession(t){console.log("Updated session contexts:",t)}needsAIEnhancement(t){return t.intent.confidence<.7||t.fulfillmentText.length<10||t.action==="input.unknown"||t.intent.displayName.includes("Fallback")}getSuggestedFollowUps(t){const n={greeting:["How are you feeling today?","What would you like to talk about?","Is there anything I can help you with?"],support:["Would you like to talk more about that?","How can I best support you right now?","What would make you feel better?"],help:["What specific area would you like help with?","Can you tell me more about what you need?","Would you like some suggestions?"],motivation:["What goals are you working towards?","What challenges are you facing?","How can I encourage you today?"]},r=Object.keys(n).find(i=>t.intent.displayName.toLowerCase().includes(i));return r?n[r]:["Is there anything else I can help with?","How are you feeling about that?","Would you like to explore this topic more?"]}}const Zh=()=>{const[e,t]=z.useState(null),[n,r]=z.useState(!1),[i,o]=z.useState(null),[s,l]=z.useState([]),[u,c]=z.useState(!1),[y,m]=z.useState(!1),[h,x]=z.useState(!1),[k,w]=z.useState("idle"),[F,f]=z.useState(!1),[a,d]=z.useState(""),[g,_]=z.useState("hey assistant"),[S,T]=z.useState(!0),[P,j]=z.useState([]),[L,ue]=z.useState(!1),[Qe,et]=z.useState(!1),[ft,jn]=z.useState(!1),ge=z.useRef(null),dt=z.useRef(null);z.useEffect(()=>{const N=ii("http://localhost:5002",{transports:["websocket"]});return N.on("connect",()=>{console.log("🔗 Connected to server"),r(!0),console.log("🎤 Starting OpenAI Realtime conversation..."),N.emit("start_realtime_conversation",{conversation_id:null})}),N.on("disconnect",()=>{console.log("🔌 Disconnected from server"),r(!1)}),N.on("realtime_connected",R=>{console.log("✅ OpenAI Realtime API connected:",R),r(!0)}),N.on("ai_greeting",R=>{console.log("🎤 AI Greeting:",R.message);const A={id:Date.now(),content:R.message,message_type:"assistant",created_at:new Date().toISOString()};l(ne=>[...ne,A]),w("speaking")}),N.on("transcript_received",R=>{console.log("📝 Transcript received:",R.transcript);const A={id:R.message_id,content:R.transcript,message_type:"user",created_at:new Date().toISOString()};l(ne=>[...ne,A])}),N.on("response_complete",()=>{console.log("✅ Response completed"),w("idle")}),N.on("message_received",R=>{l(A=>[...A,R.message]),R.type==="assistant"?w("speaking"):R.type}),N.on("voice_response",R=>{if(!h&&ge.current){const A=new Blob([Uint8Array.from(atob(R.audio_data),Ln=>Ln.charCodeAt(0))],{type:"audio/mpeg"}),ne=URL.createObjectURL(A);ge.current.src=ne,ge.current.play(),m(!0)}}),N.on("realtime_connected",R=>{console.log("✅ OpenAI Realtime API connected:",R),r(!0)}),N.on("ai_greeting",R=>{console.log("🎤 AI Greeting:",R.message);const A={id:Date.now(),content:R.message,message_type:"assistant",created_at:new Date().toISOString()};l(ne=>[...ne,A]),w("speaking")}),N.on("transcript_received",R=>{console.log("📝 Transcript received:",R.transcript);const A={id:R.message_id,content:R.transcript,message_type:"user",created_at:new Date().toISOString()};l(ne=>[...ne,A])}),N.on("response_complete",()=>{console.log("✅ Response completed"),w("idle")}),N.on("voice_transcribed",R=>{console.log("🎯 Voice transcribed:",R.text)}),N.on("command_executed",R=>{console.log("Command executed:",R)}),N.on("error",R=>{console.error("Socket error:",R)}),t(N),()=>{N.close()}},[]),z.useEffect(()=>{e&&n&&E()},[e,n]),z.useEffect(()=>{!e||!n||!i||(console.log("🔧 OpenAI Realtime API is active - no wake words needed!"),ue(!1),j([]),I(),console.log("✅ OpenAI Realtime API ready - just start talking!"))},[e,n,i]),z.useEffect(()=>{(()=>{try{const R={}.REACT_APP_GOOGLE_CLOUD_PROJECT_ID;if(R){const A=new Fu({projectId:R,sessionId:Fu.generateSessionId(),languageCode:"en-US"});dt.current=A,console.log("DialogFlow service initialized")}else console.log("DialogFlow not configured - missing project ID")}catch(R){console.log("DialogFlow initialization failed:",R)}})()},[]);const E=async()=>{if(!i)try{const R=await(await fetch("http://localhost:5002/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:"AI Voice Chat",user_id:1})})).json();R.success&&(o(R.conversation),e==null||e.emit("join_conversation",{conversation_id:R.conversation.id,user_id:1}))}catch(N){console.error("Error creating conversation:",N)}},O=async(N,R=!1)=>{if(!e||!i||!N.trim())return;if(!S){console.log("System is paused - message sending disabled");return}f(!0),w("thinking");let A=null;if(dt.current)try{if(await dt.current.canHandleQuickly(N)&&(A=await dt.current.detectIntent(N),A&&A.fulfillmentText)){const Ln={id:Date.now(),content:N,message_type:"user",created_at:new Date().toISOString()};l(Dt=>[...Dt,Ln]);const kl={id:Date.now()+1,content:A.fulfillmentText,message_type:"assistant",created_at:new Date().toISOString()};l(Dt=>[...Dt,kl]),R&&!h&&e.emit("generate_voice_only",{text:A.fulfillmentText,conversation_id:i.id}),f(!1),w("idle");return}}catch(ne){console.log("DialogFlow not available, falling back to AI:",ne)}e.emit("send_message",{conversation_id:i.id,content:N,message_type:"user",generate_voice:R,dialogflow_context:A?{intent:A.intent,parameters:A.parameters,contexts:A.contexts}:null}),f(!1)},I=async()=>{if(!(!e||!i))try{console.log("🎤 Starting continuous voice stream for OpenAI Realtime API...");const N=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:16e3,channelCount:1,echoCancellation:!0,noiseSuppression:!0}}),R=new AudioContext({sampleRate:16e3}),A=R.createMediaStreamSource(N),ne=R.createScriptProcessor(4096,1,1);ne.onaudioprocess=Ln=>{if(!S)return;const Dt=Ln.inputBuffer.getChannelData(0),_l=new Int16Array(Dt.length);for(let Sr=0;Sr<Dt.length;Sr++)_l[Sr]=Math.max(-32768,Math.min(32767,Dt[Sr]*32768));e&&e.connected&&e.emit("send_audio_stream",{audio_data:Array.from(_l),conversation_id:i.id,format:"pcm16",sample_rate:16e3})},A.connect(ne),ne.connect(R.destination),console.log("✅ Continuous voice stream active - just start talking!")}catch(N){console.error("Error starting continuous voice stream:",N)}},Q=()=>{u&&(console.log("🛑 Stopping voice streaming..."),e&&i&&e.emit("stop_voice_streaming",{conversation_id:i.id}),c(!1),w("idle"),console.log("✅ Voice streaming stopped"))},X=N=>{!e||!i||O(N,!0)},Jt=()=>{m(!1),w("idle"),S&&L&&console.log("🔄 Voice response finished, ready for next wake word...")},qe=()=>{const N=!h;x(N),N&&y&&ge.current?(ge.current.pause(),ge.current.currentTime=0,m(!1),w("idle"),console.log("🔇 Assistant muted and stopped speaking")):N||console.log("🔊 Assistant unmuted")},zt=N=>{localStorage.setItem("wakeWords",JSON.stringify(N)),console.log("Wake words saved to localStorage:",N)},De=async N=>{if(e)try{e.emit("stop_wake_word_detection");const R=N.filter(A=>A.enabled);if(R.length>0){const A=R.map(ne=>ne.keyword);e.emit("start_wake_word_detection",{wake_words:A,threshold:.3}),console.log("Wake word service restarted with:",A)}}catch(R){console.error("Failed to restart wake word service:",R)}},Zt=async N=>{const R={id:`wake-word-${Date.now()}`,keyword:N.toLowerCase(),enabled:!1,sensitivity:.5},A=[...P,R];j(A),zt(A),await De(A),console.log("Added wake word:",N)},pf=async N=>{const R=P.filter(A=>A.id!==N);j(R),zt(R),await De(R),console.log("Deleted wake word:",N)},hf=async N=>{const R=P.map(A=>A.id===N?{...A,enabled:!A.enabled}:A);j(R),zt(R),await De(R),console.log("Toggled wake word:",N)},mf=()=>{l([]),o(null),E()},yf=N=>{d(N),localStorage.setItem("userName",N)},gf=N=>{_(N),localStorage.setItem("preferredWakeWord",N)},vf=()=>{const N=!S;T(N),N?console.log("✅ System reactivated - AI ready to respond"):(console.log("⏸️ System paused - stopping all AI operations"),u&&Q(),y&&ge.current&&(ge.current.pause(),ge.current.currentTime=0,m(!1),w("idle")))};return z.useEffect(()=>{const N=localStorage.getItem("userName"),R=localStorage.getItem("preferredWakeWord");N&&d(N),R&&_(R)},[]),p.jsxs("div",{className:"min-h-screen gradient-bg flex p-6 gap-6",children:[p.jsxs("div",{className:"flex-1 flex flex-col items-center justify-center",children:[p.jsx("div",{className:"floating-panel w-full max-w-2xl mb-8",children:p.jsxs("div",{className:"glass-card",children:[p.jsxs("div",{className:"text-center mb-6",children:[p.jsx("h1",{className:"text-2xl font-bold text-white/90 mb-2",children:"AI Assistant"}),p.jsx("p",{className:"text-white/70 text-sm",children:"Click the microphone to start talking"})]}),p.jsx("div",{className:"flex items-center justify-center h-80 avatar-glow",children:p.jsx(Uh,{state:k,isConnected:n,isPlaying:y})})]})}),p.jsx("div",{className:"glass-panel rounded-3xl p-6 mb-6",children:p.jsxs("div",{className:"text-center",children:[p.jsx("h3",{className:"text-white font-semibold mb-4",children:"🎤 OpenAI Realtime Voice API"}),n?p.jsxs("div",{className:"space-y-4",children:[p.jsxs("div",{className:"inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-lg px-4 py-3",children:[p.jsx("div",{className:"w-3 h-3 bg-green-400 rounded-full animate-pulse"}),p.jsx("span",{className:"text-green-400 font-medium",children:"Continuous voice streaming active"})]}),p.jsx("p",{className:"text-white/70 text-sm",children:"Just start talking - no buttons needed! The AI will respond in real-time."}),p.jsxs("div",{className:"flex items-center justify-center space-x-4 mt-4",children:[p.jsx("button",{onClick:qe,className:`p-3 rounded-full transition-colors ${h?"bg-red-500/20 border border-red-500/30 text-red-400":"bg-blue-500/20 border border-blue-500/30 text-blue-400"}`,children:h?"🔇":"🔊"}),p.jsxs("div",{className:"text-center",children:[p.jsx("div",{className:`w-4 h-4 rounded-full mx-auto mb-1 ${y?"bg-blue-400 animate-pulse":"bg-gray-500"}`}),p.jsx("span",{className:"text-xs text-white/50",children:y?"AI Speaking":"Listening"})]})]})]}):p.jsxs("div",{className:"inline-flex items-center space-x-2 bg-yellow-500/20 border border-yellow-500/30 rounded-lg px-4 py-3",children:[p.jsx("div",{className:"w-3 h-3 bg-yellow-400 rounded-full animate-pulse"}),p.jsx("span",{className:"text-yellow-400 font-medium",children:"Connecting to OpenAI Realtime API..."})]})]})}),p.jsx("div",{className:`connection-indicator ${n?"status-connected":"status-disconnected"}`,children:p.jsxs("div",{className:"flex items-center gap-2",children:[p.jsx("div",{className:`w-2 h-2 rounded-full ${n?"bg-green-400 animate-pulse":"bg-red-400"}`}),p.jsx("span",{className:"text-sm font-medium",children:n?"Connected":"Reconnecting..."})]})})]}),p.jsx("div",{className:`flex flex-col h-screen transition-all duration-300 ${ft?"w-[600px]":"w-96"}`,children:p.jsxs("div",{className:"glass-panel rounded-3xl flex flex-col h-full max-h-screen overflow-hidden",children:[p.jsxs("div",{className:"p-6 border-b border-white/10 flex-shrink-0",children:[p.jsx("h2",{className:"text-xl font-semibold text-white/90",children:"Our Conversation"}),p.jsx("p",{className:"text-white/60 text-sm mt-1",children:"I'm here to listen and support you"})]}),p.jsx("div",{className:"flex-1 overflow-hidden min-h-0",children:p.jsx($h,{messages:s,isLoading:F})}),p.jsxs("div",{className:"p-6 border-t border-white/10 flex-shrink-0",children:[p.jsxs("div",{className:"text-center mb-4",children:[p.jsx("p",{className:"text-white/70 text-sm",children:"🎤 Voice-only conversation mode"}),p.jsx("p",{className:"text-white/50 text-xs mt-1",children:"Use your wake word to start talking"})]}),p.jsxs("div",{className:"flex flex-wrap gap-2",children:[p.jsx("button",{onClick:()=>X("I need some encouragement today"),disabled:F,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Need Encouragement"}),p.jsx("button",{onClick:()=>X("I'm feeling stressed and need support"),disabled:F,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Feeling Stressed"}),p.jsx("button",{onClick:()=>X("Tell me something positive"),disabled:F,className:"glass-button-secondary text-xs py-2 px-3 disabled:opacity-50",children:"Something Positive"})]})]})]})}),p.jsx(Jh,{isExpanded:Qe,onToggleExpanded:()=>et(!Qe),isMuted:h,onToggleMute:qe,onRefreshChat:mf,chatExpanded:ft,onToggleChatExpanded:()=>jn(!ft),userName:a,onUserNameChange:yf,preferredWakeWord:g,onWakeWordChange:gf,systemActive:S,onToggleSystemActive:vf,wakeWords:P,onAddWakeWord:Zt,onDeleteWakeWord:pf,onToggleWakeWord:hf}),p.jsx("audio",{ref:ge,onEnded:Jt,style:{display:"none"}})]})};Zc(document.getElementById("root")).render(p.jsx(z.StrictMode,{children:p.jsx(Zh,{})}));
