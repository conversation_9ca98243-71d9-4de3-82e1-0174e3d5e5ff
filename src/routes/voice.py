from flask import Blueprint, request, jsonify, Response
import logging
import base64
import io

voice_bp = Blueprint('voice', __name__)
# Note: VoiceService now uses OpenAI Realtime API and doesn't support direct synthesis

@voice_bp.route('/synthesize', methods=['POST'])
def synthesize_speech():
    """Legacy endpoint - now redirects to use OpenAI Realtime API"""
    try:
        data = request.get_json()
        text = data.get('text', '')

        if not text:
            return jsonify({'success': False, 'error': 'Text is required'}), 400

        # Return a message indicating the new approach
        return jsonify({
            'success': False,
            'error': 'Text-to-speech synthesis has been replaced with OpenAI Realtime API. Use the WebSocket interface for voice interactions.',
            'message': 'Connect to the realtime chat WebSocket for voice conversations.',
            'text': text
        }), 200

    except Exception as e:
        logging.error(f"Error in legacy synthesis endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/stream-synthesize', methods=['POST'])
def stream_synthesize():
    """Legacy endpoint - now redirects to use OpenAI Realtime API"""
    try:
        data = request.get_json()
        text = data.get('text', '')

        if not text:
            return jsonify({'success': False, 'error': 'Text is required'}), 400

        return jsonify({
            'success': False,
            'error': 'Streaming synthesis has been replaced with OpenAI Realtime API. Use the WebSocket interface for voice interactions.',
            'message': 'Connect to the realtime chat WebSocket for voice conversations.',
            'text': text
        }), 200

    except Exception as e:
        logging.error(f"Error in legacy stream synthesis endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/transcribe', methods=['POST'])
def transcribe_audio():
    """Legacy endpoint - transcription now handled by OpenAI Realtime API"""
    try:
        return jsonify({
            'success': False,
            'error': 'Speech-to-text transcription has been replaced with OpenAI Realtime API. Use the WebSocket interface for voice interactions.',
            'message': 'Connect to the realtime chat WebSocket for voice conversations with automatic transcription.'
        }), 200

    except Exception as e:
        logging.error(f"Error in legacy transcribe endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/voices', methods=['GET'])
def get_available_voices():
    """Legacy endpoint - voices now handled by OpenAI Realtime API"""
    try:
        # Return OpenAI Realtime API voice options
        voices = [
            {'voice_id': 'alloy', 'name': 'Alloy', 'category': 'openai', 'description': 'OpenAI Realtime voice'},
            {'voice_id': 'echo', 'name': 'Echo', 'category': 'openai', 'description': 'OpenAI Realtime voice'},
            {'voice_id': 'fable', 'name': 'Fable', 'category': 'openai', 'description': 'OpenAI Realtime voice'},
            {'voice_id': 'onyx', 'name': 'Onyx', 'category': 'openai', 'description': 'OpenAI Realtime voice'},
            {'voice_id': 'nova', 'name': 'Nova', 'category': 'openai', 'description': 'OpenAI Realtime voice'},
            {'voice_id': 'shimmer', 'name': 'Shimmer', 'category': 'openai', 'description': 'OpenAI Realtime voice'}
        ]

        return jsonify({
            'success': True,
            'voices': voices,
            'message': 'These are OpenAI Realtime API voices. Use the WebSocket interface to select voices.'
        })

    except Exception as e:
        logging.error(f"Error getting voices: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/sound-effects', methods=['POST'])
def generate_sound_effects():
    """Legacy endpoint - sound effects not supported in OpenAI Realtime API"""
    try:
        data = request.get_json()
        description = data.get('description', '')

        if not description:
            return jsonify({'success': False, 'error': 'Description is required'}), 400

        return jsonify({
            'success': False,
            'error': 'Sound effects generation is not available with OpenAI Realtime API.',
            'message': 'Use the WebSocket interface for voice conversations.',
            'description': description
        }), 200

    except Exception as e:
        logging.error(f"Error in legacy sound effects endpoint: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@voice_bp.route('/voice-settings', methods=['GET', 'POST'])
def voice_settings():
    """Legacy endpoint - voice settings now handled by OpenAI Realtime API"""
    try:
        if request.method == 'GET':
            settings = {
                'voice': 'alloy',
                'model': 'gpt-4o-realtime-preview-2024-12-17',
                'input_audio_format': 'pcm16',
                'output_audio_format': 'pcm16',
                'turn_detection': {
                    'type': 'server_vad',
                    'threshold': 0.5,
                    'silence_duration_ms': 500
                }
            }
            return jsonify({
                'success': True,
                'settings': settings,
                'message': 'These are OpenAI Realtime API settings. Use the WebSocket interface to configure voice settings.'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Voice settings are now configured through the OpenAI Realtime API WebSocket interface.',
                'message': 'Connect to the realtime chat WebSocket to configure voice settings.'
            }), 200

    except Exception as e:
        logging.error(f"Error with legacy voice settings: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

