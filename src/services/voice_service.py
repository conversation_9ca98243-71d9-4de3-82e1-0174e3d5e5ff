import asyncio
import websockets
import json
import base64
import logging
from typing import Optional, Callable
import os

class OpenAIRealtimeVoiceService:
    def __init__(self):
        """Initialize the voice service with OpenAI Realtime API"""
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")

        self.websocket = None
        self.is_connected = False
        self.audio_callback = None
        self.transcript_callback = None
        self.response_callback = None

    async def connect(self):
        """Connect to OpenAI Realtime API via WebSocket"""
        try:
            url = "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-12-17"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "OpenAI-Beta": "realtime=v1"
            }

            logging.info(f"🔗 Connecting to OpenAI Realtime API...")
            self.websocket = await websockets.connect(url, extra_headers=headers)
            self.is_connected = True

            # Configure the session
            await self.send_session_update()

            logging.info("✅ Connected to OpenAI Realtime API")

            # Start listening for messages
            asyncio.create_task(self.listen_for_messages())

        except Exception as e:
            logging.error(f"❌ Error connecting to OpenAI Realtime API: {str(e)}")
            self.is_connected = False
            raise

    async def send_session_update(self):
        """Configure the session settings"""
        session_config = {
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": "You are a helpful voice assistant. Keep responses concise and conversational.",
                "voice": "alloy",
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "whisper-1"
                },
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": 0.5,
                    "prefix_padding_ms": 300,
                    "silence_duration_ms": 500
                }
            }
        }

        await self.websocket.send(json.dumps(session_config))
        logging.info("✅ Session configured with voice activity detection")

    async def listen_for_messages(self):
        """Listen for messages from OpenAI Realtime API"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self.handle_message(data)
        except Exception as e:
            logging.error(f"❌ Error listening for messages: {str(e)}")
            self.is_connected = False

    async def handle_message(self, data):
        """Handle messages from OpenAI Realtime API"""
        event_type = data.get("type")

        if event_type == "session.created":
            logging.info("✅ Session created successfully")

        elif event_type == "input_audio_buffer.speech_started":
            logging.info("🎤 Speech started")

        elif event_type == "input_audio_buffer.speech_stopped":
            logging.info("🔇 Speech stopped")

        elif event_type == "conversation.item.input_audio_transcription.completed":
            transcript = data.get("transcript", "")
            logging.info(f"📝 Transcript: {transcript}")
            if self.transcript_callback:
                self.transcript_callback(transcript)

        elif event_type == "response.audio.delta":
            # Receive audio response from AI
            audio_data = data.get("delta", "")
            if audio_data and self.audio_callback:
                audio_bytes = base64.b64decode(audio_data)
                self.audio_callback(audio_bytes)

        elif event_type == "response.done":
            logging.info("✅ Response completed")
            if self.response_callback:
                self.response_callback()

        elif event_type == "error":
            error_msg = data.get("error", {}).get("message", "Unknown error")
            logging.error(f"❌ OpenAI Realtime API error: {error_msg}")

    async def send_audio(self, audio_data: bytes):
        """Send audio data to OpenAI Realtime API"""
        if not self.is_connected:
            logging.error("❌ Not connected to OpenAI Realtime API")
            return

        try:
            # Convert audio to base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Send audio append event
            event = {
                "type": "input_audio_buffer.append",
                "audio": audio_base64
            }

            await self.websocket.send(json.dumps(event))

        except Exception as e:
            logging.error(f"❌ Error sending audio: {str(e)}")

    async def commit_audio(self):
        """Commit the audio buffer and trigger processing"""
        if not self.is_connected:
            return

        try:
            event = {
                "type": "input_audio_buffer.commit"
            }
            await self.websocket.send(json.dumps(event))

        except Exception as e:
            logging.error(f"❌ Error committing audio: {str(e)}")

    def set_audio_callback(self, callback: Callable[[bytes], None]):
        """Set callback for receiving audio responses"""
        self.audio_callback = callback

    def set_transcript_callback(self, callback: Callable[[str], None]):
        """Set callback for receiving transcripts"""
        self.transcript_callback = callback

    def set_response_callback(self, callback: Callable[[], None]):
        """Set callback for response completion"""
        self.response_callback = callback


    async def disconnect(self):
        """Disconnect from OpenAI Realtime API"""
        if self.websocket:
            await self.websocket.close()
            self.is_connected = False
            logging.info("✅ Disconnected from OpenAI Realtime API")

    # Legacy methods for compatibility
    def synthesize_speech(self, text: str) -> bytes:
        """Legacy method - returns empty bytes since we use real-time audio"""
        logging.warning("synthesize_speech called - use real-time audio instead")
        return b''


