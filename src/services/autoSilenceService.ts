export interface AutoSilenceConfig {
  silenceThreshold: number // Volume threshold below which is considered silence
  silenceDuration: number // Duration in milliseconds to wait before stopping
  sampleRate: number // Audio sample rate
}

export class AutoSilenceService {
  private config: AutoSilenceConfig
  private isMonitoring = false
  private silenceStartTime: number | null = null
  private onSilenceDetectedCallback: (() => void) | null = null
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  // private mediaStream: MediaStream | null = null
  private animationFrameId: number | null = null

  constructor(config: Partial<AutoSilenceConfig> = {}) {
    this.config = {
      silenceThreshold: 0.01, // Very low threshold for silence detection
      silenceDuration: 1500, // 1.5 seconds of silence
      sampleRate: 16000,
      ...config
    }
  }

  async initialize(mediaStream: MediaStream): Promise<void> {
    try {
      // this.mediaStream = mediaStream
      
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate
      })

      // Create analyser node
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 256
      this.analyser.smoothingTimeConstant = 0.8

      // Connect media stream to analyser
      const source = this.audioContext.createMediaStreamSource(mediaStream)
      source.connect(this.analyser)

      console.log('Auto-silence service initialized')
    } catch (error) {
      console.error('Failed to initialize auto-silence service:', error)
      throw error
    }
  }

  startMonitoring(): void {
    if (!this.analyser || this.isMonitoring) {
      return
    }

    this.isMonitoring = true
    this.silenceStartTime = null
    this.monitorAudio()
    
    console.log('Auto-silence monitoring started')
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return
    }

    this.isMonitoring = false
    this.silenceStartTime = null
    
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }
    
    console.log('Auto-silence monitoring stopped')
  }

  private monitorAudio(): void {
    if (!this.isMonitoring || !this.analyser) {
      return
    }

    const bufferLength = this.analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    
    this.analyser.getByteFrequencyData(dataArray)
    
    // Calculate average volume
    const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
    const normalizedVolume = average / 255 // Normalize to 0-1 range
    
    const currentTime = Date.now()
    
    if (normalizedVolume < this.config.silenceThreshold) {
      // Silence detected
      if (this.silenceStartTime === null) {
        this.silenceStartTime = currentTime
      } else {
        // Check if silence duration exceeded
        const silenceDuration = currentTime - this.silenceStartTime
        if (silenceDuration >= this.config.silenceDuration) {
          this.onSilenceDetected()
          return // Stop monitoring after detection
        }
      }
    } else {
      // Sound detected, reset silence timer
      this.silenceStartTime = null
    }

    // Continue monitoring
    this.animationFrameId = requestAnimationFrame(() => this.monitorAudio())
  }

  private onSilenceDetected(): void {
    console.log('Auto-silence detected')
    this.stopMonitoring()
    
    if (this.onSilenceDetectedCallback) {
      this.onSilenceDetectedCallback()
    }
  }

  setOnSilenceDetected(callback: () => void): void {
    this.onSilenceDetectedCallback = callback
  }

  updateConfig(config: Partial<AutoSilenceConfig>): void {
    this.config = { ...this.config, ...config }
  }

  getConfig(): AutoSilenceConfig {
    return { ...this.config }
  }

  destroy(): void {
    this.stopMonitoring()
    
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
    
    this.analyser = null
    // this.mediaStream = null
  }

  // Get current volume level for UI feedback
  getCurrentVolume(): number {
    if (!this.analyser) {
      return 0
    }

    const bufferLength = this.analyser.frequencyBinCount
    const dataArray = new Uint8Array(bufferLength)
    this.analyser.getByteFrequencyData(dataArray)
    
    const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength
    return average / 255 // Normalize to 0-1 range
  }

  // Get time remaining until silence detection
  getTimeUntilSilence(): number {
    if (!this.silenceStartTime) {
      return this.config.silenceDuration
    }
    
    const elapsed = Date.now() - this.silenceStartTime
    return Math.max(0, this.config.silenceDuration - elapsed)
  }

  // Check if currently in silence period
  isInSilence(): boolean {
    return this.silenceStartTime !== null
  }
}
