import logging
import base64
import asyncio
import json
from flask import request
from flask_socketio import emit
from src.services.voice_service import VoiceService
from src.models.conversation import Conversation
from src.models.message import Message
from src.models.user import db

# Store active connections and their voice services
active_connections = {}

def register_realtime_chat_handlers(socketio):
    """Register OpenAI Realtime API WebSocket event handlers"""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        try:
            logging.info(f"🔗 Client connected: {request.sid}")
            emit('connected', {'status': 'connected', 'sid': request.sid})
        except Exception as e:
            logging.error(f"❌ Error in connect handler: {str(e)}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        try:
            logging.info(f"🔌 Client disconnected: {request.sid}")
            # Clean up voice service connection
            if request.sid in active_connections:
                voice_service = active_connections[request.sid].get('voice_service')
                if voice_service:
                    asyncio.create_task(voice_service.disconnect())
                del active_connections[request.sid]
        except Exception as e:
            logging.error(f"❌ Error in disconnect handler: {str(e)}")
    
    @socketio.on('start_realtime_conversation')
    def handle_start_realtime_conversation(data):
        """Start a new realtime conversation with OpenAI"""
        try:
            logging.info(f"🎤 Starting realtime conversation for {request.sid}")
            
            # Create or get conversation
            conversation_id = data.get('conversation_id')
            if conversation_id:
                conversation = Conversation.query.get(conversation_id)
            else:
                conversation = Conversation(
                    title="OpenAI Realtime Conversation",
                    user_id=1  # Default user for now
                )
                db.session.add(conversation)
                db.session.commit()
            
            # Set up OpenAI Realtime API voice service
            logging.info("🎤 Setting up OpenAI Realtime API voice service...")

            # Initialize voice service
            voice_service = VoiceService()

            # Store connection info with voice service
            active_connections[request.sid] = {
                'conversation': conversation,
                'voice_service': voice_service
            }

            # Send initial greeting immediately (before voice connection)
            greeting_message = "Hi! I'm your voice assistant powered by OpenAI's Realtime API. Just start talking and I'll respond in real-time!"

            # Save greeting message
            greeting_msg = Message(
                conversation_id=conversation.id,
                content=greeting_message,
                message_type='assistant'
            )
            db.session.add(greeting_msg)
            db.session.commit()

            # Send greeting to client
            emit('ai_greeting', {
                'message': greeting_message,
                'conversation_id': conversation.id
            })

            # Emit connection success
            emit('realtime_connected', {
                'status': 'connected',
                'conversation_id': conversation.id
            })

            logging.info(f"✅ OpenAI Realtime API conversation started for {request.sid}")
            
        except Exception as e:
            logging.error(f"❌ Error starting realtime conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('send_audio_stream')
    def handle_audio_stream(data):
        """Handle incoming audio stream from client"""
        try:
            logging.info("🎧 Received audio stream from client")

            if request.sid not in active_connections:
                emit('error', {'message': 'No active conversation'})
                return

            conversation = active_connections[request.sid]['conversation']
            voice_service = active_connections[request.sid].get('voice_service')

            if not voice_service:
                emit('error', {'message': 'Voice service not available'})
                return

            # Get audio data
            audio_data = data.get('audio_data', '')
            if not audio_data:
                return

            # For now, simulate OpenAI response
            # TODO: Implement actual OpenAI Realtime API call

            # Send transcript back
            emit('transcript_received', {
                'transcript': "I heard you speaking! OpenAI processing will be implemented next.",
                'conversation_id': conversation.id,
                'message_id': 999
            })

            # Send a test voice response
            emit('voice_response', {
                'audio_data': '',  # Empty for now
                'conversation_id': conversation.id,
                'format': 'pcm16'
            })

            logging.info("✅ Audio stream processed")

        except Exception as e:
            logging.error(f"❌ Error handling audio stream: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('commit_audio')
    def handle_commit_audio():
        """Commit audio buffer and trigger processing"""
        try:
            if request.sid not in active_connections:
                emit('error', {'message': 'No active conversation'})
                return
            
            voice_service = active_connections[request.sid]['voice_service']
            if not voice_service.is_connected:
                emit('error', {'message': 'Voice service not connected'})
                return
            
            # Commit audio buffer
            async def commit_audio():
                try:
                    await voice_service.commit_audio()
                except Exception as e:
                    logging.error(f"❌ Error committing audio: {str(e)}")
            
            asyncio.create_task(commit_audio())
            
        except Exception as e:
            logging.error(f"❌ Error committing audio: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('synthesize_greeting')
    def handle_synthesize_greeting(data):
        """Handle greeting synthesis (legacy compatibility)"""
        try:
            text = data.get('text', '')
            conversation_id = data.get('conversation_id')
            
            if not text:
                return
                
            logging.info(f"🎤 Sending greeting: {text}")
            
            # For now, just emit the greeting as text
            # The frontend can use text-to-speech or we can enhance this later
            emit('ai_greeting', {
                'message': text,
                'conversation_id': conversation_id
            })
            
        except Exception as e:
            logging.error(f"❌ Error handling greeting: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('get_conversation_history')
    def handle_get_conversation_history(data):
        """Get conversation history"""
        try:
            conversation_id = data.get('conversation_id')
            if not conversation_id:
                emit('error', {'message': 'Conversation ID required'})
                return
            
            conversation = Conversation.query.get(conversation_id)
            if not conversation:
                emit('error', {'message': 'Conversation not found'})
                return
            
            messages = Message.query.filter_by(conversation_id=conversation_id).order_by(Message.created_at).all()
            
            message_list = []
            for message in messages:
                message_list.append({
                    'id': message.id,
                    'content': message.content,
                    'role': message.role,
                    'message_type': message.message_type,
                    'created_at': message.created_at.isoformat()
                })
            
            emit('conversation_history', {
                'conversation_id': conversation_id,
                'messages': message_list
            })
            
        except Exception as e:
            logging.error(f"❌ Error getting conversation history: {str(e)}")
            emit('error', {'message': str(e)})
