import logging
import base64
import asyncio
import json
from flask import request
from flask_socketio import emit
from src.services.voice_service import VoiceService
from src.models.conversation import Conversation
from src.models.message import Message
from src.models.user import db

# Store active connections and their voice services
active_connections = {}

def register_realtime_chat_handlers(socketio):
    """Register OpenAI Realtime API WebSocket event handlers"""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        try:
            logging.info(f"🔗 Client connected: {request.sid}")
            emit('connected', {'status': 'connected', 'sid': request.sid})
        except Exception as e:
            logging.error(f"❌ Error in connect handler: {str(e)}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        try:
            logging.info(f"🔌 Client disconnected: {request.sid}")
            # Clean up voice service connection
            if request.sid in active_connections:
                voice_service = active_connections[request.sid].get('voice_service')
                if voice_service:
                    asyncio.create_task(voice_service.disconnect())
                del active_connections[request.sid]
        except Exception as e:
            logging.error(f"❌ Error in disconnect handler: {str(e)}")
    
    @socketio.on('start_realtime_conversation')
    def handle_start_realtime_conversation(data):
        """Start a new realtime conversation with OpenAI"""
        try:
            logging.info(f"🎤 Starting realtime conversation for {request.sid}")
            
            # Create or get conversation
            conversation_id = data.get('conversation_id')
            if conversation_id:
                conversation = Conversation.query.get(conversation_id)
            else:
                conversation = Conversation()
                db.session.add(conversation)
                db.session.commit()
            
            # Initialize voice service for this connection
            voice_service = VoiceService()
            
            # Set up callbacks
            def on_audio_response(audio_data):
                """Handle audio response from OpenAI"""
                try:
                    audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                    emit('voice_response', {
                        'audio_data': audio_base64,
                        'conversation_id': conversation.id,
                        'format': 'pcm16'
                    })
                except Exception as e:
                    logging.error(f"❌ Error sending audio response: {str(e)}")
            
            def on_transcript(transcript):
                """Handle transcript from OpenAI"""
                try:
                    logging.info(f"📝 Transcript: {transcript}")
                    
                    # Save user message
                    user_message = Message(
                        conversation_id=conversation.id,
                        content=transcript,
                        role='user',
                        message_type='text'
                    )
                    db.session.add(user_message)
                    db.session.commit()
                    
                    emit('transcript_received', {
                        'transcript': transcript,
                        'conversation_id': conversation.id,
                        'message_id': user_message.id
                    })
                except Exception as e:
                    logging.error(f"❌ Error handling transcript: {str(e)}")
            
            def on_response_complete():
                """Handle response completion"""
                try:
                    logging.info("✅ Response completed")
                    emit('response_complete', {
                        'conversation_id': conversation.id
                    })
                except Exception as e:
                    logging.error(f"❌ Error handling response completion: {str(e)}")
            
            # Set callbacks
            voice_service.set_audio_callback(on_audio_response)
            voice_service.set_transcript_callback(on_transcript)
            voice_service.set_response_callback(on_response_complete)
            
            # Store connection info
            active_connections[request.sid] = {
                'voice_service': voice_service,
                'conversation': conversation
            }
            
            # Connect to OpenAI Realtime API
            async def connect_to_openai():
                try:
                    await voice_service.connect()
                    emit('realtime_connected', {
                        'status': 'connected',
                        'conversation_id': conversation.id
                    })
                    
                    # Send initial greeting
                    emit('ai_greeting', {
                        'message': "Hi! I'm your voice assistant. Just start talking and I'll respond!",
                        'conversation_id': conversation.id
                    })
                    
                except Exception as e:
                    logging.error(f"❌ Error connecting to OpenAI: {str(e)}")
                    emit('error', {'message': f'Failed to connect to voice service: {str(e)}'})
            
            # Start connection in background
            asyncio.create_task(connect_to_openai())
            
        except Exception as e:
            logging.error(f"❌ Error starting realtime conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('send_audio_stream')
    def handle_audio_stream(data):
        """Handle incoming audio stream from client"""
        try:
            if request.sid not in active_connections:
                emit('error', {'message': 'No active conversation'})
                return
            
            voice_service = active_connections[request.sid]['voice_service']
            if not voice_service.is_connected:
                emit('error', {'message': 'Voice service not connected'})
                return
            
            # Get audio data
            audio_data = data.get('audio_data', '')
            if not audio_data:
                return
            
            # Decode base64 audio
            try:
                audio_bytes = base64.b64decode(audio_data)
            except Exception as e:
                logging.error(f"❌ Error decoding audio: {str(e)}")
                return
            
            # Send audio to OpenAI
            async def send_audio():
                try:
                    await voice_service.send_audio(audio_bytes)
                except Exception as e:
                    logging.error(f"❌ Error sending audio to OpenAI: {str(e)}")
            
            asyncio.create_task(send_audio())
            
        except Exception as e:
            logging.error(f"❌ Error handling audio stream: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('commit_audio')
    def handle_commit_audio():
        """Commit audio buffer and trigger processing"""
        try:
            if request.sid not in active_connections:
                emit('error', {'message': 'No active conversation'})
                return
            
            voice_service = active_connections[request.sid]['voice_service']
            if not voice_service.is_connected:
                emit('error', {'message': 'Voice service not connected'})
                return
            
            # Commit audio buffer
            async def commit_audio():
                try:
                    await voice_service.commit_audio()
                except Exception as e:
                    logging.error(f"❌ Error committing audio: {str(e)}")
            
            asyncio.create_task(commit_audio())
            
        except Exception as e:
            logging.error(f"❌ Error committing audio: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('synthesize_greeting')
    def handle_synthesize_greeting(data):
        """Handle greeting synthesis (legacy compatibility)"""
        try:
            text = data.get('text', '')
            conversation_id = data.get('conversation_id')
            
            if not text:
                return
                
            logging.info(f"🎤 Sending greeting: {text}")
            
            # For now, just emit the greeting as text
            # The frontend can use text-to-speech or we can enhance this later
            emit('ai_greeting', {
                'message': text,
                'conversation_id': conversation_id
            })
            
        except Exception as e:
            logging.error(f"❌ Error handling greeting: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('get_conversation_history')
    def handle_get_conversation_history(data):
        """Get conversation history"""
        try:
            conversation_id = data.get('conversation_id')
            if not conversation_id:
                emit('error', {'message': 'Conversation ID required'})
                return
            
            conversation = Conversation.query.get(conversation_id)
            if not conversation:
                emit('error', {'message': 'Conversation not found'})
                return
            
            messages = Message.query.filter_by(conversation_id=conversation_id).order_by(Message.created_at).all()
            
            message_list = []
            for message in messages:
                message_list.append({
                    'id': message.id,
                    'content': message.content,
                    'role': message.role,
                    'message_type': message.message_type,
                    'created_at': message.created_at.isoformat()
                })
            
            emit('conversation_history', {
                'conversation_id': conversation_id,
                'messages': message_list
            })
            
        except Exception as e:
            logging.error(f"❌ Error getting conversation history: {str(e)}")
            emit('error', {'message': str(e)})
