from flask import request # type: ignore
from flask_socketio import emit, join_room, leave_room, disconnect # type: ignore
from src.services.ai_service import AIService # type: ignore
from src.services.voice_service import VoiceService # type: ignore
from src.services.agent_service import AgentService # type: ignore
from src.services.wake_word_service import WakeWordService # type: ignore
from src.models.user import db
from src.models.conversation import Conversation
from src.models.message import Message
import logging
import base64
import json

# Initialize services
ai_service = AIService()
voice_service = VoiceService()
agent_service = AgentService()
wake_word_service = WakeWordService()

# Store active connections
active_connections = {}

def register_chat_handlers(socketio):
    """Register all WebSocket event handlers"""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        try:
            logging.info(f"Client connected: {request.sid}")
            emit('connected', {'status': 'connected', 'sid': request.sid})
        except Exception as e:
            logging.error(f"Error in connect handler: {str(e)}")
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        try:
            logging.info(f"Client disconnected: {request.sid}")
            # Clean up any active connections
            if request.sid in active_connections:
                del active_connections[request.sid]
        except Exception as e:
            logging.error(f"Error in disconnect handler: {str(e)}")
    
    @socketio.on('join_conversation')
    def handle_join_conversation(data):
        """Handle joining a conversation room"""
        try:
            conversation_id = data.get('conversation_id')
            user_id = data.get('user_id', 1)
            
            if not conversation_id:
                emit('error', {'message': 'Conversation ID is required'})
                return
            
            # Join the conversation room
            join_room(f"conversation_{conversation_id}")
            
            # Store connection info
            active_connections[request.sid] = {
                'conversation_id': conversation_id,
                'user_id': user_id
            }
            
            emit('conversation_joined', {
                'conversation_id': conversation_id,
                'status': 'joined'
            })
            
            logging.info(f"Client {request.sid} joined conversation {conversation_id}")

            # No automatic greeting - user will initiate conversation

        except Exception as e:
            logging.error(f"Error joining conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('leave_conversation')
    def handle_leave_conversation(data):
        """Handle leaving a conversation room"""
        try:
            conversation_id = data.get('conversation_id')
            
            if conversation_id:
                leave_room(f"conversation_{conversation_id}")
                emit('conversation_left', {
                    'conversation_id': conversation_id,
                    'status': 'left'
                })
            
            # Clean up connection info
            if request.sid in active_connections:
                del active_connections[request.sid]
                
        except Exception as e:
            logging.error(f"Error leaving conversation: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('send_message')
    def handle_send_message(data):
        """Handle sending a text message"""
        try:
            conversation_id = data.get('conversation_id')
            content = data.get('content', '')
            message_type = data.get('message_type', 'user')
            
            if not conversation_id or not content:
                emit('error', {'message': 'Conversation ID and content are required'})
                return
            
            # Save user message
            user_message = Message(
                conversation_id=conversation_id,
                content=content,
                message_type=message_type
            )
            db.session.add(user_message)
            
            # Emit user message to room
            socketio.emit('message_received', {
                'message': user_message.to_dict(),
                'type': 'user'
            }, room=f"conversation_{conversation_id}")
            
            # Try DialogFlow first for quick responses
            try:
                # Check if this is a quick DialogFlow query
                quick_response = None
                try:
                    # Simple pattern matching for quick responses
                    content_lower = content.lower().strip()
                    if any(word in content_lower for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                        quick_response = "Hello! I'm here to listen and support you. How are you feeling today?"
                    elif any(word in content_lower for word in ['how are you', 'how do you feel']):
                        quick_response = "I'm doing well, thank you for asking! I'm here and ready to help. How are you doing?"
                    elif any(word in content_lower for word in ['thank', 'thanks']):
                        quick_response = "You're very welcome! I'm happy to help. Is there anything else you'd like to talk about?"
                    elif any(word in content_lower for word in ['bye', 'goodbye', 'see you']):
                        quick_response = "Take care! Remember, I'm always here when you need someone to talk to."
                    elif any(word in content_lower for word in ['help', 'support', 'need']):
                        quick_response = "I'm here to support you. What's on your mind? Feel free to share whatever you're thinking about."
                except:
                    pass

                # Use quick response if available, otherwise use AI service
                if quick_response:
                    ai_response = quick_response
                    logging.info(f"🚀 Quick response used: {ai_response[:50]}...")
                else:
                    ai_response = ai_service.generate_response(content, conversation_id)

                # Save AI message
                ai_message = Message(
                    conversation_id=conversation_id,
                    content=ai_response,
                    message_type='assistant'
                )
                db.session.add(ai_message)

                # Update conversation timestamp
                conversation = Conversation.query.get(conversation_id)
                if conversation:
                    conversation.updated_at = db.func.current_timestamp()

                db.session.commit()

                # Emit AI response to room
                socketio.emit('message_received', {
                    'message': ai_message.to_dict(),
                    'type': 'assistant'
                }, room=f"conversation_{conversation_id}")

                # Generate voice response if requested
                generate_voice = data.get('generate_voice', False)
                if generate_voice:
                    try:
                        audio_data = voice_service.synthesize_speech(ai_response)
                        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

                        socketio.emit('voice_response', {
                            'audio_data': audio_base64,
                            'message_id': ai_message.id,
                            'format': 'mp3'
                        }, room=f"conversation_{conversation_id}")

                    except Exception as voice_error:
                        logging.error(f"Voice generation error: {str(voice_error)}")
                        socketio.emit('error', {
                            'message': 'Failed to generate voice response'
                        }, room=f"conversation_{conversation_id}")

            except Exception as ai_error:
                logging.error(f"AI response error: {str(ai_error)}")
                db.session.commit()  # Still save user message

                socketio.emit('error', {
                    'message': 'Failed to generate AI response'
                }, room=f"conversation_{conversation_id}")
                
        except Exception as e:
            logging.error(f"Error handling message: {str(e)}")
            db.session.rollback()
            emit('error', {'message': str(e)})
    
    @socketio.on('start_voice_recording')
    def handle_start_voice_recording(data):
        """Handle start of voice recording"""
        try:
            conversation_id = data.get('conversation_id')
            
            emit('recording_started', {
                'conversation_id': conversation_id,
                'status': 'recording'
            })
            
        except Exception as e:
            logging.error(f"Error starting voice recording: {str(e)}")
            emit('error', {'message': str(e)})
    
    @socketio.on('start_voice_streaming')
    def handle_start_voice_streaming(data):
        """Handle start of real-time voice streaming"""
        try:
            conversation_id = data.get('conversation_id')

            emit('streaming_started', {
                'conversation_id': conversation_id,
                'status': 'Voice streaming active'
            })

            logging.info(f"🎤 Voice streaming started for conversation {conversation_id}")

        except Exception as e:
            logging.error(f"Error starting voice streaming: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('voice_stream_chunk')
    def handle_voice_stream_chunk(data):
        """Handle real-time voice stream chunks"""
        try:
            conversation_id = data.get('conversation_id')
            audio_chunk = data.get('audio_chunk')
            sample_rate = data.get('sample_rate', 16000)

            # Process audio chunk in real-time
            # This is where you'd implement real-time speech recognition
            # For now, we'll accumulate chunks and process when streaming stops

            # You could implement real-time STT here with streaming APIs
            logging.debug(f"📡 Received audio chunk: {len(audio_chunk)} samples at {sample_rate}Hz")

        except Exception as e:
            logging.error(f"Error handling voice stream chunk: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('stop_voice_streaming')
    def handle_stop_voice_streaming(data):
        """Handle stop of real-time voice streaming"""
        try:
            conversation_id = data.get('conversation_id')

            emit('streaming_stopped', {
                'conversation_id': conversation_id,
                'status': 'Voice streaming stopped'
            })

            logging.info(f"🛑 Voice streaming stopped for conversation {conversation_id}")

        except Exception as e:
            logging.error(f"Error stopping voice streaming: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('voice_stream')
    def handle_voice_stream(data):
        """Handle voice stream data for speech-to-text processing"""
        try:
            conversation_id = data.get('conversation_id')
            audio_data = data.get('audio_data')

            if not conversation_id or not audio_data:
                emit('error', {'message': 'Conversation ID and audio data are required'})
                return

            logging.info(f"🎤 Processing voice stream for conversation {conversation_id}")

            # Emit processing status
            emit('voice_processing', {
                'conversation_id': conversation_id,
                'status': 'transcribing'
            })

            # Convert base64 to bytes
            import base64
            audio_bytes = base64.b64decode(audio_data)

            # Transcribe audio to text
            transcribed_text = voice_service.transcribe_audio(audio_bytes)

            logging.info(f"🎯 Transcribed text: '{transcribed_text}'")

            if transcribed_text and transcribed_text.strip():
                # Emit transcription result
                emit('voice_transcribed', {
                    'conversation_id': conversation_id,
                    'text': transcribed_text
                })

                # Handle the transcribed text as a regular message
                handle_send_message({
                    'conversation_id': conversation_id,
                    'content': transcribed_text,
                    'message_type': 'user',
                    'generate_voice': True
                })
            else:
                emit('voice_processing', {
                    'conversation_id': conversation_id,
                    'status': 'no_speech_detected'
                })

        except Exception as e:
            logging.error(f"Error handling voice stream: {str(e)}")
            emit('error', {'message': f'Voice processing failed: {str(e)}'})
            emit('voice_processing', {
                'conversation_id': data.get('conversation_id'),
                'status': 'error'
            })
    
    @socketio.on('execute_command')
    def handle_execute_command(data):
        """Handle natural language command execution"""
        try:
            conversation_id = data.get('conversation_id')
            command = data.get('command', '')
            context = data.get('context', {})
            
            if not command:
                emit('error', {'message': 'Command is required'})
                return
            
            # Emit command execution start
            emit('command_progress', {
                'status': 'started',
                'command': command,
                'message': 'Processing your command...'
            })
            
            # Execute the command
            result = agent_service.execute_command(command, context, conversation_id)
            
            # Save command as a message
            command_message = Message(
                conversation_id=conversation_id,
                content=command,
                message_type='user',
                message_metadata={'type': 'command'}
            )
            db.session.add(command_message)
            
            # Save result as assistant message
            result_content = result.get('message', 'Command executed')
            if result.get('content'):
                result_content += f"\n\n{result['content']}"
            
            result_message = Message(
                conversation_id=conversation_id,
                content=result_content,
                message_type='assistant',
                message_metadata={'type': 'command_result', 'command_result': result}
            )
            db.session.add(result_message)
            
            db.session.commit()
            
            # Emit command execution result
            emit('command_executed', {
                'result': result,
                'command': command,
                'status': 'completed'
            })
            
            # Also emit as regular messages
            socketio.emit('message_received', {
                'message': command_message.to_dict(),
                'type': 'user'
            }, room=f"conversation_{conversation_id}")
            
            socketio.emit('message_received', {
                'message': result_message.to_dict(),
                'type': 'assistant'
            }, room=f"conversation_{conversation_id}")
            
        except Exception as e:
            logging.error(f"Error executing command: {str(e)}")
            emit('command_executed', {
                'result': {'success': False, 'error': str(e)},
                'command': command,
                'status': 'failed'
            })
            emit('error', {'message': str(e)})
    
    @socketio.on('generate_voice')
    def handle_generate_voice(data):
        """Handle voice generation request"""
        try:
            text = data.get('text', '')
            voice_id = data.get('voice_id', None)
            
            if not text:
                emit('error', {'message': 'Text is required for voice generation'})
                return
            
            # Generate voice
            audio_data = voice_service.synthesize_speech(text, voice_id)
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            emit('voice_generated', {
                'audio_data': audio_base64,
                'text': text,
                'format': 'mp3'
            })
            
        except Exception as e:
            logging.error(f"Error generating voice: {str(e)}")
            emit('error', {'message': str(e)})

    # Wake Word Detection Handlers
    @socketio.on('start_wake_word_detection')
    def handle_start_wake_word_detection(data):
        """Start wake word detection"""
        try:
            # Set up detection callback
            def on_wake_word_detected(detection):
                logging.info(f"🎯 Wake word detected: {detection}")
                # Emit wake word detected event to all clients
                socketio.emit('wake_word_detected', {
                    'keyword': detection['keyword'],
                    'confidence': detection['confidence'],
                    'timestamp': detection['timestamp']
                }, broadcast=True)

                # Also trigger voice recording start
                socketio.emit('start_voice_recording', {
                    'triggered_by': 'wake_word',
                    'wake_word': detection['keyword'],
                    'confidence': detection['confidence']
                }, broadcast=True)

            # Set up error callback
            def on_wake_word_error(error):
                emit('wake_word_error', {'message': str(error)})

            wake_word_service.set_detection_callback(on_wake_word_detected)
            wake_word_service.set_error_callback(on_wake_word_error)

            # Configure wake words if provided
            wake_words = data.get('wake_words', ['hey jarvis', 'alexa'])
            threshold = data.get('threshold', 0.5)

            wake_word_service.set_wake_words(wake_words)
            wake_word_service.set_threshold(threshold)

            # Start listening
            wake_word_service.start_listening()

            emit('wake_word_detection_started', {
                'status': 'started',
                'wake_words': wake_words,
                'threshold': threshold
            })

        except Exception as e:
            logging.error(f"Error starting wake word detection: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('stop_wake_word_detection')
    def handle_stop_wake_word_detection():
        """Stop wake word detection"""
        try:
            wake_word_service.stop_listening()
            emit('wake_word_detection_stopped', {'status': 'stopped'})

        except Exception as e:
            logging.error(f"Error stopping wake word detection: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('synthesize_greeting')
    def handle_synthesize_greeting(data):
        """Synthesize and play greeting message"""
        try:
            text = data.get('text', '')

            if not text:
                return

            logging.info(f"🎤 Synthesizing greeting: {text}")

            # Generate voice response
            audio_data = voice_service.synthesize_speech(text)
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            # Send voice greeting to client
            emit('voice_response', {
                'audio_data': audio_base64,
                'message_id': 0,  # Special ID for greeting
                'format': 'mp3',
                'is_greeting': True
            })

            logging.info("✅ Greeting synthesized and sent")

        except Exception as e:
            logging.error(f"❌ Error synthesizing greeting: {str(e)}")
            emit('error', {'message': f'Greeting synthesis error: {str(e)}'})

    @socketio.on('wake_word_audio_stream')
    def handle_wake_word_audio_stream(data):
        """Handle audio stream for wake word detection"""
        try:
            audio_data = data.get('audio_data')
            audio_format = data.get('format', 'pcm_s16le')

            if not audio_data:
                emit('error', {'message': 'Audio data is required'})
                return

            # Add audio to wake word service for processing with format info
            wake_word_service.add_audio_data(audio_data, format=audio_format)

        except Exception as e:
            logging.error(f"Error processing wake word audio: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('get_wake_word_status')
    def handle_get_wake_word_status():
        """Get wake word detection status"""
        try:
            status = wake_word_service.get_status()
            emit('wake_word_status', status)

        except Exception as e:
            logging.error(f"Error getting wake word status: {str(e)}")
            emit('error', {'message': str(e)})

    @socketio.on('get_available_wake_words')
    def handle_get_available_wake_words():
        """Get list of available wake word models"""
        try:
            models = wake_word_service.get_available_models()
            emit('available_wake_words', {'models': models})

        except Exception as e:
            logging.error(f"Error getting available wake words: {str(e)}")
            emit('error', {'message': str(e)})
