import React, { useState, useRef, useEffect } from 'react'
import { Settings, X, Volume2, VolumeX, RotateCcw, Maximize2, Minimize2, Move } from 'lucide-react'

interface WakeWord {
  id: string
  keyword: string
  enabled: boolean
  sensitivity: number
}

interface ControlCentreProps {
  isExpanded: boolean
  onToggleExpanded: () => void
  isMuted: boolean
  onToggleMute: () => void
  onRefreshChat: () => void
  chatExpanded: boolean
  onToggleChatExpanded: () => void
  // New user preferences
  userName: string
  onUserNameChange: (name: string) => void
  preferredWakeWord: string
  onWakeWordChange: (wakeWord: string) => void
  systemActive: boolean
  onToggleSystemActive: () => void
  wakeWords?: WakeWord[]
  onAddWakeWord?: (keyword: string) => void
  onDeleteWakeWord?: (id: string) => void
  onToggleWakeWord?: (id: string) => void
}

const ControlCentre: React.FC<ControlCentreProps> = ({
  isExpanded,
  onToggleExpanded,
  isMuted,
  onToggleMute,
  onRefreshChat,
  chatExpanded,
  onToggleChatExpanded,
  userName,
  onUserNameChange,
  // preferredWakeWord,
  // onWakeWordChange,
  systemActive,
  onToggleSystemActive,
  wakeWords = [],
  onAddWakeWord,
  onDeleteWakeWord,
  onToggleWakeWord
}) => {
  const [position, setPosition] = useState({ x: 20, y: 20 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const [newWakeWord, setNewWakeWord] = useState('')
  // const [savedWakeWords, setSavedWakeWords] = useState<string[]>([])
  const controlCentreRef = useRef<HTMLDivElement>(null)

  // Wake word management functions
  // const handleSaveWakeWord = () => {
  //   if (newWakeWord.trim() && !savedWakeWords.includes(newWakeWord.trim())) {
  //     const updatedWakeWords = [...savedWakeWords, newWakeWord.trim()]
  //     setSavedWakeWords(updatedWakeWords)
  //     localStorage.setItem('savedWakeWords', JSON.stringify(updatedWakeWords))
  //     setNewWakeWord('')
  //   }
  // }

  // const handleDeleteWakeWord = (wakeWord: string) => {
  //   const updatedWakeWords = savedWakeWords.filter(w => w !== wakeWord)
  //   setSavedWakeWords(updatedWakeWords)
  //   localStorage.setItem('savedWakeWords', JSON.stringify(updatedWakeWords))

  //   // If deleted wake word was the current one, clear it
  //   if (preferredWakeWord === wakeWord) {
  //     onWakeWordChange('')
  //   }
  // }

  // const handleSelectWakeWord = (wakeWord: string) => {
  //   onWakeWordChange(wakeWord)
  //   setNewWakeWord(wakeWord)
  // }

  // Load saved wake words from localStorage
  // useEffect(() => {
  //   const saved = localStorage.getItem('savedWakeWords')
  //   if (saved) {
  //     try {
  //       const parsedWakeWords = JSON.parse(saved)
  //       setSavedWakeWords(parsedWakeWords)
  //     } catch (error) {
  //       console.error('Error loading saved wake words:', error)
  //     }
  //   }
  // }, [])

  // Drag functionality
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!controlCentreRef.current) return

    const rect = controlCentreRef.current.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    setIsDragging(true)
  }

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      const newX = e.clientX - dragOffset.x
      const newY = e.clientY - dragOffset.y

      // Keep within viewport bounds
      const maxX = window.innerWidth - 400 // Control centre width
      const maxY = window.innerHeight - 600 // Control centre height

      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY))
      })
    }

    const handleMouseUp = () => {
      setIsDragging(false)
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragOffset])

  if (!isExpanded) {
    return (
      <div
        className="fixed z-50 cursor-move"
        style={{ left: position.x, top: position.y }}
        onMouseDown={handleMouseDown}
      >
        <button
          onClick={onToggleExpanded}
          className="bg-black/20 backdrop-blur-sm border border-white/20 rounded-lg px-3 py-2 text-white/80 hover:bg-black/30 transition-all duration-300"
        >
          <div className="flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span className="text-sm">Control Centre</span>
          </div>
        </button>
      </div>
    )
  }

  return (
    <div
      ref={controlCentreRef}
      className="fixed z-50 bg-black/80 backdrop-blur-md border border-white/20 rounded-xl w-96 max-h-[80vh] overflow-y-auto"
      style={{ left: position.x, top: position.y }}
    >
      {/* Header with drag handle */}
      <div
        className="flex items-center justify-between p-6 pb-4 cursor-move"
        onMouseDown={handleMouseDown}
      >
        <h2 className="text-lg font-semibold text-white flex items-center space-x-2">
          <Move className="w-4 h-4 text-white/60" />
          <Settings className="w-5 h-5" />
          <span>Control Centre</span>
        </h2>
        <button
          onClick={onToggleExpanded}
          className="text-white/60 hover:text-white transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      <div className="px-6 pb-6">

      {/* User Preferences */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-white/80 mb-3">User Preferences</h3>
        <div className="space-y-3">
          {/* User Name Input */}
          <div>
            <label className="text-xs text-white/60 mb-1 block">What should I call you?</label>
            <input
              type="text"
              value={userName}
              onChange={(e) => onUserNameChange(e.target.value)}
              placeholder="Enter your preferred name..."
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400"
            />
          </div>

          {/* OpenAI Realtime API Info */}
          <div>
            <label className="text-xs text-white/60 mb-2 block">Voice System: OpenAI Realtime API</label>
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-4">
              <p className="text-blue-300 text-sm">
                ✅ No wake words needed! Just start talking and the AI will respond in real-time.
              </p>
            </div>
          </div>

          {/* Wake Word Management (Hidden) */}
          <div className="hidden">
            <label className="text-xs text-white/60 mb-2 block">Wake Word Management (Disabled)</label>

            {/* Add New Wake Word */}
            <div className="flex space-x-2 mb-3">
              <input
                type="text"
                value={newWakeWord}
                onChange={(e) => setNewWakeWord(e.target.value)}
                placeholder="Enter new wake word..."
                className="flex-1 px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/50 text-sm focus:outline-none focus:border-blue-400"
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && newWakeWord.trim() && onAddWakeWord) {
                    onAddWakeWord(newWakeWord.trim())
                    setNewWakeWord('')
                  }
                }}
              />
              <button
                onClick={() => {
                  if (newWakeWord.trim() && onAddWakeWord) {
                    onAddWakeWord(newWakeWord.trim())
                    setNewWakeWord('')
                  }
                }}
                disabled={!newWakeWord.trim()}
                className="px-3 py-2 bg-blue-500/20 border border-blue-500/30 text-blue-400 rounded-lg hover:bg-blue-500/30 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
              >
                Add
              </button>
            </div>

            {/* Wake Words List */}
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {wakeWords.map((wakeWord) => (
                <div key={wakeWord.id} className="flex items-center justify-between p-2 bg-white/5 rounded-lg">
                  <div className="flex items-center space-x-2 flex-1">
                    <button
                      onClick={() => onToggleWakeWord?.(wakeWord.id)}
                      className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                        wakeWord.enabled
                          ? 'bg-green-500 border-green-500'
                          : 'border-white/30'
                      }`}
                    >
                      {wakeWord.enabled && <span className="text-white text-xs">✓</span>}
                    </button>
                    <span className={`text-sm ${wakeWord.enabled ? 'text-white' : 'text-white/60'}`}>
                      {wakeWord.keyword}
                    </span>
                  </div>
                  <button
                    onClick={() => onDeleteWakeWord?.(wakeWord.id)}
                    className="text-red-400 hover:text-red-300 p-1"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
              ))}
              {wakeWords.length === 0 && (
                <div className="text-center text-white/60 text-sm py-2">
                  No wake words configured
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* System Control */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-white/80 mb-3">System Control</h3>
        <button
          onClick={onToggleSystemActive}
          className={`w-full flex items-center justify-center space-x-2 p-4 rounded-lg transition-all font-medium ${
            systemActive
              ? 'bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30'
              : 'bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30'
          }`}
        >
          <div className={`w-3 h-3 rounded-full ${systemActive ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`} />
          <span>{systemActive ? 'System Active - Click to Pause' : 'System Paused - Click to Resume'}</span>
        </button>
        <p className="text-xs text-white/60 mt-2 text-center">
          {systemActive ? 'AI is listening and ready to respond' : 'AI is paused - no processing or responses'}
        </p>
      </div>

      {/* Chat Controls */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-white/80 mb-3">Chat Controls</h3>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={onToggleMute}
            className={`flex items-center justify-center space-x-2 p-3 rounded-lg transition-all ${
              isMuted 
                ? 'bg-red-500/20 border border-red-500/30 text-red-400' 
                : 'bg-purple-500/20 border border-purple-500/30 text-purple-400'
            }`}
          >
            {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            <span className="text-sm">{isMuted ? 'Unmute' : 'Mute'}</span>
          </button>
          
          <button
            onClick={onRefreshChat}
            className="flex items-center justify-center space-x-2 p-3 rounded-lg bg-blue-500/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30 transition-all"
          >
            <RotateCcw className="w-4 h-4" />
            <span className="text-sm">Refresh</span>
          </button>
          
          <button
            onClick={onToggleChatExpanded}
            className="flex items-center justify-center space-x-2 p-3 rounded-lg bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30 transition-all col-span-2"
          >
            {chatExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            <span className="text-sm">{chatExpanded ? 'Normal Size' : 'Expand Chat'}</span>
          </button>
        </div>
      </div>

      {/* Wake word management removed for simplified UI */}



      {/* Instructions */}
      <div className="text-xs text-white/50 leading-relaxed">
        <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span className="text-blue-300 font-medium text-xs">OpenWakeWord System</span>
          </div>
          <p className="text-xs text-white/70">
            Now using advanced AI-powered wake word detection with no API keys required.
          </p>
        </div>

        <div className="space-y-2">
          <p>
            <strong className="text-white/70">Wake Words:</strong> Say any enabled wake word to start voice recording.
          </p>
          <p>
            <strong className="text-white/70">Auto-Stop:</strong> Recording stops automatically after 1.5 seconds of silence.
          </p>
          <p>
            <strong className="text-white/70">Sensitivity:</strong> Higher values detect wake words more easily but may increase false positives.
          </p>
          <p>
            <strong className="text-white/70">Available Models:</strong> "hey jarvis", "alexa", "hey mycroft", "hey rhasspy"
          </p>
        </div>
      </div>
      </div>
    </div>
  )
}

export default ControlCentre
