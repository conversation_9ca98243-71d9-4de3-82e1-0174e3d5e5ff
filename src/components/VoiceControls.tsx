import React from 'react'
import { Volume2, VolumeX, Loader2, Mic } from 'lucide-react'

interface VoiceControlsProps {
  isRecording: boolean
  isPlaying: boolean
  isMuted: boolean
  isProcessing?: boolean
  processingStatus?: string
  systemActive?: boolean
  onToggleMute: () => void
  onStartRecording?: () => void
}

const VoiceControls: React.FC<VoiceControlsProps> = ({
  isRecording,
  isPlaying,
  isMuted,
  isProcessing = false,
  systemActive = true,
  onToggleMute
}) => {
  return (
    <div className="flex flex-col items-center space-y-4">
      {/* Main Controls Row */}
      <div className="flex items-center justify-center space-x-6">
        {/* Status Indicator */}
        <div className="flex flex-col items-center">
          <div className={`w-8 h-8 rounded-full mb-2 flex items-center justify-center ${
            isProcessing ? 'bg-blue-400' :
            isRecording ? 'bg-green-400 animate-pulse' :
            isPlaying ? 'bg-purple-400 animate-pulse' :
            'bg-gray-400'
          }`}>
            {isProcessing && (
              <Loader2 className="w-4 h-4 text-white animate-spin" />
            )}
          </div>
          <span className="text-sm text-white/90 font-medium text-center">
            {isProcessing ? 'Thinking...' :
             isRecording ? 'Listening...' :
             isPlaying ? 'Speaking...' :
             'Ready'}
          </span>
        </div>

        {/* Mute Button */}
        <div className="flex flex-col items-center">
          <button
            onClick={onToggleMute}
            className={`p-4 rounded-full transition-all duration-300 ${
              isMuted
                ? 'bg-gray-600 hover:bg-gray-700'
                : 'bg-purple-500 hover:bg-purple-600 shadow-lg shadow-purple-500/30'
            }`}
          >
            {isMuted ? (
              <VolumeX className="w-6 h-6 text-white" />
            ) : (
              <Volume2 className="w-6 h-6 text-white" />
            )}
          </button>
          <span className="text-xs text-white/70 font-medium mt-2">
            {isMuted ? 'Unmute' : 'Mute'}
          </span>
        </div>
      </div>

      {/* System Status */}
      {!systemActive ? (
        <div className="bg-red-500/20 border border-red-500/30 rounded-lg px-4 py-2 backdrop-blur-sm">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-red-400 rounded-full" />
            <span className="text-sm text-red-400 font-medium">
              System Paused
            </span>
          </div>
        </div>
      ) : isProcessing ? (
        <div className="bg-black/20 rounded-lg px-4 py-2 backdrop-blur-sm">
          <div className="flex items-center space-x-2">
            <Loader2 className="w-4 h-4 text-blue-400 animate-spin" />
            <span className="text-sm text-white/90">
              Thinking...
            </span>
          </div>
        </div>
      ) : null}

      {/* Instructions */}
      <div className="text-center">
        <p className="text-sm text-white/70 max-w-md">
          {!systemActive
            ? 'System is paused - use Control Centre to resume'
            : 'Voice-to-voice conversation active. Say your wake word to start talking!'
          }
        </p>
      </div>
    </div>
  )
}

export default VoiceControls
