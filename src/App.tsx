import React, { useState, useEffect, useRef } from 'react'
import { io, Socket } from 'socket.io-client'

import AvatarDisplay from './components/AvatarDisplay'
import ChatHistory from './components/ChatHistory'
import VoiceControls from './components/VoiceControls'
import ControlCentre from './components/ControlCentre'
// import { AutoSilenceService } from './services/autoSilenceService'
import { DialogFlowService } from './services/dialogflowService'
import './App.css'

interface Message {
  id: number
  content: string
  message_type: 'user' | 'assistant' | 'system'
  created_at: string
  audio_url?: string
  metadata?: any
}

interface WakeWord {
  id: string
  keyword: string
  enabled: boolean
  sensitivity: number
}

interface Conversation {
  id: number
  title: string
  messages: Message[]
}

const App: React.FC = () => {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [conversation, setConversation] = useState<Conversation | null>(null)
  const [messages, setMessages] = useState<Message[]>([])

  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  // const [processingStatus, setProcessingStatus] = useState('')
  const [avatarState, setAvatarState] = useState<'idle' | 'listening' | 'speaking' | 'thinking'>('idle')
  const [isLoading, setIsLoading] = useState(false)

  // User preferences and system state
  const [userName, setUserName] = useState('')
  const [preferredWakeWord, setPreferredWakeWord] = useState('hey assistant')
  const [systemActive, setSystemActive] = useState(true) // Stop/Start processing button
  const [wakeWords, setWakeWords] = useState<WakeWord[]>([])
  const [isWakeWordListening, setIsWakeWordListening] = useState(false)
  const [controlCentreExpanded, setControlCentreExpanded] = useState(false)
  const [chatExpanded, setChatExpanded] = useState(false)

  const audioRef = useRef<HTMLAudioElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  // const audioChunksRef = useRef<Blob[]>([])

  // const autoSilenceServiceRef = useRef<AutoSilenceService | null>(null)
  const dialogFlowServiceRef = useRef<DialogFlowService | null>(null)

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io('http://localhost:5002', {
      transports: ['websocket']
    })

    newSocket.on('connect', () => {
      console.log('Connected to server')
      setIsConnected(true)

      // Start OpenAI Realtime conversation
      newSocket.emit('start_realtime_conversation', {
        conversation_id: null // Create new conversation
      })
    })

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server')
      setIsConnected(false)
    })

    newSocket.on('message_received', (data: { message: Message, type: string }) => {
      setMessages(prev => [...prev, data.message])
      if (data.type === 'assistant') {
        setAvatarState('speaking')
        // setProcessingStatus('Generating voice response...')
      } else if (data.type === 'user') {
        // setProcessingStatus('Getting AI response...')
      }
    })

    newSocket.on('voice_response', (data: { audio_data: string, message_id: number, format: string }) => {
      // Clear processing state
      setIsProcessing(false)
      // setProcessingStatus('')

      if (!isMuted && audioRef.current) {
        const audioBlob = new Blob([Uint8Array.from(atob(data.audio_data), c => c.charCodeAt(0))], { type: 'audio/mpeg' })
        const audioUrl = URL.createObjectURL(audioBlob)
        audioRef.current.src = audioUrl
        audioRef.current.play()
        setIsPlaying(true)
      }
    })

    // OpenAI Realtime API event handlers
    newSocket.on('realtime_connected', (data: { status: string, conversation_id: string }) => {
      console.log('✅ OpenAI Realtime API connected:', data)
      setIsConnected(true)
    })

    newSocket.on('ai_greeting', (data: { message: string, conversation_id: string }) => {
      console.log('🎤 AI Greeting:', data.message)
      // Add greeting message to chat
      const greetingMessage: Message = {
        id: Date.now(),
        content: data.message,
        role: 'assistant',
        timestamp: new Date().toISOString(),
        conversation_id: data.conversation_id
      }
      setMessages(prev => [...prev, greetingMessage])
      setAvatarState('speaking')
    })

    newSocket.on('transcript_received', (data: { transcript: string, conversation_id: string, message_id: number }) => {
      console.log('📝 Transcript received:', data.transcript)
      // Add user message to chat
      const userMessage: Message = {
        id: data.message_id,
        content: data.transcript,
        role: 'user',
        timestamp: new Date().toISOString(),
        conversation_id: data.conversation_id
      }
      setMessages(prev => [...prev, userMessage])
    })

    newSocket.on('response_complete', (data: { conversation_id: string }) => {
      console.log('✅ Response completed')
      setIsProcessing(false)
      setAvatarState('idle')
    })

    // Handle voice transcription and continue conversation flow
    newSocket.on('voice_transcribed', (data: { conversation_id: number, text: string }) => {
      console.log('🎯 Voice transcribed:', data.text)
    })

    newSocket.on('command_executed', (data: any) => {
      console.log('Command executed:', data)
    })

    newSocket.on('error', (error: any) => {
      console.error('Socket error:', error)
    })

    setSocket(newSocket)

    return () => {
      newSocket.close()
    }
  }, [])

  // Create initial conversation
  useEffect(() => {
    if (socket && isConnected) {
      createConversation()
    }
  }, [socket, isConnected])

  // Initialize wake word service with backend
  useEffect(() => {
    const initializeWakeWordService = async () => {
      if (!socket || !isConnected) return

      try {
        console.log('🎧 Initializing wake word detection...')

        // Load saved wake words from localStorage or use defaults
        const savedWakeWords = localStorage.getItem('wakeWords')
        const defaultWakeWords = [
          {
            id: 'hey-chat',
            keyword: 'hey chat',
            enabled: true,
            sensitivity: 0.5
          },
          {
            id: 'hey-babe',
            keyword: 'hey babe',
            enabled: false,
            sensitivity: 0.5
          },
          {
            id: 'hey-cal',
            keyword: 'hey cal',
            enabled: false,
            sensitivity: 0.5
          }
        ]

        const wakeWordsToUse = savedWakeWords ? JSON.parse(savedWakeWords) : defaultWakeWords
        setWakeWords(wakeWordsToUse)

        // Save to localStorage if using defaults
        if (!savedWakeWords) {
          localStorage.setItem('wakeWords', JSON.stringify(defaultWakeWords))
        }

        // Set up wake word detection callback
        socket.on('wake_word_detected', (detection: any) => {
          console.log('🎯 Wake word detected:', detection.keyword, 'confidence:', detection.confidence)
          setAvatarState('listening')
        })

        // Handle automatic voice recording start
        socket.on('start_voice_recording', (data: any) => {
          console.log('🎤 Starting voice recording triggered by:', data.triggered_by)
          if (!isRecording && systemActive) {
            startVoiceStreaming()
          }
        })

        // Start wake word detection on backend
        const enabledWakeWords = wakeWordsToUse.filter((w: WakeWord) => w.enabled)
        if (enabledWakeWords.length > 0) {
          const wakeWordNames = enabledWakeWords.map((w: WakeWord) => w.keyword)
          socket.emit('start_wake_word_detection', {
            wake_words: wakeWordNames,
            threshold: 0.3
          })
          setIsWakeWordListening(true)
          console.log('✅ Wake word listening is now ACTIVE for:', wakeWordNames)

          // Start continuous audio streaming for wake word detection
          startContinuousAudioStream()

          // Have the chatbot introduce itself with a short greeting
          setTimeout(() => {
            if (!isMuted && audioRef.current) {
              const greeting = "Hi! I'm your chat bot. Just say hey chat to get my attention."

              // Send greeting to backend for voice synthesis
              socket.emit('synthesize_greeting', {
                text: greeting,
                conversation_id: conversation?.id
              })
            }
          }, 2000)

          // Have the chatbot introduce itself
          setTimeout(() => {
            if (!isMuted && audioRef.current) {
              const greeting = `Hi! I'm your chat bot. Whenever you want to get my attention, just say my name like "${wakeWordNames[0]}".`

              // Send greeting to backend for voice synthesis
              socket.emit('synthesize_greeting', {
                text: greeting,
                conversation_id: conversation?.id
              })
            }
          }, 2000) // Wait 2 seconds for everything to initialize
        }

      } catch (error) {
        console.error('Failed to initialize wake word service:', error)
        setIsWakeWordListening(false)
      }
    }

    initializeWakeWordService()
  }, [socket, isConnected])

  // Initialize DialogFlow service
  useEffect(() => {
    const initializeDialogFlow = () => {
      try {
        const projectId = process.env.REACT_APP_GOOGLE_CLOUD_PROJECT_ID

        if (projectId) {
          const dialogFlowService = new DialogFlowService({
            projectId: projectId,
            sessionId: DialogFlowService.generateSessionId(),
            languageCode: 'en-US'
          })

          dialogFlowServiceRef.current = dialogFlowService
          console.log('DialogFlow service initialized')
        } else {
          console.log('DialogFlow not configured - missing project ID')
        }
      } catch (error) {
        console.log('DialogFlow initialization failed:', error)
      }
    }

    initializeDialogFlow()
  }, [])

  const createConversation = async () => {
    if (conversation) return // Don't create if one already exists
    try {
      const response = await fetch('http://localhost:5002/api/chat/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: 'AI Voice Chat',
          user_id: 1
        })
      })
      
      const data = await response.json()
      if (data.success) {
        setConversation(data.conversation)
        // Join the conversation room
        socket?.emit('join_conversation', {
          conversation_id: data.conversation.id,
          user_id: 1
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
    }
  }

  const sendMessage = async (message: string, generateVoice = false) => {
    if (!socket || !conversation || !message.trim()) return

    // Check if system is active
    if (!systemActive) {
      console.log('System is paused - message sending disabled')
      return
    }

    setIsLoading(true)
    setAvatarState('thinking')

    // Try DialogFlow first for quick responses
    let dialogFlowResponse = null
    if (dialogFlowServiceRef.current) {
      try {
        const canHandleQuickly = await dialogFlowServiceRef.current.canHandleQuickly(message)
        if (canHandleQuickly) {
          dialogFlowResponse = await dialogFlowServiceRef.current.detectIntent(message)

          if (dialogFlowResponse && dialogFlowResponse.fulfillmentText) {
            // Add user message
            const userMessage: Message = {
              id: Date.now(),
              content: message,
              message_type: 'user',
              created_at: new Date().toISOString()
            }
            setMessages(prev => [...prev, userMessage])

            // Add DialogFlow response
            const assistantMessage: Message = {
              id: Date.now() + 1,
              content: dialogFlowResponse.fulfillmentText,
              message_type: 'assistant',
              created_at: new Date().toISOString()
            }
            setMessages(prev => [...prev, assistantMessage])

            // Generate voice for DialogFlow response if requested
            if (generateVoice && !isMuted) {
              socket.emit('generate_voice_only', {
                text: dialogFlowResponse.fulfillmentText,
                conversation_id: conversation.id
              })
            }

            setIsLoading(false)
            setAvatarState('idle')

            return
          }
        }
      } catch (error) {
        console.log('DialogFlow not available, falling back to AI:', error)
      }
    }

    // Fall back to regular AI processing
    socket.emit('send_message', {
      conversation_id: conversation.id,
      content: message,
      message_type: 'user',
      generate_voice: generateVoice,
      dialogflow_context: dialogFlowResponse ? {
        intent: dialogFlowResponse.intent,
        parameters: dialogFlowResponse.parameters,
        contexts: dialogFlowResponse.contexts
      } : null
    })


    setIsLoading(false)
  }



  // Continuous audio streaming for wake word detection
  const startContinuousAudioStream = async () => {
    if (!socket) return

    try {
      console.log('🎧 Starting continuous audio stream for wake word detection...')

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // Create audio context for real-time processing
      const audioContext = new AudioContext({ sampleRate: 16000 })
      const source = audioContext.createMediaStreamSource(stream)
      const processor = audioContext.createScriptProcessor(4096, 1, 1)

      processor.onaudioprocess = (event) => {
        if (!systemActive) return

        const inputBuffer = event.inputBuffer
        const inputData = inputBuffer.getChannelData(0)

        // Convert to PCM format for wake word detection
        const int16Array = new Int16Array(inputData.length)
        for (let i = 0; i < inputData.length; i++) {
          const clampedValue = Math.max(-1, Math.min(1, inputData[i]))
          int16Array[i] = clampedValue * 0x7FFF
        }

        // Send to backend for wake word detection
        const uint8Array = new Uint8Array(int16Array.buffer)
        const base64Audio = btoa(String.fromCharCode(...uint8Array))

        socket.emit('wake_word_audio_stream', {
          audio_data: base64Audio,
          sample_rate: 16000,
          channels: 1,
          format: 'pcm_s16le'
        })
      }

      source.connect(processor)
      processor.connect(audioContext.destination)

      console.log('✅ Continuous audio stream active for wake word detection')
    } catch (error) {
      console.error('Error starting continuous audio stream:', error)
    }
  }

  const startVoiceStreaming = async () => {
    if (!systemActive || !socket || !conversation) {
      console.log('System not ready for voice streaming')
      return
    }

    try {
      console.log('🎤 Starting voice recording for conversation...')

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // Use MediaRecorder for simple voice recording
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      })

      const audioChunks: Blob[] = []

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data)
        }
      }

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })
        const reader = new FileReader()

        reader.onloadend = () => {
          const base64Audio = (reader.result as string).split(',')[1]

          // Send audio for transcription and processing
          socket.emit('voice_stream', {
            conversation_id: conversation.id,
            audio_data: base64Audio
          })
        }

        reader.readAsDataURL(audioBlob)

        // Stop the stream
        stream.getTracks().forEach(track => track.stop())
        setIsRecording(false)
        setAvatarState('thinking')
      }

      mediaRecorderRef.current = mediaRecorder
      mediaRecorder.start()

      setIsRecording(true)
      setAvatarState('listening')

      // Auto-stop recording after 10 seconds
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop()
        }
      }, 10000)

      console.log('✅ Voice recording started')
    } catch (error) {
      console.error('Error starting voice recording:', error)
    }
  }

  const stopVoiceStreaming = () => {
    if (isRecording) {
      console.log('🛑 Stopping voice streaming...')

      // Stop streaming
      if (socket && conversation) {
        socket.emit('stop_voice_streaming', {
          conversation_id: conversation.id
        })
      }

      setIsRecording(false)
      setAvatarState('idle')
      console.log('✅ Voice streaming stopped')
    }
  }

  const executeCommand = (command: string) => {
    if (!socket || !conversation) return

    // Send as a regular message with voice generation enabled
    sendMessage(command, true)
  }

  const handleAudioEnded = () => {
    setIsPlaying(false)
    setAvatarState('idle')

    // Automatically restart listening for continuous conversation
    if (systemActive && isWakeWordListening) {
      console.log('🔄 Voice response finished, ready for next wake word...')
      // The wake word detection is already running, so just wait for next wake word
    }
  }

  const handleToggleMute = () => {
    const newMutedState = !isMuted
    setIsMuted(newMutedState)

    // If muting and audio is currently playing, stop it immediately
    if (newMutedState && isPlaying && audioRef.current) {
      audioRef.current.pause()
      audioRef.current.currentTime = 0
      setIsPlaying(false)
      setAvatarState('idle')
      console.log('🔇 Assistant muted and stopped speaking')
    } else if (!newMutedState) {
      console.log('🔊 Assistant unmuted')
    }
  }

  // Wake word handlers (RE-ENABLED WITH PERSISTENCE AND SERVICE INTEGRATION)
  const saveWakeWordsToStorage = (newWakeWords: WakeWord[]) => {
    localStorage.setItem('wakeWords', JSON.stringify(newWakeWords))
    console.log('Wake words saved to localStorage:', newWakeWords)
  }

  const restartWakeWordService = async (updatedWakeWords: WakeWord[]) => {
    if (!socket) return

    try {
      // Stop current wake word detection
      socket.emit('stop_wake_word_detection')

      // Start with new wake words
      const enabledWakeWords = updatedWakeWords.filter((w: WakeWord) => w.enabled)
      if (enabledWakeWords.length > 0) {
        const wakeWordNames = enabledWakeWords.map((w: WakeWord) => w.keyword)
        socket.emit('start_wake_word_detection', {
          wake_words: wakeWordNames,
          threshold: 0.3
        })
        console.log('Wake word service restarted with:', wakeWordNames)
      }
    } catch (error) {
      console.error('Failed to restart wake word service:', error)
    }
  }

  const handleAddWakeWord = async (keyword: string) => {
    const newWakeWord = {
      id: `wake-word-${Date.now()}`,
      keyword: keyword.toLowerCase(),
      enabled: false,
      sensitivity: 0.5
    }
    const updatedWakeWords = [...wakeWords, newWakeWord]
    setWakeWords(updatedWakeWords)
    saveWakeWordsToStorage(updatedWakeWords)
    await restartWakeWordService(updatedWakeWords)
    console.log('Added wake word:', keyword)
  }

  const handleDeleteWakeWord = async (id: string) => {
    const updatedWakeWords = wakeWords.filter(w => w.id !== id)
    setWakeWords(updatedWakeWords)
    saveWakeWordsToStorage(updatedWakeWords)
    await restartWakeWordService(updatedWakeWords)
    console.log('Deleted wake word:', id)
  }

  const handleToggleWakeWord = async (id: string) => {
    const updatedWakeWords = wakeWords.map(w =>
      w.id === id ? { ...w, enabled: !w.enabled } : w
    )
    setWakeWords(updatedWakeWords)
    saveWakeWordsToStorage(updatedWakeWords)
    await restartWakeWordService(updatedWakeWords)
    console.log('Toggled wake word:', id)
  }

  // const handleUpdateSensitivity = (id: string, sensitivity: number) => {
  //   setWakeWords(prev => prev.map(w =>
  //     w.id === id ? { ...w, sensitivity: Math.max(0.1, Math.min(1.0, sensitivity)) } : w
  //   ))
  //   console.log('Updated sensitivity for wake word:', id, sensitivity)
  // }

  // const handleTestWakeWord = (id: string) => {
  //   const wakeWord = wakeWords.find(w => w.id === id)
  //   if (wakeWord) {
  //     console.log('Testing wake word:', wakeWord.keyword)
  //     // Could trigger a test detection here
  //   }
  // }

  const handleRefreshChat = () => {
    setMessages([])
    setConversation(null)
    createConversation()
  }

  // New user preference handlers
  const handleUserNameChange = (name: string) => {
    setUserName(name)
    // Save to localStorage for persistence
    localStorage.setItem('userName', name)
  }

  const handleWakeWordChange = (wakeWord: string) => {
    setPreferredWakeWord(wakeWord)
    // Save to localStorage for persistence
    localStorage.setItem('preferredWakeWord', wakeWord)
  }

  const handleToggleSystemActive = () => {
    const newActiveState = !systemActive
    setSystemActive(newActiveState)

    if (newActiveState) {
      console.log('✅ System reactivated - AI ready to respond')
    } else {
      console.log('⏸️ System paused - stopping all AI operations')

      // Stop current voice streaming if active
      if (isRecording) {
        stopVoiceStreaming()
      }

      // Stop current audio playback if active
      if (isPlaying && audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
        setIsPlaying(false)
        setAvatarState('idle')
      }

      // Clear any processing state
      setIsProcessing(false)
      // setProcessingStatus('')
    }
  }

  // Load user preferences from localStorage on mount
  useEffect(() => {
    const savedUserName = localStorage.getItem('userName')
    const savedWakeWord = localStorage.getItem('preferredWakeWord')

    if (savedUserName) setUserName(savedUserName)
    if (savedWakeWord) setPreferredWakeWord(savedWakeWord)
  }, [])

  return (
    <div className="min-h-screen gradient-bg flex p-6 gap-6">
      {/* Left Panel - Avatar Display */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="floating-panel w-full max-w-2xl mb-8">
          <div className="glass-card">
            <div className="text-center mb-6">
              <h1 className="text-2xl font-bold text-white/90 mb-2">AI Assistant</h1>
              <p className="text-white/70 text-sm">Click the microphone to start talking</p>
            </div>
            
            <div className="flex items-center justify-center h-80 avatar-glow">
              <AvatarDisplay 
                state={avatarState}
                isConnected={isConnected}
                isPlaying={isPlaying}
              />
            </div>
          </div>
        </div>

        {/* Voice Controls */}
        <div className="glass-panel rounded-3xl p-6 mb-6">
          <VoiceControls
            isRecording={isRecording}
            isPlaying={isPlaying}
            isMuted={isMuted}
            onToggleMute={handleToggleMute}
            isProcessing={isProcessing}
            systemActive={systemActive}
          />

          {/* Wake Word Status */}
          {isWakeWordListening && (
            <div className="mt-4 text-center">
              <div className="inline-flex items-center space-x-2 bg-green-500/20 border border-green-500/30 rounded-lg px-3 py-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                <span className="text-green-400 text-sm font-medium">
                  Wake word detection active
                </span>
              </div>
              {/* Wake word detection history temporarily disabled for performance */}
            </div>
          )}
        </div>

        {/* Connection Status */}
        <div className={`connection-indicator ${
          isConnected ? 'status-connected' : 'status-disconnected'
        }`}>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              isConnected ? 'bg-green-400 animate-pulse' : 'bg-red-400'
            }`} />
            <span className="text-sm font-medium">
              {isConnected ? 'Connected' : 'Reconnecting...'}
            </span>
          </div>
        </div>
      </div>

      {/* Right Panel - Chat Interface */}
      <div className={`flex flex-col h-screen transition-all duration-300 ${
        chatExpanded ? 'w-[600px]' : 'w-96'
      }`}>
        <div className="glass-panel rounded-3xl flex flex-col h-full max-h-screen overflow-hidden">
          {/* Chat Header */}
          <div className="p-6 border-b border-white/10 flex-shrink-0">
            <h2 className="text-xl font-semibold text-white/90">Our Conversation</h2>
            <p className="text-white/60 text-sm mt-1">I'm here to listen and support you</p>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 overflow-hidden min-h-0">
            <ChatHistory
              messages={messages}
              isLoading={isLoading}
            />
          </div>

          {/* Voice-Only Interaction Notice */}
          <div className="p-6 border-t border-white/10 flex-shrink-0">
            <div className="text-center mb-4">
              <p className="text-white/70 text-sm">
                🎤 Voice-only conversation mode
              </p>
              <p className="text-white/50 text-xs mt-1">
                Use your wake word to start talking
              </p>
            </div>
            
            {/* Quick Support Options */}
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => executeCommand("I need some encouragement today")}
                disabled={isLoading}
                className="glass-button-secondary text-xs py-2 px-3 disabled:opacity-50"
              >
                Need Encouragement
              </button>
              <button
                onClick={() => executeCommand("I'm feeling stressed and need support")}
                disabled={isLoading}
                className="glass-button-secondary text-xs py-2 px-3 disabled:opacity-50"
              >
                Feeling Stressed
              </button>
              <button
                onClick={() => executeCommand("Tell me something positive")}
                disabled={isLoading}
                className="glass-button-secondary text-xs py-2 px-3 disabled:opacity-50"
              >
                Something Positive
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Control Centre */}
      <ControlCentre
        isExpanded={controlCentreExpanded}
        onToggleExpanded={() => setControlCentreExpanded(!controlCentreExpanded)}
        isMuted={isMuted}
        onToggleMute={handleToggleMute}
        onRefreshChat={handleRefreshChat}
        chatExpanded={chatExpanded}
        onToggleChatExpanded={() => setChatExpanded(!chatExpanded)}
        userName={userName}
        onUserNameChange={handleUserNameChange}
        preferredWakeWord={preferredWakeWord}
        onWakeWordChange={handleWakeWordChange}
        systemActive={systemActive}
        onToggleSystemActive={handleToggleSystemActive}
        wakeWords={wakeWords}
        onAddWakeWord={handleAddWakeWord}
        onDeleteWakeWord={handleDeleteWakeWord}
        onToggleWakeWord={handleToggleWakeWord}
      />

      {/* Hidden audio element for playing responses */}
      <audio
        ref={audioRef}
        onEnded={handleAudioEnded}
        style={{ display: 'none' }}
      />
    </div>
  )
}

export default App
